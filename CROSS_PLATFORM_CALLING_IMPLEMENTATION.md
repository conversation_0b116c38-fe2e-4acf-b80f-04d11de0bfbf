# Cross-Platform Calling Implementation

## Overview

This document outlines the complete implementation of bidirectional cross-platform calling between web and mobile platforms using Agora SDK and FCM notifications.

## Implementation Summary

### 1. Enhanced Call Hooks

#### useCall.js (Audio Calls)
- **Cross-Platform Detection**: Automatically detects when calling mobile users
- **FCM Integration**: Handles incoming call notifications from mobile
- **Response Handling**: Processes call acceptance, rejection, and cancellation from mobile
- **Bidirectional Support**: Supports both web-to-mobile and mobile-to-web audio calls

#### useVideoCall.js (Video Calls)
- **Cross-Platform Detection**: Automatically detects when calling mobile users
- **FCM Integration**: Handles incoming video call notifications from mobile
- **Response Handling**: Processes call acceptance, rejection, and cancellation from mobile
- **Bidirectional Support**: Supports both web-to-mobile and mobile-to-web video calls

### 2. New Services

#### CrossPlatformCallResponseService.js
- **Response Management**: Centralized handling of call responses between platforms
- **FCM Communication**: Sends call responses (accept/reject/busy/cancel) via FCM
- **Handler Registration**: Manages response handlers for outgoing calls
- **Call State Tracking**: Tracks pending calls and their states

#### Enhanced FCMService.js
- **Message Handling**: Improved handling of different call notification types
- **Notification Display**: Enhanced call notifications with caller information
- **Action Support**: Support for notification actions (answer/decline)

### 3. Application Integration

#### App.js
- **Global FCM Initialization**: Centralized FCM service initialization
- **Response Routing**: Global handler for cross-platform call responses
- **Service Coordination**: Ensures proper service integration

## Key Features

### Bidirectional Calling
- ✅ Web user can call mobile user (audio/video)
- ✅ Mobile user can call web user (audio/video)
- ✅ Consistent call experience across platforms

### Call State Synchronization
- ✅ Real-time call acceptance/rejection between platforms
- ✅ Call cancellation from either side
- ✅ Proper cleanup and state management

### Platform Detection
- ✅ Automatic detection of mobile vs web users
- ✅ Seamless fallback to web-to-web calling when needed
- ✅ Cross-platform service routing

### Response Handling
- ✅ Accept/Reject/Busy/Cancel responses
- ✅ FCM-based communication for mobile responses
- ✅ RTM-based communication for web responses

## Testing Guide

### Prerequisites
1. **Two Devices**: One web browser and one mobile device with the Flutter app
2. **Network Connection**: Both devices should be on the same network or have internet access
3. **Permissions**: Ensure camera/microphone permissions are granted on both platforms
4. **FCM Setup**: Verify FCM tokens are properly generated and stored

### Test Scenarios

#### 1. Web-to-Mobile Audio Call
1. Open web application in browser
2. Navigate to CMT Connect page
3. Find a mobile user in the attendees list
4. Click the audio call button
5. **Expected**: Mobile device should receive FCM notification and show incoming call
6. **Test Accept**: Accept call on mobile - both platforms should connect
7. **Test Reject**: Reject call on mobile - web should show call rejected

#### 2. Web-to-Mobile Video Call
1. Open web application in browser
2. Navigate to CMT Connect page
3. Find a mobile user in the attendees list
4. Click the video call button
5. **Expected**: Mobile device should receive FCM notification and show incoming video call
6. **Test Accept**: Accept call on mobile - both platforms should connect with video
7. **Test Reject**: Reject call on mobile - web should show call rejected

#### 3. Mobile-to-Web Audio Call
1. Open Flutter app on mobile device
2. Navigate to attendees/contacts
3. Find a web user and initiate audio call
4. **Expected**: Web browser should show incoming call modal
5. **Test Accept**: Accept call on web - both platforms should connect
6. **Test Reject**: Reject call on web - mobile should show call rejected

#### 4. Mobile-to-Web Video Call
1. Open Flutter app on mobile device
2. Navigate to attendees/contacts
3. Find a web user and initiate video call
4. **Expected**: Web browser should show incoming video call modal
5. **Test Accept**: Accept call on web - both platforms should connect with video
6. **Test Reject**: Reject call on web - mobile should show call rejected

#### 5. Call Cancellation
1. Initiate call from either platform
2. Cancel call before the other party answers
3. **Expected**: Incoming call should disappear on recipient's device

#### 6. Call Controls
1. During active cross-platform call
2. Test mute/unmute on both platforms
3. Test video on/off (for video calls)
4. Test call end from either platform

### Debugging

#### Browser Console
- Check for FCM initialization messages
- Look for cross-platform call detection logs
- Monitor call response handling

#### Mobile Logs
- Check FCM notification reception
- Monitor Agora RTC connection status
- Verify call state synchronization

#### Common Issues
1. **FCM Token Missing**: Ensure FCM service is properly initialized
2. **RTC Token Issues**: Verify token generation and channel names
3. **Network Issues**: Check internet connectivity on both devices
4. **Permission Issues**: Ensure camera/microphone permissions are granted

## Architecture Benefits

### Maintainability
- Centralized response handling
- Clear separation of concerns
- Consistent error handling

### Scalability
- Service-based architecture
- Easy to extend for new platforms
- Modular design

### Reliability
- Fallback mechanisms
- Proper cleanup and error handling
- State synchronization

## Future Enhancements

1. **Call Quality Monitoring**: Add network quality indicators
2. **Call Recording**: Implement call recording for cross-platform calls
3. **Group Calling**: Extend to support multi-party cross-platform calls
4. **Push Notifications**: Enhanced push notification handling
5. **Call Analytics**: Track call success rates and quality metrics
