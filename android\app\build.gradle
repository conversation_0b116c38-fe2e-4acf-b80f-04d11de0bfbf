plugins {
    id "com.android.application"
    // START: FlutterFire Configuration
    id 'com.google.gms.google-services'
    // END: FlutterFire Configuration
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file("key.properties")
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    namespace = "com.cmtevents.cmtmeet"
    compileSdk 35
    ndkVersion = "27.0.12077973" //flutter.ndkVersion

    packagingOptions {
        pickFirst 'lib/x86/libaosl.so'
        pickFirst 'lib/x86_64/libaosl.so'
        pickFirst 'lib/armeabi-v7a/libaosl.so'
        pickFirst 'lib/arm64-v8a/libaosl.so'
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17
    }

    defaultConfig {
        vectorDrawables.useSupportLibrary = true
        applicationId = "com.cmtevents.cmtmeet"
        minSdk = 23 //flutter.minSdkVersion
        targetSdk = 35 //flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile file(keystoreProperties['storeFile'])
            storePassword keystoreProperties['storePassword']
        }
    }
    buildTypes {
        release {
            signingConfig signingConfigs.release
            shrinkResources false
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            crunchPngs false
            zipAlignEnabled true
        }
    }
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib:1.9.10"
    implementation 'io.agora.infra:aosl:1.2.13'
    implementation 'com.google.firebase:firebase-messaging:24.1.1'
}

flutter {
    source = "../.."
}

afterEvaluate {
    tasks.whenTaskAdded { task ->
        if (task.name.contains("minifyReleaseWithR8")) {
            task.doLast {
                def missingRules = file("$buildDir/outputs/mapping/release/missing_rules.txt")
                def proguardFile = file("$projectDir/proguard-rules.pro")
                if (missingRules.exists()) {
                    proguardFile.append(missingRules.text)
                    println("✅ Appended missing_rules.txt to proguard-rules.pro automatically.")
                }
            }
        }
    }
}

gradle.buildFinished {
    def rJarPath = file("$buildDir/intermediates/compile_and_runtime_not_namespaced_r_class_jar")
    if (rJarPath.exists()) {
        rJarPath.deleteDir()
        println("🧹 Cleaned R.jar intermediates after build.")
    }
}
