-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugins.** { *; }
-keep class io.flutter.embedding.engine.FlutterEngine { *; }
-keep class io.flutter.embedding.android.FlutterActivity { *; }

-keep class com.google.firebase.** { *; }
-dontwarn com.google.firebase.messaging.FirebaseMessagingService

# --- Window Manager (foldables support) ---
-keep class androidx.window.extensions.** { *; }
-keep class androidx.window.sidecar.** { *; }

# --- Play Core Split Install (Dynamic Features) ---
-keep class com.google.android.play.core.splitcompat.SplitCompatApplication { *; }
-keep class com.google.android.play.core.splitinstall.** { *; }
-keep class com.google.android.play.core.tasks.** { *; }

# --- Oppo Push ---
-keep class com.heytap.msp.push.** { *; }

# --- <PERSON><PERSON> ---
-keep class com.meizu.cloud.pushsdk.** { *; }

# --- Vivo Push ---
-keep class com.vivo.push.** { *; }

# --- <PERSON><PERSON> ---
-keep class com.xiaomi.mipush.sdk.** { *; }

# --- Prevent R8 from stripping important classes ---
-dontwarn androidx.window.**
-dontwarn com.google.android.play.core.**
-dontwarn com.heytap.msp.**
-dontwarn com.meizu.cloud.**
-dontwarn com.vivo.push.**
-dontwarn com.xiaomi.mipush.**

# --- Protect Agora classes and native methods ---
-keep class io.agora.** { *; }
-keep class io.agora.rtc.** { *; }
-keep class io.agora.rtm.** { *; }

# --- Protect all native libraries from being stripped ---
-keepclassmembers class * {
    native <methods>;
}
