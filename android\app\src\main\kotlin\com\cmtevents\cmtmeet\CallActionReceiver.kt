package com.cmtevents.cmtmeet

import android.app.NotificationManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import java.net.HttpURLConnection
import java.net.URL
import org.json.JSONObject

class CallActionReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent) {
        val callDataJson = intent.getStringExtra("call_data") ?: return
        val callData =
                JSONObject(callDataJson).let { json ->
                    json.keys().asSequence().associateWith { json.getString(it) }
                }

        val notificationManager =
                context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        when (intent.action) {
            "ACCEPT_CALL" -> {
                Log.d("NativeAndroid", "🔔 ACCEPT_CALL received")
                RingtonePlayer.stop()
                Log.d("NativeAndroid", "🔕 Ringtone stopped")

                notificationManager.cancel(1001)
                Log.d("NativeAndroid", "🗑️ Notification cancelled")

                FlutterMethodHandler.invokeWhenReady(
                        context.applicationContext,
                        "launch_call",
                        callData
                )
                Log.d("NativeAndroid", "📨 launch_call queued or invoked")

                // Launch or resume MainActivity only if Flutter isn't ready
                if (!FlutterMethodHandler.isFlutterReady) {
                    Log.d("NativeAndroid", "❄️ Flutter not ready — launching MainActivity")
                    launchMainActivityWithCall(context, callDataJson)
                } else {
                    Log.d("NativeAndroid", "✅ Flutter is ready — no need to relaunch MainActivity")
                }

                context.sendBroadcast(Intent("com.cmtevents.cmtmeet.CALL_ACCEPTED"))
            }
            "DECLINE_CALL" -> {
                RingtonePlayer.stop()
                notificationManager.cancel(1001)
                CallStateManager.isAppBusy = false
                sendDeclineStatusToFCM(
                        callData["callerName"] ?: "",
                        callData["callUUID"] ?: "",
                        callData["channelName"] ?: "",
                        callData["fcmToken"] ?: "",
                        callData["jwtToken"] ?: ""
                )
            }
            "BUSY_CALL" -> {
                RingtonePlayer.stop()
                notificationManager.cancel(1001)
                sendBusyStatusToFCM(
                        callData["callerName"] ?: "",
                        callData["callUUID"] ?: "",
                        callData["channelName"] ?: "",
                        callData["fcmToken"] ?: "",
                        callData["jwtToken"] ?: ""
                )
            }
        }
    }

    private fun launchMainActivityWithCall(context: Context, callDataJson: String) {
        Log.d("NativeAndroid", "🚀 Launching MainActivity with call_data")
        val i =
                Intent(context, MainActivity::class.java).apply {
                    putExtra("cold_call_data", callDataJson)
                    addFlags(
                            Intent.FLAG_ACTIVITY_NEW_TASK or
                                    Intent.FLAG_ACTIVITY_CLEAR_TASK or // ✅ Clear everything below
                                    Intent.FLAG_ACTIVITY_SINGLE_TOP or // ✅ Reuse if exists
                                    Intent.FLAG_ACTIVITY_CLEAR_TOP
                    )
                }
        context.startActivity(i)
    }

    private fun sendDeclineStatusToFCM(
            callerName: String,
            callUUID: String,
            channelName: String,
            fcmToken: String,
            jwtToken: String
    ) {
        sendCallStatusToFCM(callerName, callUUID, channelName, fcmToken, jwtToken, "DECLINED")
    }

    private fun sendBusyStatusToFCM(
            callerName: String,
            callUUID: String,
            channelName: String,
            fcmToken: String,
            jwtToken: String
    ) {
        sendCallStatusToFCM(callerName, callUUID, channelName, fcmToken, jwtToken, "BUSY")
    }

    private fun sendCallStatusToFCM(
            callerName: String,
            callUUID: String,
            channelName: String,
            fcmToken: String,
            jwtToken: String,
            status: String
    ) {
        Thread {
                    try {
                        val messageJson =
                                JSONObject().apply {
                                    put("token", fcmToken)
                                    put(
                                            "data",
                                            JSONObject().apply {
                                                put("callerName", callerName)
                                                put("callUUID", callUUID)
                                                put("channelName", channelName)
                                                put("status", status)
                                                put("type", "CALL_RESPONSE")
                                                put("click_action", "FLUTTER_NOTIFICATION_CLICK")
                                            }
                                    )
                                    put("android", JSONObject().apply { put("priority", "high") })
                                    put(
                                            "apns",
                                            JSONObject().apply {
                                                put(
                                                        "headers",
                                                        JSONObject().apply {
                                                            put("apns-priority", "10")
                                                        }
                                                )
                                                put(
                                                        "payload",
                                                        JSONObject().apply {
                                                            put(
                                                                    "aps",
                                                                    JSONObject().apply {
                                                                        put("sound", "default")
                                                                        put("badge", 1)
                                                                        put("content-available", 1)
                                                                    }
                                                            )
                                                            put("voip", "1")
                                                        }
                                                )
                                            }
                                    )
                                }

                        val bodyJson = JSONObject().put("message", messageJson)

                        val url =
                                URL(
                                        "https://fcm.googleapis.com/v1/projects/cmt-meet-fd921/messages:send"
                                )
                        val conn = url.openConnection() as HttpURLConnection
                        conn.requestMethod = "POST"
                        conn.setRequestProperty("Authorization", "Bearer $jwtToken")
                        conn.setRequestProperty("Content-Type", "application/json")
                        conn.doOutput = true

                        conn.outputStream.use { it.write(bodyJson.toString().toByteArray()) }

                        val responseCode = conn.responseCode
                        val responseText = conn.inputStream.bufferedReader().readText()
                        Log.d("CallStatusFCM", "✅ $status sent: $responseCode $responseText")
                    } catch (e: Exception) {
                        Log.e("CallStatusFCM", "❌ $status failed: ${e.message}", e)
                    }
                }
                .start()
    }
}
