package com.cmtevents.cmtmeet

import android.app.NotificationManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.util.Log
import android.view.View
import android.view.WindowManager
import android.widget.LinearLayout
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import org.json.JSONObject

class CallActivity : AppCompatActivity() {

    private val CHANNEL = "com.cmtevents.cmtmeet/call_service"
    private lateinit var handler: Handler
    private lateinit var callData: Map<String, String>
    private var wasCallAccepted = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setLockScreenFlags()
        setContentView(R.layout.activity_call)

        handler = Handler(mainLooper)
        callData = extractCallData(intent) ?: emptyMap()

        cancelNotification()
        setupUI()
    }

    private fun extractCallData(intent: Intent): Map<String, String>? {
        return try {
            val raw = intent.getStringExtra("call_data")
            if (raw != null) {
                val json = JSONObject(raw)
                json.keys().asSequence().associateWith { json.getString(it) }
            } else null
        } catch (e: Exception) {
            Log.e("NativeAndroid", "❌ Failed to parse call data: ${e.message}")
            null
        }
    }

    private fun setLockScreenFlags() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O_MR1) {
            setShowWhenLocked(true)
            setTurnScreenOn(true)
        } else {
            @Suppress("DEPRECATION")
            window.addFlags(
                    WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED or
                            WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD or
                            WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON or
                            WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON
            )
        }
    }

    private fun setupUI() {
        findViewById<TextView>(R.id.callerNameTextView).text = callData["callerName"] ?: "Unknown"
        findViewById<TextView>(R.id.userJobTitle).text = callData["callerJob"] ?: ""
        findViewById<TextView>(R.id.userCompany).text = callData["callerCompany"] ?: ""

        findViewById<LinearLayout>(R.id.acceptButton).setOnClickListener { acceptCall() }

        findViewById<LinearLayout>(R.id.declineButton).setOnClickListener { declineCall() }
    }

    private val callAcceptReceiver =
            object : BroadcastReceiver() {
                override fun onReceive(context: Context?, intent: Intent?) {
                    if ("com.cmtevents.cmtmeet.CALL_ACCEPTED" == intent?.action) {
                        Log.d("NativeAndroid", "✅ CALL_ACCEPTED received — finishing CallActivity")
                        finishAndRemoveTask()
                    }
                }
            }

    private val callDeclineReceiver =
            object : BroadcastReceiver() {
                override fun onReceive(context: Context, intent: Intent?) {
                    if ("com.cmtevents.cmtmeet.CALL_DECLINED".equals(intent?.action)) {
                        updateUIDeclined()
                    }
                }
            }

    override fun onResume() {
        super.onResume()
        registerReceiver(callDeclineReceiver, IntentFilter("com.cmtevents.cmtmeet.CALL_DECLINED"))
        registerReceiver(callAcceptReceiver, IntentFilter("com.cmtevents.cmtmeet.CALL_ACCEPTED"))
    }

    override fun onPause() {
        super.onPause()
        unregisterReceiver(callDeclineReceiver)
        unregisterReceiver(callAcceptReceiver)
    }

    override fun onStop() {
        super.onStop()

        // Only auto-finish if the activity is not in the foreground AND user already accepted
        if (!isFinishing && wasCallAccepted) {
            Log.d("NativeAndroid", "🔚 onStop → finishing due to accepted call")
            finishAndRemoveTask()
        }
    }

    private fun acceptCall() {
        wasCallAccepted = true

        sendBroadcast(
                Intent(this, CallActionReceiver::class.java).apply {
                    action = "ACCEPT_CALL"
                    putExtra("call_data", JSONObject(callData).toString())
                }
        )
    }

    private fun declineCall() {
        sendBroadcast(
                Intent(this, CallActionReceiver::class.java).apply {
                    action = "DECLINE_CALL"
                    putExtra("call_data", JSONObject(callData).toString())
                }
        )
        updateUIDeclined()
    }

    private fun updateUIDeclined() {
        findViewById<TextView>(R.id.callingStatus).apply {
            text = "Call Declined"
            setTextColor(Color.RED)
        }
        findViewById<LinearLayout>(R.id.acceptButton).visibility = View.GONE
        findViewById<LinearLayout>(R.id.declineButton).visibility = View.GONE
        handler.postDelayed({ finish() }, 3000)
    }

    private fun cancelNotification() {
        (getSystemService(NOTIFICATION_SERVICE) as NotificationManager).cancel(1001)
    }
}
