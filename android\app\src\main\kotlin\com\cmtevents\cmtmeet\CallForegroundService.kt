package com.cmtevents.cmtmeet

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationCompat

class CallForegroundService : Service() {

    companion object {
        private const val CHANNEL_ID = "call_foreground_channel"
        private const val NOTIFICATION_ID = 2001
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        createNotificationChannel()

        val tapIntent =
                Intent(this, MainActivity::class.java).apply {
                    putExtra("navigateTo", "call")
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
                }

        val tapPendingIntent =
                PendingIntent.getActivity(
                        this,
                        0,
                        tapIntent,
                        PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                )

        val hangUpIntent = Intent(this, HangUpReceiver::class.java)
        val hangUpPendingIntent =
                PendingIntent.getBroadcast(
                        this,
                        0,
                        hangUpIntent,
                        PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                )

        val notification =
                NotificationCompat.Builder(this, CHANNEL_ID)
                        .setSmallIcon(R.drawable.call_24px)
                        .setContentTitle("Call in progress")
                        .setContentText("Tap to return to the call")
                        .setContentIntent(tapPendingIntent)
                        .setOngoing(true)
                        .addAction(R.drawable.call_end_24px, "HANG UP", hangUpPendingIntent)
                        .build()

        startForeground(NOTIFICATION_ID, notification)
        return START_STICKY
    }

    override fun onBind(intent: Intent?): IBinder? = null

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel =
                    NotificationChannel(
                            CHANNEL_ID,
                            "Ongoing Call",
                            NotificationManager.IMPORTANCE_LOW
                    )
            (getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager)
                    .createNotificationChannel(channel)
        }
    }
}
