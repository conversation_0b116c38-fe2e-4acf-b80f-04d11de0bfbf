package com.cmtevents.cmtmeet

import android.app.*
import android.app.ActivityManager
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.telephony.TelephonyManager
import android.util.Log
import androidx.core.app.NotificationCompat
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.embedding.engine.FlutterEngineCache
import org.json.JSONObject

class FirebaseMessagingService : FirebaseMessagingService() {
    private val CHANNEL = "com.cmtevents.cmtmeet/call_service"

    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        val data = remoteMessage.data
        val callDataJson = JSONObject(data as Map<*, *>).toString()
        val type = data["type"]
        val status = data["status"]
        val callerName = data["callerName"] ?: "Unknown"
        val callUUID = data["callUUID"] ?: "Unkonwn"
        val callType = data["callType"] ?: "audio"

        Log.d("NativeAndroid", "📩 Message received: $data")

        if (!isAppInForeground()) {

            preloadFlutter(applicationContext)
        }

        when (type) {
            "CALL_RESPONSE" ->
                    handleCallResponse(status, callDataJson, callerName, callType, callUUID)
        }
    }

    fun isAppInForeground(): Boolean {
        val activityManager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val appProcesses = activityManager.runningAppProcesses ?: return false

        val packageName = applicationContext.packageName
        for (appProcess in appProcesses) {
            if (appProcess.importance ==
                            ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND &&
                            appProcess.processName == packageName
            ) {
                return true
            }
        }
        return false
    }

    private fun preloadFlutter(context: Context) {
        Handler(Looper.getMainLooper()).post {
            if (FlutterEngineCache.getInstance().get("call_engine") == null) {
                val engine = FlutterEngine(context)
                FlutterEngineCache.getInstance().put("call_engine", engine)
                Log.d("NativeAndroid", "🚀 Flutter engine preloaded")
            }
        }
    }

    private fun handleCallResponse(
            status: String?,
            callDataJson: String,
            callerName: String,
            callType: String,
            callUUID: String
    ) {
        when (status) {
            "CALLING" -> {
                if (CallStateManager.isAppBusy) {
                    Log.d("NativeAndroid", "📵 App busy — auto BUSY reply")
                    sendBusyBroadcast(callDataJson)
                } else {
                    CallStateManager.isAppBusy = true
                    showFullScreenNotification(
                            applicationContext,
                            callDataJson,
                            callerName,
                            callType
                    )
                }
            }
            "BUSY" -> {
                Log.d("NativeAndroid", "📴 Call BUSY received for $callerName")
                // Send BUSY status to Flutter
                CallStateManager.isAppBusy = false
                sendBusyStatusToFlutter()
            }
            "DECLINED" -> {
                Log.d("NativeAndroid", "📴 Call DECLINED received for $callerName")
                CallStateManager.isAppBusy = false
                sendBroadcast(Intent("com.cmtevents.cmtmeet.CALL_DECLINED"))
                // updateCallStatus(CallStatus.DECLINED)
                sendDeclineStatusToFlutter(callUUID)
                dismissCallNotification()
            }
        }
    }

    private fun sendBusyBroadcast(callDataJson: String) {
        Intent(this, CallActionReceiver::class.java).apply {
            action = "BUSY_CALL"
            putExtra("call_data", callDataJson)
            sendBroadcast(this)
        }
    }

    private fun sendBusyStatusToFlutter() {
        try {
            FlutterMethodHandler.invokeSilently("onCallStatusChanged", mapOf("status" to "BUSY"))
        } catch (e: Exception) {
            Log.e("NativeAndroid", "❌ Failed to send BUSY status to Flutter", e)
        }
    }

    private fun sendDeclineStatusToFlutter(callUUID: String) {
        try {
            FlutterMethodHandler.invokeSilently(
                    "onCallStatusChanged",
                    mapOf("status" to "DECLINED", "callUUID" to callUUID)
            )
        } catch (e: Exception) {
            Log.e("NativeAndroid", "❌ Failed to send DECLINED status to Flutter", e)
        }
    }

    private fun dismissCallNotification() {
        getNotificationManager(this).cancel(1001)
        RingtonePlayer.stop()
    }

    private fun getNotificationManager(context: Context): NotificationManager {
        return context.getSystemService(NOTIFICATION_SERVICE) as NotificationManager
    }

    private fun showFullScreenNotification(
            context: Context,
            callDataJson: String,
            callerName: String,
            callType: String
    ) {
        val channelId = "call_channel_id"

        val telephonyManager =
                context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager

        @Suppress("DEPRECATION")
        val isSystemCallActive = telephonyManager.callState != TelephonyManager.CALL_STATE_IDLE

        if (!isSystemCallActive && !RingtonePlayer.isPlaying()) {
            RingtonePlayer.play(context)
        }

        val fullScreenIntent =
                Intent(context, CallActivity::class.java).apply {
                    putExtra("call_data", callDataJson)
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
                }

        val fullScreenPendingIntent =
                PendingIntent.getActivity(
                        context,
                        0,
                        fullScreenIntent,
                        PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                )

        val acceptIntent =
                Intent(context, CallActionReceiver::class.java).apply {
                    action = "ACCEPT_CALL"
                    putExtra("call_data", callDataJson)
                }

        val declineIntent =
                Intent(context, CallActionReceiver::class.java).apply {
                    action = "DECLINE_CALL"
                    putExtra("call_data", callDataJson)
                }

        val acceptPendingIntent =
                PendingIntent.getBroadcast(
                        context,
                        1,
                        acceptIntent,
                        PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                )

        val declinePendingIntent =
                PendingIntent.getBroadcast(
                        context,
                        2,
                        declineIntent,
                        PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                )

        val notificationManager =
                context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel =
                    NotificationChannel(
                                    channelId,
                                    "Call Notifications",
                                    NotificationManager.IMPORTANCE_HIGH
                            )
                            .apply {
                                description = "Used for incoming call alerts"
                                lockscreenVisibility = Notification.VISIBILITY_PUBLIC
                            }
            notificationManager.createNotificationChannel(channel)
        }

        val notification =
                NotificationCompat.Builder(context, channelId)
                        .setSmallIcon(R.drawable.account_circle_24px)
                        .setContentTitle(callerName)
                        .setContentText(
                                if (callType == "video") "Incoming video call"
                                else "Incoming voice call"
                        )
                        .setPriority(NotificationCompat.PRIORITY_HIGH)
                        .setCategory(NotificationCompat.CATEGORY_CALL)
                        .setFullScreenIntent(fullScreenPendingIntent, true)
                        .setAutoCancel(true)
                        .setOngoing(true)
                        .addAction(R.drawable.call_24px, "Accept", acceptPendingIntent)
                        .addAction(R.drawable.call_end_24px, "Decline", declinePendingIntent)
                        .build()

        notificationManager.notify(1001, notification)
    }
}

object CallStateManager {
    @Volatile var isAppBusy: Boolean = false
}
