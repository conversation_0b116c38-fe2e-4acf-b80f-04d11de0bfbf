package com.cmtevents.cmtmeet

import android.content.Context
import android.content.Intent
import android.os.Handler
import android.os.Looper
import android.util.Log
import io.flutter.plugin.common.MethodChannel
import org.json.JSONObject

object FlutterMethodHandler {
    var nativeChannel: MethodChannel? = null
    private val callQueue = mutableListOf<Pair<String, Any?>>()
    var isFlutterReady: Boolean = false

    fun invokeWhenReady(context: Context, method: String, arguments: Any?) {
        if (isFlutterReady && nativeChannel != null) {
            Log.d("NativeAndroid", "📡 Sending method to Flutter: $method")

            Handler(Looper.getMainLooper()).post {
                try {
                    nativeChannel!!.invokeMethod(method, arguments)

                    // 👁️‍🗨️ If app is backgrounded, bring MainActivity to front
                    if (!AppVisibilityTracker.isInForeground) {
                        Log.d(
                                "NativeAndroid",
                                "🌙 App in background — bringing MainActivity to front"
                        )
                        val backgroundAppContext = context.applicationContext
                        if (backgroundAppContext != null && arguments is Map<*, *>) {
                            val callDataJson = JSONObject(arguments).toString()
                            bringMainActivityToFront(backgroundAppContext, callDataJson)
                        }
                    }
                } catch (e: Exception) {
                    Log.e("NativeAndroid", "❌ Failed to invoke $method: ${e.message}", e)
                }
            }
        } else {
            Log.d(
                    "NativeAndroid",
                    "🕓 Flutter not ready, queuing method: $method with args: $arguments"
            )
            callQueue.add(method to arguments)
        }
    }

    fun invokeSilently(method: String, arguments: Any?) {
        if (isFlutterReady && nativeChannel != null) {
            Log.d("NativeAndroid", "📡 Sending (silent) method to Flutter: $method")

            Handler(Looper.getMainLooper()).post {
                try {
                    nativeChannel!!.invokeMethod(method, arguments)
                } catch (e: Exception) {
                    Log.e("NativeAndroid", "❌ Failed to invoke $method silently: ${e.message}", e)
                }
            }
        } else {
            Log.d(
                    "NativeAndroid",
                    "🕓 Flutter not ready, queuing (silent) method: $method with args: $arguments"
            )
            callQueue.add(method to arguments)
        }
    }

    fun markFlutterReady(channel: MethodChannel) {
        nativeChannel = channel
        isFlutterReady = true
        Log.d("NativeAndroid", "🚀 Flutter ready, flushing ${callQueue.size} queued calls")

        for ((method, args) in callQueue) {
            Log.d("NativeAndroid", "📤 Flushing method: $method with args: $args")
            // Handler(Looper.getMainLooper()).post {
            try {
                channel.invokeMethod(method, args)
            } catch (e: Exception) {
                Log.e("NativeAndroid", "❌ Error flushing $method: ${e.message}", e)
            }
            // }
        }

        callQueue.clear()
        Log.d("NativeAndroid", "✅ Call queue cleared")
    }

    private fun bringMainActivityToFront(context: Context, callDataJson: String) {
        val intent =
                Intent(context, MainActivity::class.java).apply {
                    putExtra("call_data", callDataJson)
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
                }
        context.startActivity(intent)
    }
}
