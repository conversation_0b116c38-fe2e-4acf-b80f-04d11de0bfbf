package com.cmtevents.cmtmeet

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log

class HangUpReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent?) {
        FlutterMethodHandler.invokeWhen<PERSON><PERSON>y(context.applicationContext, "hang_up", null)
        CallStateManager.isAppBusy = false
        Log.d("NativeAndroid", "Call state is now free")
        context.stopService(Intent(context, CallForegroundService::class.java))
    }
}
