package com.cmtevents.cmtmeet

import android.app.Activity
import android.app.Application
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.WindowManager
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity : FlutterActivity() {
    private val CHANNEL = "com.cmtevents.cmtmeet/caller_tone"
    private val SERVICE_CHANNEL = "com.cmtevents.cmtmeet/call_service"
    private val CLEANUP_CHANNEL = "com.cmtevents.cmtmeet/cleanup"
    private var tonePlayer: NativeCallerTonePlayer? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O_MR1) {
            setShowWhenLocked(true)
            setTurnScreenOn(true)
        } else {
            @Suppress("DEPRECATION")
            window.addFlags(
                    WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED or
                            WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD or
                            WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON or
                            WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON
            )
        }

        intent?.extras?.getString("call_data")?.let { callData ->
            Log.d("NativeAndroid", "📞 Received call_data in onCreate")
            MethodChannel(flutterEngine!!.dartExecutor.binaryMessenger, SERVICE_CHANNEL)
                    .invokeMethod("launch_call", callData)
        }

        intent?.extras?.getString("cold_call_data")?.let { callData ->
            Log.d("NativeAndroid", "📞 Received call_data in onCreate")
            MethodChannel(flutterEngine!!.dartExecutor.binaryMessenger, SERVICE_CHANNEL)
                    .invokeMethod("cold_launch_call", callData)
        }
    }

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        tonePlayer = NativeCallerTonePlayer(applicationContext)

        val methodChannel =
                MethodChannel(flutterEngine.dartExecutor.binaryMessenger, SERVICE_CHANNEL)
        FlutterMethodHandler.markFlutterReady(methodChannel)

        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler {
                call,
                result ->
            tonePlayer?.onMethodCall(call, result)
        }

        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CLEANUP_CHANNEL)
                .setMethodCallHandler { call, result ->
                    if (call.method == "resetAppBusy") {
                        val value = call.argument<Boolean>("value") ?: false
                        CallStateManager.isAppBusy = value
                        Log.d("NativeAndroid", "✅ CallStateManager.isAppBusy set to $value")
                        result.success(null)
                    } else {
                        result.notImplemented()
                    }
                }

        methodChannel.setMethodCallHandler { call, result ->
            when (call.method) {
                "startService" -> {
                    Log.d("NativeAndroid", "🟢 [startService] Invoked from Flutter")
                    CallStateManager.isAppBusy = true
                    startCallForegroundService(applicationContext)
                    result.success(null)
                }
                "stopService" -> {
                    Log.d("NativeAndroid", "🛑 [stopService] Invoked from Flutter")
                    CallStateManager.isAppBusy = false
                    stopCallForegroundService(applicationContext)
                    result.success(null)
                }
                "launch_call" -> {
                    Log.d("NativeAndroid", "📞 [launch_call] Invoked from Flutter")
                    result.success(null)
                }
                else -> {
                    Log.w("NativeAndroid", "⚠️ Unknown method: ${call.method}")
                    result.notImplemented()
                }
            }
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)

        // 🔥 Option 1: Use navigateTo=call (your current logic)
        intent.extras?.getString("navigateTo")?.let {
            if (it == "call") {
                MethodChannel(flutterEngine!!.dartExecutor.binaryMessenger, SERVICE_CHANNEL)
                        .invokeMethod("openCallScreen", null)
            }
        }

        // ✅ Option 2: NEW — If call_data is passed (from FCM), also trigger openCallScreen
        intent.extras?.getString("call_data")?.let { callData ->
            Log.d("NativeAndroid", "📞 Received call_data via intent")
            MethodChannel(flutterEngine!!.dartExecutor.binaryMessenger, SERVICE_CHANNEL)
                    .invokeMethod("launch_call", callData)
        }

        intent.extras?.getString("cold_call_data")?.let { callData ->
            Log.d("NativeAndroid", "📞 Received call_data via intent")
            MethodChannel(flutterEngine!!.dartExecutor.binaryMessenger, SERVICE_CHANNEL)
                    .invokeMethod("cold_launch_call", callData)
        }
    }

    private fun startCallForegroundService(context: Context) {
        val serviceIntent = Intent(context, CallForegroundService::class.java)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            context.startForegroundService(serviceIntent)
        } else {
            context.startService(serviceIntent)
        }
    }

    private fun stopCallForegroundService(context: Context) {
        val intent = Intent(context, CallForegroundService::class.java)
        context.stopService(intent)
    }
}

object AppVisibilityTracker : Application.ActivityLifecycleCallbacks {
    private var activityReferences = 0
    private var isActivityChangingConfigurations = false
    var isInForeground = false
        private set

    override fun onActivityStarted(activity: Activity) {
        if (++activityReferences == 1 && !isActivityChangingConfigurations) {
            isInForeground = true
        }
    }

    override fun onActivityStopped(activity: Activity) {
        isActivityChangingConfigurations = activity.isChangingConfigurations
        if (--activityReferences == 0 && !isActivityChangingConfigurations) {
            isInForeground = false
        }
    }

    override fun onActivityResumed(activity: Activity) {}
    override fun onActivityPaused(activity: Activity) {}
    override fun onActivityCreated(a: Activity, b: Bundle?) {}
    override fun onActivityDestroyed(a: Activity) {}
    override fun onActivitySaveInstanceState(a: Activity, outState: Bundle) {}
}