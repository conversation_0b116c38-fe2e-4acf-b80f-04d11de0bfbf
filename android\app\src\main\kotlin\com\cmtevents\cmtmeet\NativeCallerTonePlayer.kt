package com.cmtevents.cmtmeet

import android.content.Context
import android.media.AudioAttributes
import android.media.AudioManager
import android.media.MediaPlayer
import android.os.Handler
import android.os.Looper
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel

class NativeCallerTonePlayer(private val context: Context) : MethodChannel.MethodCallHandler {

    private var mediaPlayer: MediaPlayer? = null
    private var isSpeakerOn = true
    private val handler = Handler(Looper.getMainLooper())

    override fun onMethodCall(call: MethodCall, result: MethodChannel.Result) {
        when (call.method) {
            "startRinging" -> {
                startRinging()
                result.success(null)
            }
            "stopRinging" -> {
                stopRinging()
                result.success(null)
            }
            "switchOutput" -> {
                val toSpeaker = call.argument<Boolean>("speaker") ?: true
                switchOutput(toSpeaker)
                result.success(null)
            }
            else -> result.notImplemented()
        }
    }

    fun startRinging() {
        stopRinging() // ensure cleanup before starting again
        val afd = context.resources.openRawResourceFd(R.raw.ring)
        mediaPlayer =
                MediaPlayer().apply {
                    setDataSource(afd.fileDescriptor, afd.startOffset, afd.length)
                    setAudioAttributes(
                            AudioAttributes.Builder()
                                    .setUsage(AudioAttributes.USAGE_VOICE_COMMUNICATION)
                                    .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
                                    .build()
                    )
                    isLooping = false
                    prepare()
                    setVolume(0.1f, 0.1f)
                    setOnCompletionListener {
                        handler.postDelayed({ startRinging() }, 0) // repeat immediately after end
                    }
                    start()
                }

        switchOutput(isSpeakerOn)
    }

    fun stopRinging() {
        mediaPlayer?.stop()
        mediaPlayer?.release()
        mediaPlayer = null
    }

    fun switchOutput(toSpeaker: Boolean) {
        val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
        isSpeakerOn = toSpeaker

        if (toSpeaker) {
            audioManager.mode = AudioManager.MODE_NORMAL
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                val devices = audioManager.availableCommunicationDevices
                val speaker =
                        devices.firstOrNull {
                            it.type == android.media.AudioDeviceInfo.TYPE_BUILTIN_SPEAKER
                        }
                if (speaker != null) {
                    audioManager.setCommunicationDevice(speaker)
                }
            } else {
                @Suppress("DEPRECATION") audioManager.setSpeakerphoneOn(true)
            }
        } else {
            audioManager.mode = AudioManager.MODE_IN_CALL
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                val devices = audioManager.availableCommunicationDevices
                val earpiece =
                        devices.firstOrNull {
                            it.type == android.media.AudioDeviceInfo.TYPE_BUILTIN_EARPIECE
                        }
                if (earpiece != null) {
                    audioManager.setCommunicationDevice(earpiece)
                }
            } else {
                @Suppress("DEPRECATION") audioManager.setSpeakerphoneOn(false)
            }
        }
    }
}
