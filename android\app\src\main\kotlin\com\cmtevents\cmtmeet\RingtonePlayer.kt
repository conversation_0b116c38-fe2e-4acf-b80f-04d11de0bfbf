package com.cmtevents.cmtmeet

import android.content.Context
import android.media.Ringtone
import android.media.RingtoneManager

object RingtonePlayer {
    private var ringtone: Ringtone? = null

    fun play(context: Context) {
        if (isPlaying()) return // 🛑 Already playing

        try {
            val uri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_RINGTONE)
            ringtone = RingtoneManager.getRingtone(context.applicationContext, uri)
            ringtone?.play()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun stop() {
        try {
            ringtone?.stop()
            ringtone = null
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun isPlaying(): Boolean = ringtone?.isPlaying == true
}
