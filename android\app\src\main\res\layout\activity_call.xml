<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:gravity="center_horizontal"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000000"
    android:padding="24dp"
    android:weightSum="10">

    <!-- Top Spacer -->
    <Space
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <!-- Calling Text -->
    <TextView
        android:id="@+id/callingStatus"
        android:text="Calling..."
        android:textColor="#FFFFFF"
        android:textSize="24sp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <!-- Spacer -->
    <Space
        android:layout_width="match_parent"
        android:layout_height="16dp"
        android:layout_weight="4" />

    <!-- User Icon -->
    <ImageView
        android:layout_width="150dp"
        android:layout_height="150dp"
        android:src="@drawable/account_circle_24px"
        android:tint="#999999" />

    <!-- Name -->
    <TextView
        android:id="@+id/callerNameTextView"
        android:text="Kumaran Sokkar"
        android:textColor="#FFFFFF"
        android:textSize="22sp"
        android:layout_marginTop="16dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <!-- Title -->
    <TextView
        android:id="@+id/userJobTitle"
        android:text="Artist"
        android:textColor="#CCCCCC"
        android:textSize="16sp"
        android:layout_marginTop="4dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <!-- Company -->
    <TextView
        android:id="@+id/userCompany"
        android:text="CMT-Official"
        android:textColor="#CCCCCC"
        android:textSize="16sp"
        android:layout_marginTop="2dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <!-- Spacer -->
    <Space
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="5"
    />

    <!-- Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_marginBottom="24dp"
        android:weightSum="2">

        <!-- Decline Button -->
        <LinearLayout
            android:id="@+id/declineButton"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="50dp"
            android:layout_marginEnd="8dp"
            android:orientation="horizontal"
            android:gravity="center"
            android:background="@drawable/call_decline_button">

            <ImageView
                android:contentDescription="@string/decline"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="8dp"
                android:src="@drawable/call_end_24px" />

            <TextView
                android:text="@string/decline"
                android:textSize="14sp"
                android:textColor="#FFFFFF"
                android:textStyle="bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
        </LinearLayout>

        <!-- Accept Button -->
        <LinearLayout
            android:id="@+id/acceptButton"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="50dp"
            android:layout_marginStart="8dp"
            android:orientation="horizontal"
            android:gravity="center"
            android:background="@drawable/call_accept_button">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="8dp"
                android:contentDescription="@string/accept"
                android:src="@drawable/call_24px" />

            <TextView
                android:text="@string/accept"
                android:textSize="14sp"
                android:textColor="#FFFFFF"
                android:textStyle="bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
        </LinearLayout>
    </LinearLayout>

</LinearLayout>