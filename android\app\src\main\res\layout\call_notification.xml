<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/notification_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/notification_bg"
    android:padding="0dp"
    android:minHeight="240dp"
>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="15dp"
    >

        <!-- Header Row: Name + Profile Image -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/caller_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Caller Name"
                    android:textStyle="bold"
                    android:textSize="16sp"
                    android:textColor="@color/text_color"
                />

                <TextView
                    android:id="@+id/call_type"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Incoming voice call"
                    android:textSize="14sp"
                    android:textColor="@color/text_color" />
            </LinearLayout>

            <ImageView
                android:id="@+id/profile_image"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:scaleType="centerCrop"
                android:src="@drawable/account_circle_24px_black"
                android:backgroundTint="@color/text_color"
                android:layout_marginStart="12dp" />
        </LinearLayout>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="5dp">

            <Button
                android:id="@+id/accept_button"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:layout_weight="1"
                android:text="@string/notification_accept"
                android:textColor="@android:color/white"
                android:backgroundTint="#4CAF50" />

            <Button
                android:id="@+id/decline_button"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:layout_weight="1"
                android:text="@string/notification_decline"
                android:textColor="@android:color/white"
                android:backgroundTint="#F44336"
                android:layout_marginStart="8dp" />
        </LinearLayout>
    </LinearLayout>
</FrameLayout>