buildscript {
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.3.1'
        classpath 'com.google.gms:google-services:4.4.0'
        // api 'io.agora.infra:aosl:1.2.13'
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

rootProject.buildDir = "../build"
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
    project.evaluationDependsOn(":app")

    if (project.plugins.hasPlugin('java')) {
        project.extensions.configure(JavaPluginExtension) {
            toolchain {
                languageVersion = JavaLanguageVersion.of(17)
            }
        }
    }
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
