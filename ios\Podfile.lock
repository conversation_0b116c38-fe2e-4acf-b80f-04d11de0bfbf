PODS:
  - add_2_calendar (0.0.1):
    - Flutter
  - agora_chat_sdk (0.0.1):
    - Flutter
    - HyphenateChat (= 4.10.2)
  - agora_rtc_engine (6.5.1):
    - AgoraIrisRTC_iOS (= 4.5.1-build.1)
    - AgoraRtcEngine_iOS (= 4.5.1)
    - Flutter
  - AgoraInfra_iOS (1.2.13)
  - AgoraIrisRTC_iOS (4.5.1-build.1)
  - AgoraRtcEngine_iOS (4.5.1):
    - AgoraRtcEngine_iOS/AIAEC (= 4.5.1)
    - AgoraRtcEngine_iOS/AIAECLL (= 4.5.1)
    - AgoraRtcEngine_iOS/AINS (= 4.5.1)
    - AgoraRtcEngine_iOS/AINSLL (= 4.5.1)
    - AgoraRtcEngine_iOS/AudioBeauty (= 4.5.1)
    - AgoraRtcEngine_iOS/ClearVision (= 4.5.1)
    - AgoraRtcEngine_iOS/ContentInspect (= 4.5.1)
    - AgoraRtcEngine_iOS/FaceCapture (= 4.5.1)
    - AgoraRtcEngine_iOS/FaceDetection (= 4.5.1)
    - AgoraRtcEngine_iOS/LipSync (= 4.5.1)
    - AgoraRtcEngine_iOS/ReplayKit (= 4.5.1)
    - AgoraRtcEngine_iOS/RtcBasic (= 4.5.1)
    - AgoraRtcEngine_iOS/SpatialAudio (= 4.5.1)
    - AgoraRtcEngine_iOS/VideoAv1CodecDec (= 4.5.1)
    - AgoraRtcEngine_iOS/VideoAv1CodecEnc (= 4.5.1)
    - AgoraRtcEngine_iOS/VideoCodecDec (= 4.5.1)
    - AgoraRtcEngine_iOS/VideoCodecEnc (= 4.5.1)
    - AgoraRtcEngine_iOS/VirtualBackground (= 4.5.1)
    - AgoraRtcEngine_iOS/VQA (= 4.5.1)
  - AgoraRtcEngine_iOS/AIAEC (4.5.1)
  - AgoraRtcEngine_iOS/AIAECLL (4.5.1)
  - AgoraRtcEngine_iOS/AINS (4.5.1)
  - AgoraRtcEngine_iOS/AINSLL (4.5.1)
  - AgoraRtcEngine_iOS/AudioBeauty (4.5.1)
  - AgoraRtcEngine_iOS/ClearVision (4.5.1)
  - AgoraRtcEngine_iOS/ContentInspect (4.5.1)
  - AgoraRtcEngine_iOS/FaceCapture (4.5.1)
  - AgoraRtcEngine_iOS/FaceDetection (4.5.1)
  - AgoraRtcEngine_iOS/LipSync (4.5.1)
  - AgoraRtcEngine_iOS/ReplayKit (4.5.1)
  - AgoraRtcEngine_iOS/RtcBasic (4.5.1):
    - AgoraInfra_iOS (= 1.2.13)
  - AgoraRtcEngine_iOS/SpatialAudio (4.5.1)
  - AgoraRtcEngine_iOS/VideoAv1CodecDec (4.5.1)
  - AgoraRtcEngine_iOS/VideoAv1CodecEnc (4.5.1)
  - AgoraRtcEngine_iOS/VideoCodecDec (4.5.1)
  - AgoraRtcEngine_iOS/VideoCodecEnc (4.5.1)
  - AgoraRtcEngine_iOS/VirtualBackground (4.5.1)
  - AgoraRtcEngine_iOS/VQA (4.5.1)
  - awesome_notifications (0.10.0):
    - Flutter
    - IosAwnCore (~> 0.10.0)
  - CryptoSwift (1.8.4)
  - Firebase/CoreOnly (11.13.0):
    - FirebaseCore (~> 11.13.0)
  - Firebase/Messaging (11.13.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.13.0)
  - Firebase/RemoteConfig (11.13.0):
    - Firebase/CoreOnly
    - FirebaseRemoteConfig (~> 11.13.0)
  - firebase_core (3.14.0):
    - Firebase/CoreOnly (= 11.13.0)
    - Flutter
  - firebase_messaging (15.2.5):
    - Firebase/Messaging (= 11.13.0)
    - firebase_core
    - Flutter
  - firebase_remote_config (5.4.5):
    - Firebase/RemoteConfig (= 11.13.0)
    - firebase_core
    - Flutter
  - FirebaseABTesting (11.13.0):
    - FirebaseCore (~> 11.13.0)
  - FirebaseCore (11.13.0):
    - FirebaseCoreInternal (~> 11.13.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Logger (~> 8.1)
  - FirebaseCoreInternal (11.13.0):
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
  - FirebaseInstallations (11.13.0):
    - FirebaseCore (~> 11.13.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.13.0):
    - FirebaseCore (~> 11.13.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Reachability (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - nanopb (~> 3.30910.0)
  - FirebaseRemoteConfig (11.13.0):
    - FirebaseABTesting (~> 11.0)
    - FirebaseCore (~> 11.13.0)
    - FirebaseInstallations (~> 11.0)
    - FirebaseRemoteConfigInterop (~> 11.0)
    - FirebaseSharedSwift (~> 11.0)
    - GoogleUtilities/Environment (~> 8.1)
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
  - FirebaseRemoteConfigInterop (11.15.0)
  - FirebaseSharedSwift (11.15.0)
  - Flutter (1.0.0)
  - flutter_callkit_incoming (0.0.1):
    - CryptoSwift
    - Flutter
  - flutter_native_splash (2.4.3):
    - Flutter
  - flutter_ringtone_player (0.0.1):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - HyphenateChat (4.10.2)
  - IosAwnCore (0.10.0)
  - iris_method_channel (0.0.1):
    - Flutter
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - PromisesObjC (2.4.0)
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - url_launcher_ios (0.0.1):
    - Flutter
  - wakelock_plus (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - add_2_calendar (from `.symlinks/plugins/add_2_calendar/ios`)
  - agora_chat_sdk (from `.symlinks/plugins/agora_chat_sdk/ios`)
  - agora_rtc_engine (from `.symlinks/plugins/agora_rtc_engine/ios`)
  - awesome_notifications (from `.symlinks/plugins/awesome_notifications/ios`)
  - Firebase/Messaging
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - firebase_remote_config (from `.symlinks/plugins/firebase_remote_config/ios`)
  - Flutter (from `Flutter`)
  - flutter_callkit_incoming (from `.symlinks/plugins/flutter_callkit_incoming/ios`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - flutter_ringtone_player (from `.symlinks/plugins/flutter_ringtone_player/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - iris_method_channel (from `.symlinks/plugins/iris_method_channel/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - AgoraInfra_iOS
    - AgoraIrisRTC_iOS
    - AgoraRtcEngine_iOS
    - CryptoSwift
    - Firebase
    - FirebaseABTesting
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseRemoteConfig
    - FirebaseRemoteConfigInterop
    - FirebaseSharedSwift
    - GoogleDataTransport
    - GoogleUtilities
    - HyphenateChat
    - IosAwnCore
    - nanopb
    - PromisesObjC

EXTERNAL SOURCES:
  add_2_calendar:
    :path: ".symlinks/plugins/add_2_calendar/ios"
  agora_chat_sdk:
    :path: ".symlinks/plugins/agora_chat_sdk/ios"
  agora_rtc_engine:
    :path: ".symlinks/plugins/agora_rtc_engine/ios"
  awesome_notifications:
    :path: ".symlinks/plugins/awesome_notifications/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  firebase_remote_config:
    :path: ".symlinks/plugins/firebase_remote_config/ios"
  Flutter:
    :path: Flutter
  flutter_callkit_incoming:
    :path: ".symlinks/plugins/flutter_callkit_incoming/ios"
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  flutter_ringtone_player:
    :path: ".symlinks/plugins/flutter_ringtone_player/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  iris_method_channel:
    :path: ".symlinks/plugins/iris_method_channel/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  add_2_calendar: e626377f607d055070093cbd56abd1fafa1a02a5
  agora_chat_sdk: c971710a18b05c37d13f28a865efdb4b1bdb62fa
  agora_rtc_engine: 7434787c0b5284d803c31c726562b8399b8c1744
  AgoraInfra_iOS: 65e11a2183ab7836258768868d06058c22701b13
  AgoraIrisRTC_iOS: dd3d957c25be6bd2b2a5d03fe706ebe8a46909f0
  AgoraRtcEngine_iOS: 5092a058c7b2842db39d8ca614d451af6f84969a
  awesome_notifications: 0f432b28098d193920b11a44cfa9d2d9313a3888
  CryptoSwift: e64e11850ede528a02a0f3e768cec8e9d92ecb90
  Firebase: ****************************************
  firebase_core: 700bac7ed92bb754fd70fbf01d72b36ecdd6d450
  firebase_messaging: 49be4c5372d93da18484873ee6cf8ae1327af26e
  firebase_remote_config: 1c2a0ba5b23d5386142eebf5ffd2a646608d1b22
  FirebaseABTesting: 4048f61cc10d2fad064d3089ace6bd5fb910169b
  FirebaseCore: c692c7f1c75305ab6aff2b367f25e11d73aa8bd0
  FirebaseCoreInternal: 29d7b3af4aaf0b8f3ed20b568c13df399b06f68c
  FirebaseInstallations: 0ee9074f2c1e86561ace168ee1470dc67aabaf02
  FirebaseMessaging: 195bbdb73e6ca1dbc76cd46e73f3552c084ef6e4
  FirebaseRemoteConfig: 518ca257cdb2ccbc2b781ef2f2104f1104c7488f
  FirebaseRemoteConfigInterop: 1c6135e8a094cc6368949f5faeeca7ee8948b8aa
  FirebaseSharedSwift: e17c654ef1f1a616b0b33054e663ad1035c8fd40
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_callkit_incoming: cb8138af67cda6dd981f7101a5d709003af21502
  flutter_native_splash: 6cad9122ea0fad137d23137dd14b937f3e90b145
  flutter_ringtone_player: a77c42464250845611eaa44c27e8714acc800138
  fluttertoast: 2c67e14dce98bbdb200df9e1acf610d7a6264ea1
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  HyphenateChat: 6924f613947f6301bee909fc12c3bb01b50f52c6
  IosAwnCore: 653786a911089012092ce831f2945cd339855a89
  iris_method_channel: b9db2053dac3dc84e256c8792eff6f11323a53bd
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  wakelock_plus: 04623e3f525556020ebd4034310f20fe7fda8b49
  webview_flutter_wkwebview: ****************************************

PODFILE CHECKSUM: 1812cf85aa47c187bc7f2aee06c91301283e60c6

COCOAPODS: 1.16.2
