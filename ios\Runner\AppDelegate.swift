import UIKit                 // For UIApplication, UIWindow, etc.
import Flutter               // For FlutterViewController, FlutterAppDelegate
import FirebaseCore          // For FirebaseApp.configure()
import FirebaseMessaging     // For Messaging (APNs token)

@main
@objc class AppDelegate: FlutterAppDelegate {
    
    var voipService: VoIPService?
    var contactEditorService: ContactEditorService?
    var tonePlayer: NativeCallerTonePlayer?

    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        
        FirebaseApp.configure()
        GeneratedPluginRegistrant.register(with: self)

        // Grab the root Flutter controller
        guard let controller = window?.rootViewController as? FlutterViewController else {
            fatalError("rootViewController is not a FlutterViewController")
        }

        // Initialize services
        contactEditorService = ContactEditorService(controller: controller)
        voipService = VoIPService()
        tonePlayer = NativeCallerTonePlayer()

        // Setup MethodChannel for caller tone
        let toneChannel = FlutterMethodChannel(
            name: "com.cmtevents.cmtmeet/caller_tone",
            binaryMessenger: controller.binaryMessenger
        )

        toneChannel.setMethodCallHandler { [weak self] call, result in
            self?.tonePlayer?.handle(call, result: result)
        }

        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }

    override func application(
        _ application: UIApplication,
        didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data
    ) {
        FirebaseService.updateAPNsToken(deviceToken)
        super.application(application, didRegisterForRemoteNotificationsWithDeviceToken: deviceToken)
    }
}
