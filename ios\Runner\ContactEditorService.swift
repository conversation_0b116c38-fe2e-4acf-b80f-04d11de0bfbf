import Foundation                  // For NSObject
import Flutter                     // For FlutterMethodChannel, FlutterResult, FlutterViewController
import Contacts                    // For CNMutableContact, CNContact
import ContactsUI                  // For CNContactViewController
import UIKit                       // For presenting view controllers (if needed)

class ContactEditorService: NSObject, CNContactViewControllerDelegate {
  private weak var controller: FlutterViewController?
  private var contactResult: FlutterResult?

  init(controller: FlutterViewController) {
    self.controller = controller
    super.init()
    setupMethodChannel()
  }

  private func setupMethodChannel() {
    let channel = FlutterMethodChannel(name: "com.cmtevents.contact_editor",
                                       binaryMessenger: controller!.binaryMessenger)
    channel.setMethodCallHandler { [weak self] (call, result) in
      guard call.method == "openContactEditor" else {
        result(FlutterMethodNotImplemented)
        return
      }
      self?.contactResult = result
      if let contactData = call.arguments as? [String: Any] {
        self?.openNativeContactEditor(contactData: contactData)
      }
    }
  }

  private func openNativeContactEditor(contactData: [String: Any]) {
    let contact = CNMutableContact()

    let senderName = contactData["senderName"] as? String ?? ""
    let nameParts = senderName.split(separator: " ", maxSplits: 1).map(String.init)
    contact.givenName = nameParts.first ?? ""
    contact.familyName = nameParts.count > 1 ? nameParts[1] : ""

    contact.phoneNumbers = [CNLabeledValue(label: CNLabelPhoneNumberMobile,
                                           value: CNPhoneNumber(stringValue: contactData["senderPhone"] as? String ?? ""))]

    if let email = contactData["senderEmail"] as? String {
      contact.emailAddresses = [CNLabeledValue(label: CNLabelWork, value: email as NSString)]
    }

    if let company = contactData["senderCompany"] as? String {
      contact.organizationName = company
    }

    if let jobTitle = contactData["senderJobTitle"] as? String {
      contact.jobTitle = jobTitle
    }

    let contactVC = CNContactViewController(forNewContact: contact)
    contactVC.delegate = self
    contactVC.navigationItem.rightBarButtonItem?.isEnabled = true

    let nav = UINavigationController(rootViewController: contactVC)
    controller?.present(nav, animated: true, completion: nil)
  }

  func contactViewController(_ viewController: CNContactViewController, didCompleteWith contact: CNContact?) {
    viewController.dismiss(animated: true) { [weak self] in
      self?.contactResult?(contact != nil)
      self?.contactResult = nil
    }
  }
}
