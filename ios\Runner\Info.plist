<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>CMT Meet</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>CMT Meet</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppleMusicUsageDescription</key>
	<string>This app may access Apple Music for enhanced audio features.</string>
	<key>NSCalendarsUsageDescription</key>
	<string>This app needs access to your calendar to add events</string>
	<key>NSCameraUsageDescription</key>
	<string>This app needs camera access for video meetings and user photos.</string>
	<key>NSContactsUsageDescription</key>
	<string>This app may access your contacts to improve connection and collaboration
			experiences.</string>
	<key>NSCriticalMessagingUsageDescription</key>
	<string>This app uses critical messaging to deliver important updates and alerts to the
			user.</string>
	<key>NSLocalNetworkUsageDescription</key>
	<string>We need access to the local network to connect to services.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>This app uses the microphone for audio during calls or meetings.</string>
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>This app may use speech recognition for voice commands and dictation.</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
		<string>fetch</string>
		<string>voip</string>
		<string>audio</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UIStatusBarHidden</key>
	<false/>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
</dict>
</plist>
