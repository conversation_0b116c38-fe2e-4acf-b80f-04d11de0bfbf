import Foundation
import AVFoundation
import Flutter

class NativeCallerTonePlayer: NSObject {
    
    private var audioPlayer: AVAudioPlayer?
    private var isSpeakerOn = true
    
    func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        switch call.method {
        case "startRinging":
            startRinging()
            result(nil)
        case "stopRinging":
            stopRinging()
            result(nil)
        case "switchOutput":
            let args = call.arguments as? [String: Any]
            let speaker = args?["speaker"] as? Bool ?? true
            switchOutput(toSpeaker: speaker)
            result(nil)
        default:
            result(FlutterMethodNotImplemented)
        }
    }
    
    private func startRinging() {
        stopRinging()  // Clean up if already playing
        
        guard let url = Bundle.main.url(forResource: "ring", withExtension: "wav") else {
            print("⚠️ ring.wav not found in bundle")
            return
        }
        
        do {
            let session = AVAudioSession.sharedInstance()
            try session.setCategory(.playAndRecord, options: [.allowBluetooth, .defaultToSpeaker])
            try session.setActive(true)
            
            audioPlayer = try AVAudioPlayer(contentsOf: url)
            audioPlayer?.volume = 0.1
            audioPlayer?.numberOfLoops = -1  // Infinite loop
            audioPlayer?.prepareToPlay()
            audioPlayer?.play()
            
        } catch {
            print("❌ Error starting audio: \(error.localizedDescription)")
        }
    }
    
    private func stopRinging() {
        audioPlayer?.stop()
        audioPlayer = nil
        try? AVAudioSession.sharedInstance().setActive(false)
    }
    
    private func switchOutput(toSpeaker: Bool) {
        isSpeakerOn = toSpeaker
        let session = AVAudioSession.sharedInstance()
        
        do {
            if toSpeaker {
                try session.overrideOutputAudioPort(.speaker)
            } else {
                try session.overrideOutputAudioPort(.none)
            }
        } catch {
            print("❌ Failed to switch output: \(error.localizedDescription)")
        }
    }
}
