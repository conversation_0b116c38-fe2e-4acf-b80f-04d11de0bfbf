import Foundation                 // For NSObject
import PushKit                   // For PKPushRegistry, PKPushRegistryDelegate, PKPushType, PKPushPayload
import flutter_callkit_incoming   // For SwiftFlutterCallkitIncomingPlugin (if using CallKit incoming plugin)

class VoIPService: NSObject, PKPushRegistryDelegate {
  private var voipRegistry: PKPushRegistry?

  override init() {
    super.init()
    voipRegistry = PKPushRegistry(queue: .main)
    voipRegistry?.delegate = self
    voipRegistry?.desiredPushTypes = [.voIP]
  }

  func pushRegistry(_ registry: PKPushRegistry, didUpdate credentials: PKPushCredentials, for type: PKPushType) {
    let deviceToken = credentials.token.map { String(format: "%02x", $0) }.joined()
    SwiftFlutterCallkitIncomingPlugin.sharedInstance?.setDevicePushTokenVoIP(deviceToken)
  }

  func pushRegistry(_ registry: PKPushRegistry, didReceiveIncomingPushWith payload: PKPushPayload, for type: PKPushType, completion: @escaping () -> Void) {
    guard type == .voIP else {
      completion()
      return
    }
    
    let data = payload.dictionaryPayload
    guard let callData = data["data"] as? [String: Any] else {
      completion()
      return
    }
    
    let callUUID = callData["callUUID"] as? String ?? UUID().uuidString
    let callerName = callData["callerName"] as? String ?? "Unknown"
    let callerId = callData["callerId"] as? String ?? "Unknown"
    let isVideo = (callData["callType"] as? String) == "video"
    
    let info: [String: Any] = [
      "id": callUUID,
      "nameCaller": callerName,
      "handle": callerId,
      "type": isVideo ? 1 : 0,
      "extra": callData
    ]
    
    SwiftFlutterCallkitIncomingPlugin.sharedInstance?.showCallkitIncoming(
      flutter_callkit_incoming.Data(args: info),
      fromPushKit: true
    )
    
    DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
      completion()
    }
  }
}
