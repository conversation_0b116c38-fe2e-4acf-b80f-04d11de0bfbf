import 'package:cmtmeet/utils/constants.dart';
import 'package:flutter/material.dart';

class CustomSubmitButtonWidget extends StatelessWidget {
  final Widget text;
  final VoidCallback onPressed;
  final Color? color;

  const CustomSubmitButtonWidget(
      {super.key, required this.text, required this.onPressed, this.color});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      height: 42,
      decoration: BoxDecoration(
        borderRadius: BorderRadiusDirectional.circular(20),
      ),
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          foregroundColor: Colors.white,
          backgroundColor: color ?? const Color.fromRGBO(192, 16, 88, 1),
          padding: EdgeInsets.zero,
          textStyle: Constants.textStyleSubmit,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          minimumSize: Size(140, 40),
        ),
        onPressed: onPressed,
        child: Center(child: text),
        // child: Center(child: Text(text)),
      ),
    );
  }
}
