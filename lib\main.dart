import 'dart:async';
import 'dart:convert';
import 'dart:io';
// import 'dart:io';

import 'package:agora_chat_sdk/agora_chat_sdk.dart';
import 'package:cmtmeet/firebase_options.dart';
import 'package:cmtmeet/pages/call_screen.dart';
import 'package:cmtmeet/pages/splash_screen.dart';
import 'package:cmtmeet/service/call_service.dart';
import 'package:cmtmeet/service/connection_service.dart';
import 'package:cmtmeet/service/firebase_service.dart';
// import 'package:cmtmeet/service/firebase_service_china.dart';
import 'package:cmtmeet/service/localization_service.dart';
import 'package:cmtmeet/service/token_manager.dart';
import 'package:cmtmeet/service/websocket_service.dart';
import 'package:cmtmeet/utils/app_routes.dart';
import 'package:cmtmeet/utils/constants.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

final GlobalKey<ScaffoldMessengerState> scaffoldMessengerKey =
    GlobalKey<ScaffoldMessengerState>();

Map<String, dynamic>? _initialCallData;
bool _appColdStarted = true;

void main() async {
  WidgetsBinding widgetsBinding = WidgetsFlutterBinding.ensureInitialized();
  await GetStorage.init();
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  initializeFirebaseMessaging();

  FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
  if (Platform.isAndroid) {
    const MethodChannel _channel =
        MethodChannel('com.cmtevents.cmtmeet/call_service');
    _channel.setMethodCallHandler((MethodCall call) async {
      print("Method from Native: ${call.method}");
      print("Arguments from Native Side: ${call.arguments}");
      if (call.method == 'cold_launch_call') {
        final String json = call.arguments;
        final data = jsonDecode(json);
        _initialCallData = Map<String, dynamic>.from(data);
      } else if (call.method == 'launch_call') {
        final Map<String, dynamic> callData =
            Map<String, dynamic>.from(call.arguments);
        print("✅ Received call data from native: $callData");

        _initialCallData = callData;

        if (Get.isRegistered<GetMaterialController>()) {
          print("Not cold start");
          _appColdStarted = false;
          userBusyStatus.value = true;
          Get.to(() => CallScreen(
                calleeAgoraId: callData['callerAgoraId'] ?? '',
                calleeName: callData['callerName'] ?? '',
                calleeJob: callData['callerJob'] ?? '',
                calleeCompany: callData['callerCompany'] ?? '',
                fcmToken: callData['fcmToken'] ?? '',
                jwtToken: callData['jwtToken'] ?? '',
                channelName: callData['channelName'] ?? '',
                token: callData['token'] ?? '',
                callUUID: callData['callUUID'] ?? '',
                status: 'attended',
                callType: callData['callType'] ?? '',
                callInitiator: false,
                fromColdStart: false,
              ));
        }
      } else if (call.method == "onCallStatusChanged") {
        final status = call.arguments['status'] as String;
        final CallService _callService = CallService();
        print(status);
        if (status == "BUSY") {
          Timer.periodic(Duration(milliseconds: 100), (timer) {
            print("Trying");
            if (CallService().callUIReady) {
              print("Passed");
              CallService().handleBusyStatus();
              timer.cancel();
            }
          });
        } else if (status == "DECLINED") {
          final callUUID = call.arguments['callUUID'] as String;
          if (_callService.callUUID == callUUID) {
            CallService().handleDeclineStatus();
          }
        }
      } else if (call.method == "hang_up") {
        CallService().endCall();
      } else if (call.method == "openCallScreen") {
        final CallService _callService = CallService();
        Get.to(() => CallScreen(
              calleeAgoraId: _callService.calleeAgoraId,
              calleeName: _callService.calleeName,
              calleeJob: _callService.calleeJob,
              calleeCompany: _callService.calleeCompany,
              fcmToken: _callService.fcmToken,
              jwtToken: _callService.jwtToken,
              channelName: _callService.channelName,
              token: '', // Add rtcToken if needed
              callUUID: _callService.callUUID,
              status: 'ongoing',
              callType: _callService.callType,
              callInitiator: _callService.callInitiator,
              fromColdStart: false,
            ));
      }
    });
  }
  print(
      "_appColdStarted: $_appColdStarted, _initialCallData: $_initialCallData");

  _initializeChatClient();

  Get.put<WebSocketService>(WebSocketService());
  Get.put<ConnectionService>(ConnectionService());
  WidgetsBinding.instance.addObserver(AppLifecycleObserver());

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      scaffoldMessengerKey: scaffoldMessengerKey,
      debugShowCheckedModeBanner: false,
      translations: LocalizationService(),
      locale: const Locale('en', 'US'),
      fallbackLocale: const Locale('en', 'US'),
      home: _appColdStarted && _initialCallData != null
          ? InitialCallScreenLoader(callData: _initialCallData!)
          : LaunchRouter(),
      getPages: AppPages.routes,
    );
  }
}

class InitialCallScreenLoader extends StatefulWidget {
  final Map<String, dynamic> callData;

  const InitialCallScreenLoader({super.key, required this.callData});

  @override
  State<InitialCallScreenLoader> createState() =>
      _InitialCallScreenLoaderState();
}

class _InitialCallScreenLoaderState extends State<InitialCallScreenLoader> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      FlutterNativeSplash.remove();

      print("🚀 Navigating to CallScreen with data: ${widget.callData}");
      print("Cold start");
      userBusyStatus.value = true;

      Get.offAll(() => CallScreen(
            calleeAgoraId: widget.callData['callerAgoraId'] ?? '',
            calleeName: widget.callData['callerName'] ?? '',
            calleeJob: widget.callData['callerJob'] ?? '',
            calleeCompany: widget.callData['callerCompany'] ?? '',
            fcmToken: widget.callData['fcmToken'] ?? '',
            jwtToken: widget.callData['jwtToken'] ?? '',
            channelName: widget.callData['channelName'] ?? '',
            token: widget.callData['token'] ?? '',
            callUUID: widget.callData['callUUID'] ?? '',
            status: 'attended',
            callType: widget.callData['callType'] ?? '',
            callInitiator: false,
            fromColdStart: true,
          ));
    });
  }

  @override
  Widget build(BuildContext context) {
    // Return an empty or loading widget as this widget's only purpose is to
    // trigger navigation and then it will be replaced by the CallScreen.
    return const Scaffold(
      body: Center(
        child: CircularProgressIndicator(),
      ),
    );
  }
}

void _initializeChatClient() async {
  try {
    var options = ChatOptions(appKey: Constants.appKey, autoLogin: true);
    // if (Platform.isIOS) {
    //   options.enableAPNs(Constants.fcmServiceToken);
    // } else {
    //   options.enableFCM(Constants.fcmServiceToken);
    // }
    await ChatClient.getInstance.init(options);

    print('ChatClient initialized with autoLogin');
  } catch (e) {
    print('ChatClient initialization failed: $e');
    rethrow;
  }
}

class AppLifecycleObserver extends WidgetsBindingObserver {
  @override
  Future<void> didChangeAppLifecycleState(AppLifecycleState state) async {
    final webSocketService = Get.find<WebSocketService>();

    switch (state) {
      case AppLifecycleState.resumed:
        await TokenManager.handleAppResume();
        await Get.find<ConnectionService>().handleAppResume();
        break;

      case AppLifecycleState.detached:
        webSocketService.closeConnection();
        print("App detached - WebSocket closed");
        break;

      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
      case AppLifecycleState.hidden:
        break;
    }
  }
}
