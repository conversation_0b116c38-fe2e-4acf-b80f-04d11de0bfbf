import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';

class AgendaPage extends StatefulWidget {
  const AgendaPage({super.key});

  @override
  State<AgendaPage> createState() => _AgendaPageState();
}

class _AgendaPageState extends State<AgendaPage> {
  final GetStorage storage = GetStorage();
  List<dynamic> agenda = [];
  int selectedDayIndex = 0;
  String? selectedTrack;

  @override
  void initState() {
    super.initState();
    final storedData = storage.read("eventSchedule") ?? [];
    setState(() {
      agenda = storedData;
    });
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: DefaultTabController(
          length: agenda[0]["eventDurationDays"],
          child: Column(
            children: [
              Container(
                color: Colors.white,
                child: TabBar(
                  tabAlignment: TabAlignment.start,
                  isScrollable: true,
                  indicatorColor: Color.fromARGB(255, 175, 16, 82),
                  labelColor: Color.fromARGB(255, 175, 16, 82),
                  unselectedLabelColor: Colors.black54,
                  labelPadding: EdgeInsets.symmetric(horizontal: 10.0),
                  onTap: (index) {
                    setState(() {
                      selectedDayIndex = index;
                    });
                  },
                  tabs: getDistinctFromDates(agenda)
                      .map((date) => Tab(text: formatDate(date)))
                      .toList(),
                ),
              ),

              // Expanded Tab Content
              Expanded(
                child: TabBarView(
                  children: getDistinctFromDates(agenda)
                      .asMap()
                      .keys
                      .map((index) => _buildAgendaDay(index + 1))
                      .toList(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAgendaDay(int selectedDay) {
    List<dynamic> dayAgenda =
        agenda.where((session) => session["eventDay"] == selectedDay).toList();

    final todaySessions = [];

    for (final session in dayAgenda) {
      final type = session["sessionType"];

      if (type == 'speech' || type == 'Chairman') {
        final parentId = session["parentTopicId"];
        final key = parentId ?? session["topicId"];

        // Check if session with same topic already exists
        final existingIndex = todaySessions.indexWhere((s) =>
            (s["sessionType"] == 'speech' || s["sessionType"] == 'Chairman') &&
            (s["topicId"] == key));

        Map<String, dynamic>? speaker;
        if (session["speakerId"] != null) {
          speaker = {
            "speakerId": session["speakerId"],
            "profileImage": session["profileImage"],
            "speakerFullName": session["speakerFullName"],
            "speakerJobTitle": session["speakerJobTitle"],
            "speakerCompany": session["speakerCompany"],
          };
        }

        if (existingIndex != -1) {
          // Append speaker to existing session
          todaySessions[existingIndex]["speakers"].add(speaker);
        } else {
          // Create new speech session
          todaySessions.add({
            "sessionType": type,
            "topicId": key,
            "topicTitle": session["topicTitle"],
            "scheduledTime": session["scheduledTime"],
            "scheduledTimeOnly": session["scheduledTimeOnly"],
            "speakers": speaker != null ? [speaker] : [],
          });
        }
      } else {
        todaySessions.add(session);
      }
    }

    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(15.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: todaySessions.map((session) {
            if (session["sessionType"] == 'tab') {
              return _buildSessionCardTab(session);
            } else if (session["sessionType"] == 'Remark') {
              return session["topicTitle"] != null
                  ? _buildSessionCardRemark(session)
                  : SizedBox();
            } else if (session["sessionType"] == 'breaks') {
              return _buildSessionCardBreak(session);
            }
            return _buildSessionCard(session);
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildSessionCardTab(dynamic session) {
    final sessionType = session['sessionType'];
    return Card(
      margin: EdgeInsets.only(bottom: 15),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(7.0),
      ),
      elevation: sessionType == 'tab' ? 0 : 3,
      color: sessionType == 'tab' ? Color(0xffc01058) : Colors.white,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 10.0),
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(10.0),
            topRight: Radius.circular(10.0),
          ),
        ),
        child: Text(
          session["topicTitle"],
          style: TextStyle(
            color: sessionType == 'tab' ? Colors.white : Colors.black,
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildSessionCardRemark(dynamic session) {
    return Container(
      padding: EdgeInsets.only(bottom: 15.0),
      child: Text(
        session["topicTitle"],
        style: TextStyle(
            color: Color(0xffc01058),
            fontSize: 16,
            fontWeight: FontWeight.bold,
            decoration: TextDecoration.underline),
      ),
    );
  }

  Widget _buildSessionCardBreak(dynamic session) {
    return Card(
      margin: EdgeInsets.only(bottom: 15),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(7.0),
      ),
      elevation: 3,
      color: Colors.white,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 10.0),
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(10.0),
            topRight: Radius.circular(10.0),
          ),
        ),
        child: Text(
          '${session["scheduledTimeOnly"]} - ${session["topicTitle"]}',
          style: TextStyle(
            color: Colors.black,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildSessionCard(dynamic session) {
    final localTime = formatToDeviceLocalTime(session['scheduledTime']);
    return Card(
      margin: EdgeInsets.only(bottom: 20),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10.0),
      ),
      elevation: 2,
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Session Time Header
          Container(
            padding:
                const EdgeInsets.symmetric(vertical: 8.0, horizontal: 10.0),
            decoration: const BoxDecoration(
              color: Colors.black54,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(10.0),
                topRight: Radius.circular(10.0),
              ),
            ),
            child: Text(
              "${session["scheduledTimeOnly"]} | Local Time: $localTime",
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

          const SizedBox(height: 10),

          // Topic Title
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10.0),
            child: Text(
              session["topicTitle"] ?? '',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),
          ),

          const SizedBox(height: 5),

          // Multiple Speakers
          if (session["speakers"] != null)
            ...session["speakers"].map<Widget>((speaker) {
              return Padding(
                padding: const EdgeInsets.symmetric(
                    horizontal: 10.0, vertical: 10.0),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Profile Image or Icon
                    Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                            color: Color.fromARGB(255, 97, 97, 97), width: 1),
                      ),
                      child: ClipOval(
                        child: '${speaker["profileImage"]}'
                                .endsWith('speaker_default.jpg')
                            ? Icon(
                                Icons.account_circle_rounded,
                                size: 50,
                                color: Colors.grey[500],
                              )
                            : Image.network(
                                '${speaker["profileImage"]}',
                                width: 50,
                                height: 50,
                                fit: BoxFit.cover,
                              ),
                      ),
                    ),
                    const SizedBox(width: 10),

                    // Speaker Info
                    Flexible(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            speaker["speakerFullName"] ?? '',
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          if (speaker["speakerJobTitle"] != null)
                            Text(
                              speaker["speakerJobTitle"],
                              style: TextStyle(
                                  fontSize: 12, color: Colors.black54),
                            ),
                          if (speaker["speakerCompany"] != null)
                            Text(
                              speaker["speakerCompany"],
                              style: TextStyle(
                                  fontSize: 12, color: Colors.black54),
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),

          const SizedBox(height: 10),
        ],
      ),
    );
  }

  List<String> getDistinctFromDates(List<dynamic> data) {
    Set<String> seen = {};
    return data
        .map((e) => e["fromDate"] as String)
        .where((date) => seen.add(date))
        .toList();
  }

  String formatDate(String inputDate) {
    DateTime date = DateFormat("dd MMM yyyy HH:mm:ss").parse(inputDate);
    return DateFormat("dd MMMM yyyy").format(date);
  }

  String formatToDeviceLocalTime(String scheduledTime) {
    try {
      final timeParts = scheduledTime.split(' ');
      final timeString = timeParts[3];
      final tzOffset = timeParts[4].replaceAll('GMT', '');

      final timeComponents = timeString.split(':');
      final hour = int.parse(timeComponents[0]);
      final minute = int.parse(timeComponents[1]);

      final offsetSign = tzOffset[0] == '+' ? 1 : -1;
      final offsetHours = int.parse(tzOffset.substring(1, 3));
      final offsetMinutes = int.parse(tzOffset.substring(3, 5));
      final totalOffsetMinutes =
          offsetSign * (offsetHours * 60 + offsetMinutes);

      final deviceOffsetMinutes = DateTime.now().timeZoneOffset.inMinutes;

      final timeDifferenceMinutes = totalOffsetMinutes - deviceOffsetMinutes;

      final adjustedTime = DateTime(2000, 1, 1, hour, minute)
          .add(Duration(minutes: -timeDifferenceMinutes));

      return '${adjustedTime.hour.toString().padLeft(2, '0')}:'
          '${adjustedTime.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      debugPrint('Time conversion error: $e');
      return scheduledTime.split(' ')[3].substring(0, 5);
    }
  }
}
