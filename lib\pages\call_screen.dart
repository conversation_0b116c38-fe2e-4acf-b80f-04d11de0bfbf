import 'dart:async';
import 'dart:io';

import 'package:agora_rtc_engine/agora_rtc_engine.dart';
import 'package:cmtmeet/service/call_service.dart';
import 'package:cmtmeet/service/firebase_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

class CallScreen extends StatefulWidget {
  final String calleeAgoraId;
  final String callUUID;
  final String calleeName;
  final String calleeJob;
  final String calleeCompany;
  final String callType;
  final String channelName;
  final String token;
  final String fcmToken;
  final String jwtToken;
  final String status;
  final bool callInitiator;
  final bool fromColdStart;
  CallScreen({
    required this.calleeAgoraId,
    required this.callUUID,
    required this.calleeName,
    required this.calleeJob,
    required this.calleeCompany,
    required this.callType,
    required this.channelName,
    required this.token,
    required this.fcmToken,
    required this.jwtToken,
    required this.status,
    required this.callInitiator,
    required this.fromColdStart,
  });

  @override
  State<CallScreen> createState() => _CallScreenState();
}

class _CallScreenState extends State<CallScreen> {
  StreamSubscription<int>? _callDurationSubscription;
  final CallService _callService = CallService();
  bool userJoined = false;
  bool isCallDeclined = false;
  bool isCallEnded = false;
  bool isCalleeBusy = false;
  bool isRemoteAudioEnabled = true;
  bool isRemoteVideoEnabled = true;

  @override
  void initState() {
    super.initState();
    _callService.assignCallData(
        callUUID: widget.callUUID,
        calleeAgoraId: widget.calleeAgoraId,
        calleeName: widget.calleeName,
        calleeJob: widget.calleeJob,
        calleeCompany: widget.calleeCompany,
        callType: widget.callType,
        channelName: widget.channelName,
        fcmToken: widget.fcmToken,
        jwtToken: widget.jwtToken,
        callInitiator: widget.callInitiator);
    // setUserBusyStatus(true);
    print("User busy Status: ${userBusyStatus.value}");
    print("Cold Start: ${widget.fromColdStart}");
    if (widget.status != 'ongoing') {
      _initializeCall();
    }
    _setupCallServiceListeners();
    _setupDurationListener();
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
  }

  @override
  void dispose() {
    _callDurationSubscription?.cancel();

    // ❗️Clear all listeners
    _callService.onUserJoined = null;
    _callService.onRemoteAudioMuted = null;
    _callService.onRemoteVideoMuted = null;
    _callService.onUserOffline = null;
    _callService.onEndCall = null;
    _callService.onBusyStatusReceived = null;
    _callService.onDeclineStatusReceived = null;

    // Reset preferred orientations
    SystemChrome.setPreferredOrientations(DeviceOrientation.values);
    super.dispose();
  }

  void _setupDurationListener() {
    _callDurationSubscription = _callService.callDurationStream.listen((duration) {
      if (mounted) {
        setState(() {});
      }
    });
  }

  Future<void> _initializeCall() async {
    try {
      if (widget.status == 'ringing') {
        await _callService.startRinging();
      }
      await _callService.initializeAgora();
      await Future.delayed(Duration(milliseconds: 300));

      await _acceptCall();
    } catch (e) {
      print("Initialize Error: $e");
      Get.back();
    }
  }

  void _setupCallServiceListeners() {
    setState(() {
      if (_callService.acceptedCall) {
        userJoined = true;
      } else {
        _callService.onUserJoined = () {
          userJoined = true;
        };
      }
    });

    _callService.onRemoteAudioMuted = (isMuted) {
      setState(() {
        isRemoteAudioEnabled = !isMuted;
      });
    };

    _callService.onRemoteVideoMuted = (isMuted) {
      setState(() {
        isRemoteVideoEnabled = !isMuted;
      });
    };

    _callService.onUserOffline = () {
      setState(() {
        if (!_callService.isCallEnded) {
          endCall();
        }
      });
    };

    _callService.onEndCall = () {
      endCall(notify: true);
    };

    _callService.onBusyStatusReceived = () {
      busyCall(); // Trigger the busy flow
    };

    _callService.onDeclineStatusReceived = () {
      declineCall(init: false);
    };

    _callService.markCallUIReady();
  }

  Future<void> _acceptCall() async {
    await _callService.joinChannel(
      token: widget.token,
      channelName: widget.channelName,
      isVideoCall: widget.callType == 'video',
    );
  }

  void declineCall({init = true}) {
    if (isCallDeclined) return;
    setState(() {
      isCallDeclined = true;
    });
    if (init) {
      _callService.declineCall();
    }
    debugPrint("📞 Call declined, navigating in 2 seconds...");
    Future.delayed(Duration(seconds: 2), () {
      isCallDeclined = false;
      Get.back();
    });
  }

  void endCall({notify = false}) {
    debugPrint("📞 [endCall] Called with notify: $notify");

    if (isCallEnded) {
      debugPrint("📞 [endCall] Already ended, skipping.");
      return;
    }

    debugPrint("📞 [endCall] Marking call as ended...");
    setState(() {
      isCallEnded = true;
    });

    if (!notify) {
      debugPrint("📞 [endCall] Calling _callService.endCall()");
      _callService.endCall();
    } else {
      debugPrint("📞 [endCall] notify=true, skipping _callService.endCall()");
    }

    debugPrint("📞 [endCall] Navigating away in 2 seconds...");
    Future.delayed(const Duration(seconds: 2), () {
      debugPrint("📞 [endCall → delayed] Triggered after 2s");
      isCallEnded = false;
      debugPrint("📞 [endCall → delayed] isCallEnded reset");

      if (!widget.callInitiator) {
        debugPrint("📞 [endCall → delayed] Callee flow");

        if (widget.fromColdStart) {
          debugPrint("📞 [endCall → delayed] fromColdStart = true → LaunchRouter");
          if (Platform.isAndroid) {
            debugPrint("📞 [endCall] Killing app with Process.killProcess");
            SystemChannels.platform.invokeMethod('SystemNavigator.pop'); // Optional
            Future.delayed(Duration(milliseconds: 300), () {
              exit(0); // Actually terminates app process
            });
          } else if (Platform.isIOS) {
            debugPrint("📞 [endCall → delayed] iOS → exit(0)");
            // Try commenting this out:
            exit(0);
          }
        } else {
          debugPrint("📞 [endCall → delayed] fromColdStart = false → Get.back()");
          Get.back();
        }
      } else {
        debugPrint("📞 [endCall → delayed] Caller flow → Get.back()");
        Get.back();
      }
    });
  }

  void busyCall() {
    if (isCalleeBusy) return;
    setState(() {
      isCalleeBusy = true;
    });
    _callService.stopRinging();

    Future.delayed(Duration(seconds: 2), () {
      isCalleeBusy = false;
      Get.back();
    });
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: Color.fromRGBO(25, 25, 25, 1),
        body: Container(
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height,
          child: Stack(
            children: [
              if (widget.callType == 'video') _buildMainVideoView(),
              if (widget.callType == 'video')
                if (_callService.remoteUid != null && _callService.joinedCall)
                  _buildSecondaryVideoView(),
              Container(
                padding: EdgeInsets.symmetric(vertical: 30),
                width: MediaQuery.of(context).size.width,
                height: MediaQuery.of(context).size.height,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildCallStatus(),
                    if (widget.callType == 'audio') _buildUserInfo(),
                    _buildCallControls(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMainVideoView() {
    return Center(
      child: _callService.remoteUid != null
          ? isRemoteVideoEnabled
              ? AgoraVideoView(
                  controller: VideoViewController.remote(
                    rtcEngine: _callService.engine!,
                    canvas: VideoCanvas(uid: _callService.remoteUid),
                    connection: RtcConnection(channelId: widget.channelName),
                  ),
                )
              : _buildBlankView()
          : _callService.joinedCall
              ? AgoraVideoView(
                  controller: VideoViewController(
                    rtcEngine: _callService.engine!,
                    canvas: const VideoCanvas(uid: 0),
                  ),
                )
              : const Center(child: CircularProgressIndicator()),
    );
  }

  Widget _buildSecondaryVideoView() {
    return Align(
      alignment: Alignment.bottomRight,
      child: Container(
        margin: const EdgeInsets.only(bottom: 110, right: 20),
        width: 150,
        height: 200,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          border: Border.all(color: Colors.white, width: 2),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(10),
          child: _callService.isVideoOn
              ? AgoraVideoView(
                  controller: VideoViewController(
                    rtcEngine: _callService.engine!,
                    canvas: const VideoCanvas(uid: 0),
                  ),
                )
              : _buildBlankView(),
        ),
      ),
    );
  }

  Widget _buildUserInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Icon(Icons.account_circle, size: 130, color: Color.fromRGBO(154, 151, 151, 1)),
        Text(widget.calleeName, style: TextStyle(color: Colors.white, fontSize: 20)),
        if (widget.calleeJob != '')
          Text(widget.calleeJob,
              style: TextStyle(color: Color.fromRGBO(191, 191, 191, 1), fontSize: 12)),
        Text(widget.calleeCompany,
            style: TextStyle(color: Color.fromRGBO(191, 191, 191, 1), fontSize: 12)),
        SizedBox(height: 40),
        !isRemoteAudioEnabled
            ? Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.mic_off_sharp, size: 20, color: Colors.red),
                  SizedBox(width: 10),
                  Text('Remote User Muted',
                      style: TextStyle(color: Colors.white, fontSize: 12)),
                ],
              )
            : SizedBox(),
      ],
    );
  }

  Widget _buildCallControls() {
    return Container(
        width: MediaQuery.of(context).size.width * 0.85, child: _buildControlButtons());
  }

  Widget _buildCallStatus() {
    const whiteText15 = TextStyle(color: Colors.white, fontSize: 15);
    const whiteText20 = TextStyle(color: Colors.white, fontSize: 20);
    const whiteText21 = TextStyle(color: Colors.white, fontSize: 21);
    const whiteText22 = TextStyle(color: Colors.white, fontSize: 22);
    const greyText15 = TextStyle(color: Color.fromRGBO(191, 191, 191, 1), fontSize: 15);
    const redText12 = TextStyle(color: Colors.red, fontSize: 12);
    const whiteText12 = TextStyle(color: Colors.white, fontSize: 12);
    const redText18 = TextStyle(color: Colors.red, fontSize: 18);

    if (isCallEnded) {
      return Text('Call Ended', style: redText18);
    }

    if (isCallDeclined) {
      return Text('Call Declined', style: redText18);
    }

    if (isCalleeBusy) {
      return Column(
        children: [
          Text('Busy with another call', style: redText18),
          Text(widget.calleeName, style: whiteText21),
        ],
      );
    }

    if (widget.status == 'ringing' && !_callService.acceptedCall) {
      return widget.callType == 'video'
          ? Column(
              children: [
                Text('Calling...', style: whiteText20),
                Text(widget.calleeName,
                    style: TextStyle(color: Colors.white, fontSize: 21)),
              ],
            )
          : Text('Ringing...', style: whiteText20);
    }

    if (widget.callType == 'video') {
      return _callService.acceptedCall
          ? Column(
              children: [
                Text(widget.calleeName, style: whiteText22),
                Text(_formatDuration(_callService.callDurationInSeconds),
                    style: whiteText15),
                if (!isRemoteAudioEnabled) ...[
                  SizedBox(height: 10),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.mic_off_sharp, size: 20, color: Colors.red),
                      SizedBox(width: 5),
                      Text('Remote User Mic Muted', style: redText12),
                    ],
                  ),
                ],
              ],
            )
          : Column(
              children: [
                Text(widget.calleeName, style: whiteText22),
                Text('Calling...', style: whiteText15),
              ],
            );
    }

    return _callService.acceptedCall
        ? Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text('Connected', style: whiteText22),
              Text(_formatDuration(_callService.callDurationInSeconds),
                  style: greyText15),
              if (!isRemoteAudioEnabled) ...[
                SizedBox(height: 15),
                Center(
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.mic_off_sharp, size: 15, color: Colors.white),
                        SizedBox(width: 5),
                        Text('Remote User Muted', style: whiteText12),
                      ],
                    ),
                  ),
                ),
              ] else ...[
                SizedBox(height: 25),
              ],
            ],
          )
        : Text('Calling...', style: whiteText20);
  }

  Widget _buildControlButtons() {
    return Container(
      height: MediaQuery.of(context).size.height * 0.08,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Color.fromRGBO(32, 44, 51, 1),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          if (widget.callType == 'video')
            _buildCircleButton(
              icon: _callService.isVideoOn
                  ? Icons.videocam_sharp
                  : Icons.videocam_off_sharp,
              isActive: _callService.isVideoOn,
              onTap: _callService.toggleVideo,
            ),
          ValueListenableBuilder<bool>(
            valueListenable: _callService.isSpeakerOn,
            builder: (_, isActive, __) => _buildCircleButton(
              icon: isActive ? Icons.volume_up_sharp : Icons.volume_off_sharp,
              isActive: isActive,
              onTap: _callService.toggleSpeaker,
            ),
          ),
          ValueListenableBuilder<bool>(
            valueListenable: _callService.isMicOn,
            builder: (_, isActive, __) => _buildCircleButton(
              icon: isActive ? Icons.mic_sharp : Icons.mic_off_sharp,
              isActive: isActive,
              onTap: _callService.toggleMute,
            ),
          ),
          if (widget.callType == 'video')
            _buildCircleButton(
              icon: Icons.cameraswitch,
              isActive: _callService.isFrontCam,
              onTap: _callService.switchCamera,
            ),
          _buildCircleButton(
              icon: Icons.phone_sharp,
              color: Colors.red,
              isActive: false,
              onTap: () {
                if (_callService.acceptedCall) {
                  if (!isCallEnded) {
                    endCall();
                  }
                } else {
                  if (!isCallDeclined) {
                    declineCall();
                  }
                }
              }),
        ],
      ),
    );
  }

  Widget _buildCircleButton({
    required IconData icon,
    required VoidCallback onTap,
    bool isActive = true,
    Color color = const Color.fromRGBO(42, 57, 66, 1),
  }) {
    return GestureDetector(
      onTap: onTap,
      child: CircleAvatar(
        radius: 24,
        backgroundColor: isActive ? Colors.white : color,
        child: Icon(icon, color: isActive ? Colors.black : Colors.white, size: 27),
      ),
    );
  }

  Widget _buildBlankView() {
    return Container(
      color: Colors.black,
      child: Center(
        child: Icon(
          Icons.videocam_off_sharp,
          size: 70,
          color: Colors.red,
        ),
      ),
    );
  }

  String _formatDuration(int seconds) {
    final minutes = (seconds ~/ 60).toString().padLeft(2, '0');
    final remainingSeconds = (seconds % 60).toString().padLeft(2, '0');
    return '$minutes:$remainingSeconds';
  }
}
