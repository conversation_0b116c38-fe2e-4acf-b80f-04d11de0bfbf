import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:cmtmeet/service/call_service.dart';
import 'package:cmtmeet/service/caller_tone_service.dart';
import 'package:cmtmeet/service/callkit_manager.dart';
import 'package:cmtmeet/service/firebase_service.dart';
import 'package:cmtmeet/utils/constants.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:agora_rtc_engine/agora_rtc_engine.dart';
import 'package:flutter/services.dart';
import 'package:flutter_ringtone_player/flutter_ringtone_player.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:get_storage/get_storage.dart';
import 'package:permission_handler/permission_handler.dart';

Function()? onCallDeclinedGlobal;
Function()? onCallBusyGlobal;

class CallScreen extends StatefulWidget {
  final String callerAgoraId;
  final String callerName;
  final String callerJob;
  final String callerCompany;
  final String fcmToken;
  final String jwtToken;
  final String channelName;
  final String token;
  final String callUUID;
  final String status;
  final String callType;
  final Function(String, String, String)? onExit;

  CallScreen({
    required this.callerAgoraId,
    required this.callerName,
    required this.callerJob,
    required this.callerCompany,
    required this.fcmToken,
    required this.jwtToken,
    required this.channelName,
    required this.token,
    required this.callUUID,
    required this.status,
    required this.callType,
    this.onExit,
  });

  @override
  State<CallScreen> createState() => _CallScreenState();
}

class _CallScreenState extends State<CallScreen> with WidgetsBindingObserver {
  late String userAgoraId;
  int? _remoteUid;
  bool _isRemoteVideoEnabled = true;
  bool _isRemoteAudioEnabled = true;
  final storage = GetStorage();
  late RtcEngine _engine;
  bool _joinedCall = false;
  bool _acceptedCall = false;
  bool _isCallDeclined = false; // Track if the call is declined
  bool _isCallEnded = false;
  bool isUserBusy = false;
  bool isVideoOn = false;
  bool isSpeakerOn = false;
  bool isMicOn = true;
  bool isFrontCam = true;
  bool _isDisposing = false;
  bool _declineSent = false;

  // Timer and call duration variables
  Timer? _callTimer;
  Timer? _declineTimer;
  int _callDurationInSeconds = 0;

  // Audio player for ringing
  // final CallTonePlayer _callTone = CallTonePlayer();
  bool _isRinging = false;

  // StreamSubscription<RemoteMessage>? _fcmSubscription;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    userAgoraId = storage.read("user")["agoraid"];
    // _configureAudioSession();

    // Request permissions before initializing Agora
    _requestPermissions(widget.callType == 'video').then((granted) {
      if (granted) {
        print("Permission Granted and Initializing Agora");
        _initializeAgora();
        if (widget.status == 'receiving' || widget.status == 'ringing') {
          print("Request Permisssion Granted");
          if (mounted) {
            _startRinging();
          }
        }
      } else {
        if (mounted) {
          Get.back();
        }
      }
    });

    onCallBusyGlobal = () {
      _handleCallBusy();
    };

    onCallDeclinedGlobal = () {
      _handleCallDeclined();
    };

    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
  }

  @override
  void dispose() {
    _cleanUpCall();
    onCallDeclinedGlobal = null;
    _declineTimer?.cancel();
    // isCallPageOpen = false;
    SystemChrome.setPreferredOrientations(DeviceOrientation.values);
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  // Start ringing
  Future<void> _startRinging() async {
    print("Start Ringing");
    if (_isRinging) return;
    _isRinging = true;

    try {
      if (widget.status == 'receiving') {
        // Receiver: loudspeaker ringtone
        FlutterRingtonePlayer().play(
          android: AndroidSounds.ringtone,
          ios: IosSounds.glass,
          looping: true,
          volume: 1.0,
          asAlarm: false,
        );
      } else if (widget.status == 'ringing') {
        // Caller: custom ringtone in earpiece
        // print("Ringing");
        // await _configureAudioSession();
        // await _audioSession.setActive(true);
        // await _audioPlayer.stop();

        // await _audioPlayer.setReleaseMode(ReleaseMode.loop);
        // await _audioPlayer.setPlayerMode(PlayerMode.lowLatency);
        // await _audioPlayer.setSource(AssetSource('ring.mp3'));
        // await _audioPlayer.resume();
        // _callTone.start();
        await NativeCallerTone.start();
        await NativeCallerTone.switchOutput(false);
      }
    } catch (e) {
      debugPrint('Error starting ringtone: $e');
      _isRinging = false;
    }
  }

  Future<void> _stopRinging() async {
    print("Stop Ringing");
    if (!_isRinging) return;
    _isRinging = false;

    try {
      if (widget.status == 'receiving') {
        await FlutterRingtonePlayer().stop();
      } else if (widget.status == 'ringing') {
        // if (_audioPlayer.state == PlayerState.playing) {
        //   await _audioPlayer.stop();
        // }
        // if (!_acceptedCall) {
        //   await _audioSession.setActive(false);
        // }
        // _callTone.stop();
        await NativeCallerTone.stop();
      }
    } catch (e) {
      debugPrint('Error stopping ringtone: $e');
    } finally {
      _isRinging = false;
    }
  }

  void _handleCallBusy() {
    print("Stop due to busy");
    _stopRinging();
    if (mounted) {
      setState(() => isUserBusy = true);
    }

    _declineTimer = Timer(const Duration(seconds: 2), () {
      // Cancel the timer first
      _declineTimer?.cancel();

      // Reset the declined state (optional, since we're leaving)
      if (mounted) {
        setState(() => isUserBusy = false);
        _cleanUpCall(isDeclined: true);
        WidgetsBinding.instance.addPostFrameCallback((_) {
          widget.onExit?.call(widget.callType, 'missed_call', '0');
        });
        Get.back();
      }

      // Perform cleanup and navigate back
    });

    // Ensure timer is cancelled if this function is called again
    onCallBusyGlobal = () {
      _declineTimer?.cancel();
      if (mounted) {
        _handleCallBusy();
      }
    };
  }

  void _handleCallDeclined() {
    // Early return if already processing or disposed
    if (_declineSent || _isDisposing || !mounted || _acceptedCall) return;
    print("Call Declined");

    // Mark as declined to prevent duplicate calls
    _declineSent = true;

    // Immediately stop ringing
    _stopRinging();

    // Show visual feedback that call was declined
    if (mounted) {
      setState(() => _isCallDeclined = true);
    }

    // Setup cleanup timer
    _declineTimer = Timer(const Duration(seconds: 2), () {
      // Cancel the timer first
      _declineTimer?.cancel();

      // Reset the declined state (optional, since we're leaving)
      if (mounted) {
        setState(() => _isCallDeclined = false);
        _cleanUpCall(isDeclined: true);
        WidgetsBinding.instance.addPostFrameCallback((_) {
          widget.onExit?.call(widget.callType, 'call', '0');
        });
        Get.back();
      }

      // Perform cleanup and navigate back
    });

    // Ensure timer is cancelled if this function is called again
    onCallDeclinedGlobal = () {
      _declineTimer?.cancel();
      if (!_declineSent && mounted) {
        _handleCallDeclined();
      }
    };
  }

  Future<void> _initializeAgora() async {
    try {
      _engine = createAgoraRtcEngine();
      await _engine.initialize(RtcEngineContext(appId: Constants.appId));
      // await startCallForegroundService();
      if (!mounted) return;
      // Register event handlers
      _engine.registerEventHandler(
        RtcEngineEventHandler(
          onJoinChannelSuccess: (RtcConnection connection, int elapsed) async {
            debugPrint("Local user joined the channel");

            if (widget.callType == 'audio') _engine.setEnableSpeakerphone(false);

            if (mounted) setState(() => _joinedCall = true);
          },
          onUserJoined: (RtcConnection connection, int remoteUid, int elapsed) {
            debugPrint("Remote user joined with UID: $remoteUid");
            setState(() {
              _remoteUid = remoteUid;
              _acceptedCall = true;
              _startCallTimer(); // Start the timer when the call is joined
              _stopRinging(); // Stop ringing when the call is accepted
            });
          },
          onUserMuteAudio: (connection, remoteUid, muted) {
            setState(() {
              print("Remote user audio muted: $muted, remoteUid: $remoteUid");
              _isRemoteAudioEnabled = !muted;
            });
          },
          onUserMuteVideo: (connection, remoteUid, muted) {
            setState(() {
              print("Remote user video muted: $muted, remoteUid: $remoteUid");
              _isRemoteVideoEnabled = !muted;
            });
          },
          onUserOffline:
              (RtcConnection connection, int remoteUid, UserOfflineReasonType reason) {
            debugPrint("Remote user left with UID: $remoteUid");
            setState(() => _remoteUid = null);
            if (!_isCallEnded && mounted) {
              _endCall();
            }
          },
          onError: (ErrorCodeType err, String msg) {
            debugPrint("Agora Error: $msg and code: $err");
          },
        ),
      );

      // Enable audio for both call types
      await _engine.enableAudio();

      // Enable video only if it's a video call
      if (widget.callType == 'video') {
        isVideoOn = true;
        isSpeakerOn = true;
        await _engine.enableVideo();
        await _engine.startPreview();
      }

      // If the caller is initiating the call, join the channel immediately
      if (widget.status == 'ringing' || widget.status == 'attended') {
        _acceptCall();
      }
    } catch (e) {
      debugPrint("Failed to initialize Agora: $e");
      if (mounted) {
        Get.back();
      }
    }
  }

  // Start the call timer
  void _startCallTimer() {
    _callTimer = Timer.periodic(Duration(seconds: 1), (timer) {
      setState(() {
        _callDurationInSeconds++;
      });
    });
  }

  // Stop the call timer
  void _stopCallTimer() {
    _callTimer?.cancel();
    _callTimer = null;
    _callDurationInSeconds = 0; // Reset duration
  }

  Future<void> _sendDecline() async {
    try {
      final receiverFCMToken = widget.fcmToken;
      final jwtToken = widget.jwtToken;

      // Prepare FCM message payload for call decline
      final messageData = {
        "message": {
          "token": receiverFCMToken,
          "data": {
            "callUUID": widget.callUUID,
            "channelName": widget.channelName,
            "status": "DECLINED",
            "type": "CALL_RESPONSE",
            "click_action": "FLUTTER_NOTIFICATION_CLICK",
          },
          "android": {
            "priority": "high",
            "notification": {"sound": "default", "channel_id": "calls_channel"}
          },
          "apns": {
            "headers": {"apns-priority": "10"},
            "payload": {
              "aps": {"sound": "default", "badge": 1, "content-available": 1}
            }
          }
        }
      };

      // Send HTTP request to FCM API
      final response = await http.post(
        Uri.parse(Constants.FCM_API_URL),
        headers: {
          "Authorization": "Bearer $jwtToken",
          "Content-Type": "application/json"
        },
        body: jsonEncode(messageData),
      );

      print(response.statusCode);

      if (response.statusCode != 200) {
        final errorData = jsonDecode(response.body);
        throw Exception("FCM Decline Error: ${errorData.toString()}");
      }

      print("✅ Call decline notification sent successfully from Call screen");
    } catch (e) {
      debugPrint("Failed to send call decline status: $e");
      // Optionally show error to user
      Get.snackbar(
        "Error",
        "Failed to send decline notification",
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  Future<void> _cleanUpCall({bool isDeclined = false}) async {
    if (_isDisposing) return; // Prevent multiple executions
    _isDisposing = true;
    print("Cleanup called");

    _stopCallTimer();
    _stopRinging();
    // _fcmSubscription?.cancel();

    try {
      if (_joinedCall) {
        await _engine.leaveChannel();
      }
      await _engine.release();
    } catch (e) {
      debugPrint('Error cleaning Agora: $e');
    }

    // Only send decline if we're the ones initiating it
    print("${!_declineSent} ${!isDeclined} $_joinedCall");
    if (!_declineSent && !isDeclined && _joinedCall) {
      try {
        await _sendDecline();
      } catch (e) {
        debugPrint('Failed to send decline: $e');
      }
    }
    // await stopCallForegroundService();
    // userBusyStatus = false;
    // await _audioSession.setActive(false);
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      backgroundColor: Colors.black,
      body: Container(
        width: MediaQuery.of(context).size.width,
        height: MediaQuery.of(context).size.height,
        child: Stack(
          children: [
            // Full-screen video (remote or local)
            if (widget.callType == 'video')
              Center(
                child: _remoteUid != null
                    ? _isRemoteVideoEnabled
                        ? AgoraVideoView(
                            controller: VideoViewController.remote(
                              rtcEngine: _engine,
                              canvas: VideoCanvas(uid: _remoteUid),
                              connection: RtcConnection(channelId: widget.channelName),
                            ),
                          )
                        : _buildBlankView()
                    : _joinedCall
                        ? AgoraVideoView(
                            controller: VideoViewController(
                              rtcEngine: _engine,
                              canvas: const VideoCanvas(uid: 0),
                            ),
                          )
                        : const Center(
                            child: CircularProgressIndicator(),
                          ),
              ),

            // Picture-in-picture local video (when remote user is connected)
            if (widget.callType == 'video')
              if (_remoteUid != null && _joinedCall)
                Align(
                  alignment: Alignment.bottomRight,
                  child: Container(
                    margin: const EdgeInsets.only(bottom: 110, right: 20),
                    width: 150,
                    height: 200,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(color: Colors.white, width: 2),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(10),
                      child: isVideoOn
                          ? AgoraVideoView(
                              controller: VideoViewController(
                                rtcEngine: _engine,
                                canvas: const VideoCanvas(uid: 0),
                              ),
                            )
                          : _buildBlankView(),
                    ),
                  ),
                ),
            Container(
              padding: EdgeInsets.symmetric(vertical: 30),
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _buildCallStatus(),
                  if (widget.callType == 'audio') _buildUserInfo(),
                  _buildCallControls()
                ],
              ),
            ),
          ],
        ),
      ),
    ));
  }

  Widget _buildUserInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Icon(Icons.account_circle, size: 130, color: Color.fromRGBO(154, 151, 151, 1)),
        Text(widget.callerName, style: TextStyle(color: Colors.white, fontSize: 20)),
        if (widget.callerJob != '')
          Text(widget.callerJob,
              style: TextStyle(color: Color.fromRGBO(191, 191, 191, 1), fontSize: 12)),
        Text(widget.callerCompany,
            style: TextStyle(color: Color.fromRGBO(191, 191, 191, 1), fontSize: 12)),
        SizedBox(height: 40),
        !_isRemoteAudioEnabled
            ? Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.mic_off_sharp, size: 20, color: Colors.red),
                  SizedBox(width: 10),
                  Text('Remote User Muted',
                      style: TextStyle(color: Colors.white, fontSize: 12)),
                ],
              )
            : SizedBox(),
      ],
    );
  }

  Widget _buildCallControls() {
    return Container(
      width: MediaQuery.of(context).size.width * 0.85,
      child: widget.status == 'ringing' && _remoteUid == null
          ? _buildControlButtons()
          : _joinedCall
              ? _buildControlButtons()
              : _buildCallActionButtons(),
    );
  }

  // Helper method to format duration (e.g., 65 seconds -> "01:05")
  String _formatDuration(int seconds) {
    final minutes = (seconds ~/ 60).toString().padLeft(2, '0');
    final remainingSeconds = (seconds % 60).toString().padLeft(2, '0');
    return '$minutes:$remainingSeconds';
  }

  // Helper method for call action buttons
  Widget _buildCallActionButtons() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildTextButton(
            label: 'DECLINE',
            icon: Icons.call_end_sharp,
            color: Colors.red,
            onTap: !_isCallDeclined ? _declineCall : () {},
          ),
          _buildTextButton(
            label: 'ACCEPT',
            icon: (widget.callType == 'audio') ? Icons.call_sharp : Icons.videocam_sharp,
            color: Colors.green,
            onTap: !_isCallDeclined ? _acceptCall : () {},
          ),
        ],
      ),
    );
  }

  Widget _buildCallStatus() {
    // Common text styles
    const whiteText15 = TextStyle(color: Colors.white, fontSize: 15);
    const whiteText20 = TextStyle(color: Colors.white, fontSize: 20);
    const whiteText21 = TextStyle(color: Colors.white, fontSize: 21);
    const whiteText22 = TextStyle(color: Colors.white, fontSize: 22);
    const greyText15 = TextStyle(color: Color.fromRGBO(191, 191, 191, 1), fontSize: 15);
    const redText12 = TextStyle(color: Colors.red, fontSize: 12);
    const whiteText12 = TextStyle(color: Colors.white, fontSize: 12);
    const redText18 = TextStyle(color: Colors.red, fontSize: 18);

    if (_isCallEnded) {
      return Text('Call Ended', style: redText18);
    }

    if (_isCallDeclined) {
      return Text('Call Declined', style: redText18);
    }

    if (isUserBusy) {
      return Column(
        children: [
          Text('Busy with another call', style: redText18),
          Text(widget.callerName, style: whiteText21),
        ],
      );
    }

    // Ringing/Incoming call state
    if (widget.status == 'ringing' && !_acceptedCall) {
      return widget.callType == 'video'
          ? Column(
              children: [
                Text('Calling...', style: whiteText20),
                Text(widget.callerName, style: whiteText21),
              ],
            )
          : Text('Ringing...', style: whiteText20);
    }

    // Video call active state
    if (widget.callType == 'video') {
      return _joinedCall
          ? Column(
              children: [
                Text(widget.callerName, style: whiteText22),
                Text(_formatDuration(_callDurationInSeconds), style: whiteText15),
                if (!_isRemoteAudioEnabled) ...[
                  SizedBox(height: 10),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.mic_off_sharp, size: 20, color: Colors.red),
                      SizedBox(width: 5),
                      Text('Remote User Mic Muted', style: redText12),
                    ],
                  ),
                ],
              ],
            )
          : Column(
              children: [
                Text(widget.callerName, style: whiteText22),
                Text('Incoming Call...', style: whiteText15),
              ],
            );
    }

    // Audio call active state
    return _joinedCall
        ? Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text('Connected', style: whiteText22),
              Text(_formatDuration(_callDurationInSeconds), style: greyText15),
              if (!_isRemoteAudioEnabled) ...[
                SizedBox(height: 15),
                Center(
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.mic_off_sharp, size: 15, color: Colors.white),
                        SizedBox(width: 5),
                        Text('Remote User Muted', style: whiteText12),
                      ],
                    ),
                  ),
                ),
              ] else ...[
                SizedBox(height: 25),
              ],
            ],
          )
        : Text('Calling...', style: whiteText20);
  }

  // Helper method for call controls
  Widget _buildControlButtons() {
    return Container(
      height: MediaQuery.of(context).size.height * 0.08,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Color.fromRGBO(32, 44, 51, 1),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          if (widget.callType == 'video')
            _buildCircleButton(
              icon: isVideoOn ? Icons.videocam_sharp : Icons.videocam_off_sharp,
              isActive: isVideoOn,
              onTap: _toggleVideo,
            ),
          _buildCircleButton(
            icon: isSpeakerOn ? Icons.volume_up_sharp : Icons.volume_off_sharp,
            isActive: isSpeakerOn,
            onTap: _toggleSpeaker,
          ),
          _buildCircleButton(
            icon: isMicOn ? Icons.mic_sharp : Icons.mic_off_sharp,
            isActive: isMicOn,
            onTap: _toggleMute,
          ),
          if (widget.callType == 'video')
            _buildCircleButton(
              icon: Icons.cameraswitch,
              isActive: isFrontCam,
              onTap: _switchCamera,
            ),
          _buildCircleButton(
            icon: Icons.phone_sharp,
            color: Colors.red,
            isActive: false,
            onTap: _endCall,
          ),
        ],
      ),
    );
  }

  // Reusable circle button widget
  Widget _buildCircleButton({
    required IconData icon,
    required VoidCallback onTap,
    bool isActive = true,
    Color color = const Color.fromRGBO(42, 57, 66, 1),
  }) {
    return GestureDetector(
      onTap: onTap,
      child: CircleAvatar(
        radius: 24,
        backgroundColor: isActive ? Colors.white : color,
        child: Icon(icon, color: isActive ? Colors.black : Colors.white, size: 27),
      ),
    );
  }

  // Reusable text button widget
  Widget _buildTextButton({
    required String label,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Container(
      width: MediaQuery.of(context).size.width * 0.35,
      height: 40,
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(20)),
      child: ElevatedButton.icon(
        style: ElevatedButton.styleFrom(
          foregroundColor: Colors.white,
          backgroundColor: color,
          textStyle: Constants.textStyle,
          elevation: 3,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        ),
        onPressed: onTap,
        icon: Icon(icon, color: Colors.white, size: 22),
        label: FittedBox(
          fit: BoxFit.scaleDown,
          child: Text(label, style: TextStyle(fontSize: 13)),
        ),
      ),
    );
  }

  void _switchCamera() async {
    setState(() {
      isFrontCam = !isFrontCam;
      _engine.switchCamera();
    });
  }

  void _toggleSpeaker() async {
    final newSpeakerState = !isSpeakerOn;

    if (_joinedCall) {
      try {
        await _engine.setEnableSpeakerphone(newSpeakerState);
      } catch (e) {
        debugPrint("Agora speaker toggle failed: $e");
      }
    } else {
      await NativeCallerTone.switchOutput(isSpeakerOn);
    }

    setState(() {
      isSpeakerOn = newSpeakerState;
    });
  }

  void _endCall() async {
    if (_isCallEnded) return;
    _isCallEnded = true;

    try {
      if (Platform.isIOS) {
        await CallManager().endCall(widget.callUUID);
      }
      if (!_acceptedCall) {
        _sendDecline();
        _declineSent = true;
      }

      final callTimer = _callDurationInSeconds;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        widget.onExit?.call(widget.callType, 'call', '$callTimer');
      });
      _stopCallTimer();
      if (_joinedCall && !_isDisposing && mounted) {
        await _engine.leaveChannel();
      }
      // userBusyStatus = false;

      _declineTimer = Timer(const Duration(seconds: 2), () {
        // Cancel the timer first
        _declineTimer?.cancel();

        // Reset the declined state (optional, since we're leaving)
        if (mounted) {
          setState(() => _isCallEnded = false);
          _cleanUpCall(isDeclined: true);
          Get.back();
        }

        // Perform cleanup and navigate back
      });

      // Ensure timer is cancelled if this function is called again
      onCallDeclinedGlobal = () {
        _declineTimer?.cancel();
        if (!_declineSent && mounted) {
          _handleCallDeclined();
        }
      };
      if (mounted) Get.back();
    } catch (e) {
      debugPrint('End call error: $e');
      if (mounted) Get.back();
    }
  }

  void _toggleMute() {
    setState(() {
      isMicOn = !isMicOn;
      _engine.muteLocalAudioStream(!isMicOn);
    });
  }

  void _toggleVideo() {
    setState(() {
      isVideoOn = !isVideoOn;
      _engine.muteLocalVideoStream(!isVideoOn);
    });
  }

  void _declineCall() {
    if (_declineSent) return;
    _declineSent = true;
    print("Stop after Decline");
    _stopRinging();
    _sendDecline();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      widget.onExit?.call(widget.callType, 'call', '0');
    });
    // userBusyStatus = false;
    if (mounted) Get.back();
  }

  void _acceptCall() async {
    // await _configureAudioSession();
    // Join the channel
    _engine.joinChannel(
      token: widget.token,
      channelId: widget.channelName,
      uid: 0,
      options: ChannelMediaOptions(
        channelProfile: ChannelProfileType.channelProfileCommunication,
        clientRoleType: ClientRoleType.clientRoleBroadcaster,
        publishCameraTrack: widget.callType == 'video' && isVideoOn,
        publishMicrophoneTrack: isMicOn,
        autoSubscribeAudio: true,
        autoSubscribeVideo: true,
      ),
    );
    // if (widget.status == 'ringing') {
    //   _startRinging();
    // }
    print(widget.status);
    if (widget.status == 'receiving') {
      print("Stop after Accept");
      _stopRinging(); // Stop ringing when the call is accepted
    }
  }

  // Helper function to request permissions
  Future<bool> _requestPermissions(bool isVideoCall) async {
    // Request microphone permission
    final micStatus = await Permission.microphone.request();

    // If it's a video call, request camera permission too
    PermissionStatus? cameraStatus;
    if (isVideoCall) {
      cameraStatus = await Permission.camera.request();
    }

    if (micStatus != PermissionStatus.granted ||
        (isVideoCall && cameraStatus != PermissionStatus.granted)) {
      debugPrint("❌ Camera or microphone permission denied");

      // Show alert or guide user to settings if permanently denied
      if (micStatus.isPermanentlyDenied ||
          (isVideoCall && cameraStatus!.isPermanentlyDenied)) {
        openAppSettings();
      } else {
        _showError("You must grant camera & microphone permissions for calls.");
      }

      return false;
    }
    return true;
  }

  // Helper function to show errors
  void _showError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(message)),
      );
    }
  }

  Widget _buildBlankView() {
    return Container(
      color: Colors.black,
      child: Center(
        child: Icon(
          Icons.videocam_off_sharp,
          size: 70,
          color: Colors.red,
        ),
      ),
    );
  }
}

// class CallTonePlayer {
//   Timer? _timer;
//   bool _isPlaying = false;

//   /// Start playing the default ringtone intermittently (e.g., every 3 seconds)
//   void start() {
//     if (_isPlaying) return;
//     _isPlaying = true;
//     // FlutterRingtonePlayer().play(fromAsset: "assets/ring.mp3");

//     _timer = Timer.periodic(Duration(seconds: 3), (timer) {
//       FlutterRingtonePlayer().play(
//         fromAsset: "assets/ring.wav",
//         // android: AndroidSounds.ringtone,
//         // ios: IosSounds.electronic,
//         looping: false, // we'll manually handle repeating
//         volume: 1.0,
//         asAlarm: true,
//       );
//     });
//   }

//   /// Stop the call tone
//   void stop() {
//     print("Stop called");
//     // Stop sound immediately (only works on Android, iOS will auto-stop)
//     FlutterRingtonePlayer().stop();
//     _timer?.cancel();
//     _timer = null;
//     _isPlaying = false;
//   }
// }
