// import 'dart:convert';
// import 'dart:io';

// import 'package:agora_chat_sdk/agora_chat_sdk.dart';
// import 'package:android_intent_plus/android_intent.dart';
// import 'package:android_intent_plus/flag.dart';
// import 'package:cached_network_image/cached_network_image.dart';
// import 'package:cmtmeet/pages/schedule_meeting_page.dart';
// import 'package:cmtmeet/service/firebase_service_china.dart';
// import 'package:cmtmeet/service/websocket_service.dart';
// import 'package:cmtmeet/utils/constants.dart';
// import 'package:cmtmeet/utils/vcard_generator.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:get/get.dart';
// import 'package:get_storage/get_storage.dart';
// import 'package:intl/intl.dart';
// import 'package:path_provider/path_provider.dart';
// import 'package:http/http.dart' as http;
// import 'package:url_launcher/url_launcher.dart';

// class ChatScreen extends StatefulWidget {
//   final String chatId;
//   final String chatAgoraId;
//   final String chatImage;
//   final String chatName;
//   final String chatJob;
//   final String chatCompany;
//   final VoidCallback? onExit;
//   final Function(String, int) onMessageSent;
//   final Function(int) onSchedule;

//   const ChatScreen({
//     required this.chatId,
//     required this.chatAgoraId,
//     required this.chatImage,
//     required this.chatName,
//     required this.chatJob,
//     required this.chatCompany,
//     this.onExit,
//     required this.onMessageSent,
//     required this.onSchedule,
//   });

//   @override
//   _ChatScreenState createState() => _ChatScreenState();
// }

// class _ChatScreenState extends State<ChatScreen> {
//   final WebSocketService webSocketService = Get.find<WebSocketService>();
//   final GetStorage storage = GetStorage();
//   final TextEditingController _messageController = TextEditingController();
//   final ScrollController _scrollController = ScrollController();
//   final List<ChatMessage> _messages = [];
//   String? lastMessageTs;
//   String? cursor;
//   bool isLoading = false;
//   late Map<String, dynamic> user;
//   late Map<String, dynamic> event;
//   String? eventCode;

//   late void Function(dynamic, dynamic, dynamic) _statusListener;

//   final List<Map<String, dynamic>> _processedMessages = [];
//   String _status = "offline";

//   @override
//   void initState() {
//     super.initState();

//     ChatHandlerManager.initializeChat(
//       messages: _messages,
//       processMessages: _processMessages,
//       onMessageSent: widget.onMessageSent,
//       markMessagesAsRead: _markMessagesAsRead,
//       chatAgoraId: widget.chatAgoraId,
//     );
//     setChatScreenState(true);
//     currentChatUserId = widget.chatAgoraId;

//     user = storage.read("user");
//     event = storage.read("event") ?? {};
//     eventCode = storage.read("event")?["eventCode"];

//     _statusListener = (userId, eventCode, status) {
//       final key = "$userId-$eventCode";
//       if (key == '${widget.chatId}-$eventCode' && mounted) {
//         setState(() {
//           _status = status;
//         });
//       }
//     };

//     webSocketService.addStatusListener(_statusListener);

//     if (eventCode != null) {
//       final key = '${widget.chatId}-$eventCode';
//       setState(() {
//         _status = webSocketService.userStatuses[key] ?? "offline";
//       });
//     }

//     _loadMessages(initialLoad: true);
//     _scrollController.addListener(_scrollListener);
//     WidgetsBinding.instance.addPostFrameCallback((_) {
//       _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
//     });
//     print("🔥 ChatScreen initialized.");
//   }

//   void _scrollListener() {
//     if (_scrollController.position.pixels ==
//         _scrollController.position.maxScrollExtent) {
//       _loadMessages();
//     }
//   }

//   @override
//   void dispose() {
//     ChatHandlerManager.dispose();
//     _scrollController.removeListener(_scrollListener);
//     _scrollController.dispose();
//     setChatScreenState(false);
//     currentChatUserId = null;
//     webSocketService.removeStatusListener(_statusListener);
//     _messageController.dispose();
//     _messages.clear();
//     _processedMessages.clear();
//     WidgetsBinding.instance.addPostFrameCallback((_) {
//       widget.onExit?.call();
//     });
//     print("🔥 ChatScreen disposed.");
//     super.dispose();
//   }

//   void _processMessages() {
//     _processedMessages.clear();
//     String? lastDate;
//     List<Map<String, dynamic>> tempMessages = [];
//     for (var message in _messages.reversed) {
//       String currentDate =
//           _formatTimestamp(message.serverTime).split(", ").first;

//       if (lastDate != currentDate) {
//         tempMessages.add({
//           "isDate": true,
//           "date": currentDate,
//         });
//         lastDate = currentDate;
//       }

//       tempMessages.add({
//         "isDate": false,
//         "message": message,
//       });
//     }
//     _processedMessages.addAll(tempMessages.reversed);

//     if (mounted) {
//       setState(() {});

//       // Scroll to bottom (minScrollExtent when reverse:true)
//       WidgetsBinding.instance.addPostFrameCallback((_) {
//         if (_scrollController.hasClients &&
//             _scrollController.position.pixels >
//                 _scrollController.position.maxScrollExtent + 100) {
//           _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
//         }
//       });
//     }
//   }

//   Future<void> _loadMessages({bool initialLoad = false}) async {
//     if (isLoading) return;
//     setState(() {
//       isLoading = true;
//     });

//     try {
//       ChatConversationType conversationType = ChatConversationType.Chat;

//       final options = FetchMessageOptions(
//         msgTypes: [MessageType.TXT, MessageType.CUSTOM],
//       );

//       // Fetch messages from server history
//       ChatCursorResult<ChatMessage> result =
//           await ChatClient.getInstance.chatManager.fetchHistoryMessagesByOption(
//         widget.chatAgoraId,
//         conversationType,
//         options: options,
//         cursor: cursor,
//         pageSize: 10,
//       );

//       List<ChatMessage> loadedMessages = result.data;
//       cursor = result.cursor;
//       print(loadedMessages);

//       if (loadedMessages.isNotEmpty) {
//         lastMessageTs = loadedMessages.first.serverTime.toString();
//         _messages.addAll(loadedMessages);
//         _processMessages();

//         ChatConversation? conversation = await ChatClient
//             .getInstance.chatManager
//             .getConversation(widget.chatAgoraId,
//                 type: ChatConversationType.Chat);
//         await conversation?.markAllMessagesAsRead();
//       }

//       if (initialLoad) {
//         WidgetsBinding.instance.addPostFrameCallback((_) {
//           _scrollController.jumpTo(0);
//         });
//       }
//     } catch (e) {
//       print('Error loading messages: $e');
//     } finally {
//       setState(() {
//         isLoading = false;
//       });
//     }
//   }

//   Future<void> _markMessagesAsRead(List<ChatMessage> messages) async {
//     if (messages.isEmpty) return;

//     try {
//       for (var message in messages) {
//         ChatConversation? conversation = await ChatClient
//             .getInstance.chatManager
//             .getConversation(widget.chatAgoraId,
//                 type: ChatConversationType.Chat);
//         await conversation?.markMessageAsRead(message.msgId);
//       }
//       print("✅ Marked ${messages.length} messages as read.");
//     } catch (e) {
//       print("❌ Error marking messages as read: $e");
//     }
//   }

//   void _sendMessage() async {
//     if (_messageController.text.trim().isEmpty) return;
//     String messageContent = _messageController.text.trim();

//     try {
//       var message = ChatMessage.createTxtSendMessage(
//         targetId: widget.chatAgoraId,
//         content: messageContent,
//       );
//       message.deliverOnlineOnly = false;
//       // Configure push notification
//       message.attributes = {
//         'em_force_notification':
//             true, // Force push even if app is in foreground
//         'em_push_title': 'New message', // Title for notification
//         'em_push_content': messageContent, // Content preview
//         'em_push_channel_id': 'chat_messages', // Android notification channel
//         'em_push_importance': 'high', // Heads-up notification
//         'em_push_sound': 'default', // Android notification sound
//         'em_push_category': 'message', // Android notification category
//         'em_push_visibility': 'public', // Show on lock screen
//         'em_push_vibrate': true, // Enable vibration
//         'em_push_lights': true,
//         'em_apns_ext': {
//           // iOS specific
//           'em_push_title': 'New message',
//           'em_push_subtitle': 'From ${GetStorage().read("user")["fullname"]}',
//         },
//         'em_android_ext': {
//           // Android specific
//           // 'em_push_title': '$senderName sent a message',
//           'em_push_title': 'Agora FCM',
//           'em_push_summary': messageContent,
//           'em_push_channel_id': 'chat_messages', // Android notification channel
//           'em_push_importance': 'high', // Heads-up notification
//           'em_push_sound': 'default', // Android notification sound
//           'em_push_category': 'message', // Android notification category
//           'em_push_visibility': 'public', // Show on lock screen
//           'em_push_vibrate': true, // Enable vibration
//           'em_push_lights': true,
//         },
//         // Optional: Use a predefined template
//         // 'em_push_template': {
//         //   'name': 'your_template_name',
//         //   'title_args': ['arg1'],
//         //   'content_args': ['arg2']
//         // }
//       };
//       await ChatClient.getInstance.chatManager.sendMessage(message);

//       final response = await http.post(
//         Uri.parse("https://${Constants.url}/messages/send-message"),
//         headers: {"Content-Type": "application/json"},
//         body: jsonEncode({
//           "fromAgoraId": user["agoraid"],
//           "toAgoraId": widget.chatAgoraId,
//           "message": messageContent
//         }),
//       );

//       print(jsonDecode(response.body)["message"]);

//       // Notify parent page to update the timestamp
//       final int currentTimestamp = DateTime.now().millisecondsSinceEpoch;
//       widget.onMessageSent(widget.chatAgoraId, currentTimestamp);

//       setState(() {
//         _messages.insert(0, message);
//         _processMessages();
//       });

//       _messageController.clear();
//       WidgetsBinding.instance.addPostFrameCallback((_) {
//         _scrollController.jumpTo(0);
//       });
//     } catch (e) {
//       print('Error sending message: $e');
//     }
//   }

//   Future<void> _sendVCard() async {
//     final vCardContent = VCardGenerator.generateVCard(
//       fullName: user["fullname"],
//       phone: user["phone"] ?? "9876543210",
//       email: user["email"] ?? "",
//       company: user["companyname"] ?? "",
//       jobTitle: user["jobtitle"] ?? "",
//     );

//     try {
//       var message = ChatMessage.createCustomSendMessage(
//         targetId: widget.chatAgoraId,
//         event: "vcard",
//         params: {
//           "vcard": vCardContent,
//           "senderName": user["fullname"],
//           "senderPhone": user["phone"] ?? "9876543210",
//           "senderEmail": user["email"] ?? "",
//           "senderCompany": user["companyname"],
//           "senderJobTitle": user["jobtitle"],
//         },
//       );

//       // Add push notification attributes
//       message.attributes = {
//         'em_force_notification': true,
//         'em_push_title': 'Contact shared',
//         'em_push_content': '${user["fullname"]} shared their contact',
//       };

//       // Add message to UI immediately
//       setState(() {
//         _messages.insert(0, message);
//         _processMessages();
//       });

//       // Notify parent page to update the timestamp
//       final int currentTimestamp = DateTime.now().millisecondsSinceEpoch;
//       widget.onMessageSent(widget.chatAgoraId, currentTimestamp);

//       // Scroll to bottom
//       WidgetsBinding.instance.addPostFrameCallback((_) {
//         _scrollController.jumpTo(0);
//       });

//       // Send message to server
//       await ChatClient.getInstance.chatManager.sendMessage(message);
//     } catch (e) {
//       // Remove from UI if sending fails
//       print('Error sending vCard: $e');
//     }
//   }

//   String _formatTimestamp(int timestamp) {
//     final dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
//     final datePart = DateFormat('d MMMM yyyy').format(dateTime);
//     final timePart = DateFormat('h:mm a').format(dateTime);
//     return "$datePart, $timePart";
//   }

//   Widget _buildMessageBubble(ChatMessage message, bool isMe) {
//     if (message.body.type == MessageType.CUSTOM) {
//       final customBody = message.body as ChatCustomMessageBody;
//       if (customBody.event == "vcard") {
//         // print(message);
//         return _buildVCardBubble(message, isMe, message.msgId);
//         // } else if (customBody.event == "schedule_meeting") {
//         //   return _buildMeetingBubble(customBody.params, isMe, message.msgId);
//       }
//     } else if (message.body.type == MessageType.TXT) {
//       final content = (message.body as ChatTextMessageBody).content;
//       final time = _formatTimestamp(message.serverTime).split(", ").last;
//       return Align(
//         alignment: isMe ? Alignment.centerRight : Alignment.centerLeft,
//         child: Container(
//           constraints: BoxConstraints(
//             maxWidth:
//                 MediaQuery.of(context).size.width * 0.7, // Maximum 70% width
//           ),
//           padding: const EdgeInsets.symmetric(vertical: 7.0, horizontal: 10.0),
//           margin: const EdgeInsets.symmetric(vertical: 6.0),
//           decoration: BoxDecoration(
//             color: isMe ? Color(0xffe5ffde) : Colors.white,
//             borderRadius: BorderRadius.only(
//               topLeft: Radius.circular(isMe ? 11.0 : 3.0),
//               topRight: Radius.circular(isMe ? 3.0 : 11.0),
//               bottomLeft: Radius.circular(11.0),
//               bottomRight: Radius.circular(11.0),
//             ),
//           ),
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               Text(
//                 content,
//                 style: TextStyle(
//                   color: Colors.black,
//                   fontSize: 16,
//                 ),
//               ),
//               SizedBox(height: 2),
//               Text(
//                 time,
//                 style: TextStyle(
//                   color: Colors.black54,
//                   fontSize: 12,
//                 ),
//               ),
//             ],
//           ),
//         ),
//       );
//     }
//     // Optionally handle other message types (e.g., images, files)
//     return SizedBox.shrink();
//   }

//   String formatDuration(int seconds) {
//     if (seconds < 60) return '$seconds secs';

//     final int mins = seconds ~/ 60;
//     final int secs = seconds % 60;

//     if (mins < 60) {
//       return secs == 0 ? '$mins min' : '$mins min, $secs secs';
//     }

//     final int hrs = mins ~/ 60;
//     final int remMins = mins % 60;

//     final parts = <String>[];
//     if (hrs > 0) parts.add('$hrs hr${hrs > 1 ? 's' : ''}');
//     if (remMins > 0) parts.add('$remMins min');
//     if (secs > 0) parts.add('$secs sec${secs > 1 ? 's' : ''}');

//     return parts.join(', ');
//   }

//   Widget _buildVCardBubble(ChatMessage message, bool isMe, String msgId) {
//     Map<String, String>? params =
//         (message.body as ChatCustomMessageBody).params;
//     final time = _formatTimestamp(message.serverTime).split(", ").last;

//     return Align(
//       alignment: isMe ? Alignment.centerRight : Alignment.centerLeft,
//       child: Container(
//         constraints: BoxConstraints(
//           maxWidth: MediaQuery.of(context).size.width * 0.7,
//         ),
//         padding: const EdgeInsets.all(12.0),
//         margin: const EdgeInsets.symmetric(vertical: 6.0),
//         decoration: BoxDecoration(
//           color: Color(0xffc01058),
//           borderRadius: BorderRadius.circular(12.0),
//         ),
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Row(
//               mainAxisAlignment: MainAxisAlignment.start,
//               children: [
//                 Icon(
//                   Icons.account_circle_sharp,
//                   size: 40,
//                   color: Colors.white,
//                 ),
//                 SizedBox(width: 7),
//                 Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Text(
//                       '${params!["senderName"]}',
//                       overflow: TextOverflow.ellipsis,
//                       style: TextStyle(
//                           color: Colors.white,
//                           fontSize: 14,
//                           fontWeight: FontWeight.bold),
//                     ),
//                     Text(
//                       '${params["senderCompany"]}',
//                       overflow: TextOverflow.ellipsis,
//                       style: TextStyle(color: Colors.white, fontSize: 12),
//                     ),
//                   ],
//                 )
//               ],
//             ),
//             Row(
//               mainAxisAlignment: MainAxisAlignment.end,
//               children: [
//                 Text(
//                   time,
//                   style: TextStyle(
//                     color: Color.fromARGB(255, 255, 129, 182),
//                     fontSize: 10,
//                   ),
//                 ),
//               ],
//             ),
//             Divider(thickness: 0.5, color: Color.fromARGB(255, 255, 129, 182)),
//             Row(
//               mainAxisAlignment: MainAxisAlignment.spaceAround,
//               children: [
//                 GestureDetector(
//                   onTap: () => _deleteContact(msgId),
//                   child: Text(
//                     'Delete',
//                     style: TextStyle(color: Colors.white),
//                   ),
//                 ),
//                 GestureDetector(
//                   onTap: () => _saveContactWithEditor(params),
//                   child: Text(
//                     'Save Contact',
//                     style: TextStyle(color: Colors.white),
//                   ),
//                 )
//               ],
//             )
//           ],
//         ),
//       ),
//     );
//   }

//   // Widget _buildMeetingBubble(Map<String, dynamic>? params, bool isMe, String msgId) {
//   //   final timestamp = int.tryParse(params!['dateTime']) ?? 0;
//   //   final dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
//   //   final now = DateTime.now();
//   //   final canJoin =
//   //       now.isAfter(dateTime) && now.isBefore(dateTime.add(const Duration(hours: 1)));
//   //   final meetingStarted = now.isAfter(dateTime);

//   //   return Align(
//   //     alignment: isMe ? Alignment.centerRight : Alignment.centerLeft,
//   //     child: ConstrainedBox(
//   //       constraints: BoxConstraints(maxWidth: MediaQuery.of(context).size.width * 0.8),
//   //       child: Card(
//   //         margin: const EdgeInsets.symmetric(vertical: 6.0),
//   //         shape: RoundedRectangleBorder(
//   //           borderRadius: BorderRadius.circular(10.0),
//   //         ),
//   //         elevation: 2,
//   //         color: Colors.white,
//   //         child: Column(
//   //           children: [
//   //             Container(
//   //               padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 10.0),
//   //               decoration: BoxDecoration(
//   //                 color: canJoin ? Colors.green : Colors.black54,
//   //                 borderRadius: BorderRadius.only(
//   //                   topLeft: Radius.circular(10.0),
//   //                   topRight: Radius.circular(10.0),
//   //                 ),
//   //               ),
//   //               child: Row(
//   //                 children: [
//   //                   Expanded(
//   //                     flex: 5,
//   //                     child: Text(
//   //                       DateFormat('dd MMM yyyy').format(dateTime),
//   //                       style: TextStyle(
//   //                         color: Colors.white,
//   //                         fontSize: 16,
//   //                         fontWeight: FontWeight.bold,
//   //                       ),
//   //                     ),
//   //                   ),
//   //                   Expanded(
//   //                     flex: 5,
//   //                     child: Text(
//   //                       DateFormat('hh:mm a').format(dateTime),
//   //                       textAlign: TextAlign.end,
//   //                       style: TextStyle(
//   //                         color: Colors.white,
//   //                         fontSize: 16,
//   //                         fontWeight: FontWeight.bold,
//   //                       ),
//   //                     ),
//   //                   ),
//   //                 ],
//   //               ),
//   //             ),
//   //             SizedBox(height: 10),
//   //             Container(
//   //               padding: EdgeInsets.symmetric(horizontal: 10),
//   //               child: Column(
//   //                 children: [
//   //                   Row(
//   //                     children: [
//   //                       Icon(Icons.event_note_sharp, size: 50, color: Color(0xffc01058)),
//   //                       SizedBox(width: 12),
//   //                       Flexible(
//   //                         child: Column(
//   //                           crossAxisAlignment: CrossAxisAlignment.start,
//   //                           children: [
//   //                             Text(
//   //                               'Meeting Schedule',
//   //                               style:
//   //                                   TextStyle(fontWeight: FontWeight.bold, fontSize: 17),
//   //                             ),
//   //                             Text(
//   //                               params['title'],
//   //                               style: TextStyle(fontSize: 14),
//   //                               softWrap: true,
//   //                               overflow: TextOverflow.ellipsis,
//   //                             )
//   //                           ],
//   //                         ),
//   //                       )
//   //                     ],
//   //                   ),
//   //                   Divider(thickness: 0.5, color: Colors.grey[500]),
//   //                   SizedBox(height: 5),
//   //                   Row(
//   //                     mainAxisAlignment: MainAxisAlignment.spaceAround,
//   //                     children: [
//   //                       GestureDetector(
//   //                         onTap: meetingStarted ? null : () => _addToCalendar(params),
//   //                         child: Text(
//   //                           'Add to Calendar',
//   //                           style: TextStyle(
//   //                               color:
//   //                                   meetingStarted ? Colors.grey[500] : Color(0xffc01058),
//   //                               fontWeight: FontWeight.bold),
//   //                         ),
//   //                       ),
//   //                       GestureDetector(
//   //                         onTap: canJoin
//   //                             ? () => _startMeeting(
//   //                                 room:
//   //                                     "${params['senderId']}_${params['senderName']}_$timestamp")
//   //                             : null,
//   //                         child: Text(
//   //                           'Attend Meeting',
//   //                           style: TextStyle(
//   //                               color: canJoin ? Color(0xffc01058) : Colors.grey[500],
//   //                               fontWeight: FontWeight.bold),
//   //                         ),
//   //                       )
//   //                     ],
//   //                   )
//   //                 ],
//   //               ),
//   //             ),
//   //             SizedBox(height: 13),
//   //           ],
//   //         ),
//   //       ),
//   //     ),
//   //   );
//   // }

//   // Future<void> _startMeeting({required String room}) async {
//   //   final callerId = storage.read("user")["id"].toString();
//   //   final calleeId = widget.chatId;
//   //   final eventCode = storage.read("event")["eventCode"].toString();

//   //   try {
//   //     final response = await http.post(
//   //       Uri.parse("https://${Constants.url}/meet/meeting"),
//   //       headers: {"Content-Type": "application/json"},
//   //       body: jsonEncode({
//   //         "callerId": callerId,
//   //         "calleeId": calleeId,
//   //         "channelName": room,
//   //         "eventCode": eventCode,
//   //       }),
//   //     );

//   //     if (response.statusCode == 200) {
//   //       final responseBody = jsonDecode(response.body);
//   //       final token = responseBody["token"];

//   //       Get.to(() => MeetingRoom(room: room, token: token));
//   //     } else {
//   //       _showError("Failed to start meet: ${response.statusCode}");
//   //     }
//   //   } catch (e, stackTrace) {
//   //     print("❌ Error starting meet: $e\n$stackTrace");
//   //     _showError("Failed to start meet. Please try again.");
//   //   }
//   // }

//   // Future<void> _addToCalendar(Map<String, dynamic> meeting) async {
//   //   try {
//   //     final timestamp = int.tryParse(meeting['dateTime'].toString()) ?? 0;
//   //     if (timestamp == 0) throw Exception('Invalid meeting time');
//   //     final dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
//   //     final meetingId = '${widget.chatAgoraId}_${meeting['dateTime']}';
//   //     // Store meeting details
//   //     final meetings = GetStorage().read('scheduled_meetings') ?? {};
//   //     meetings[meetingId] = {
//   //       'chatId': widget.chatId,
//   //       'chatAgoraId': widget.chatAgoraId,
//   //       'title': meeting['title'],
//   //       'dateTime': meeting['dateTime'],
//   //     };
//   //     await GetStorage().write('scheduled_meetings', meetings);
//   //     if (Platform.isAndroid) {
//   //       // Android implementation
//   //       final intent = AndroidIntent(
//   //         action: 'android.intent.action.INSERT',
//   //         data: 'content://com.android.calendar/events',
//   //         arguments: {
//   //           'title': meeting['title'].toString(),
//   //           'description': 'Meeting with ${meeting['senderName'] ?? ''}',
//   //           'beginTime': dateTime.millisecondsSinceEpoch,
//   //           'endTime': dateTime.add(const Duration(hours: 1)).millisecondsSinceEpoch,
//   //           'allDay': false,
//   //         },
//   //       );
//   //       await intent.launch();
//   //     } else if (Platform.isIOS) {
//   //       // iOS implementation using add_2_calendar package
//   //       final Event event = Event(
//   //         title: meeting['title'].toString(),
//   //         description: 'Meeting with ${meeting['senderName'] ?? ''}',
//   //         startDate: dateTime,
//   //         endDate: dateTime.add(const Duration(hours: 1)),
//   //         iosParams: const IOSParams(
//   //           reminder: Duration(minutes: 30), // optional reminder
//   //         ),
//   //       );
//   //       await Add2Calendar.addEvent2Cal(event);
//   //     }
//   //   } catch (e, stackTrace) {
//   //     print('Error adding to calendar: $e\n$stackTrace');
//   //     if (mounted) {
//   //       ScaffoldMessenger.of(context).showSnackBar(
//   //         SnackBar(
//   //           content: Text('Failed to add to calendar: ${e.toString()}'),
//   //           action: SnackBarAction(
//   //             label: 'Retry',
//   //             onPressed: () => _addToCalendar(meeting),
//   //           ),
//   //         ),
//   //       );
//   //     }
//   //   }
//   // }

//   Future<void> _deleteContact(String msgId) async {
//     try {
//       // Delete from server
//       await ChatClient.getInstance.chatManager.deleteRemoteMessagesWithIds(
//         conversationId: widget.chatAgoraId,
//         type: ChatConversationType.Chat,
//         msgIds: [msgId],
//       );

//       // Delete locally
//       setState(() {
//         _messages.removeWhere((m) => m.msgId == msgId);
//         _processMessages();
//       });

//       ScaffoldMessenger.of(context).showSnackBar(
//         const SnackBar(
//           content: Text('Contact deleted'),
//           duration: Duration(seconds: 2),
//         ),
//       );
//     } catch (e) {
//       ScaffoldMessenger.of(context).showSnackBar(
//         SnackBar(
//           content: Text('Failed to delete contact: $e'),
//           duration: Duration(seconds: 2),
//         ),
//       );
//     }
//   }

//   Future<void> _saveContactWithEditor(Map<String, dynamic>? params) async {
//     try {
//       // Validate required parameters
//       if (params?["senderName"] == null || params?["senderPhone"] == null) {
//         throw Exception("Contact information incomplete");
//       }

//       if (Platform.isAndroid) {
//         // Android - Open native contact editor
//         final intent = AndroidIntent(
//           action: 'android.intent.action.INSERT',
//           type: 'vnd.android.cursor.dir/contact',
//           flags: [Flag.FLAG_ACTIVITY_NEW_TASK],
//           arguments: {
//             'name': params!["senderName"],
//             'phone': params["senderPhone"],
//             if (params["senderEmail"] != null &&
//                 params["senderEmail"].isNotEmpty)
//               'email': params["senderEmail"],
//             if (params["senderCompany"] != null &&
//                 params["senderCompany"].isNotEmpty)
//               'company': params["senderCompany"],
//             if (params["senderJobTitle"] != null &&
//                 params["senderJobTitle"].isNotEmpty)
//               'job_title': params["senderJobTitle"],
//             'finishActivityOnSaveCompleted': true,
//           },
//         );
//         await intent.launch();
//       } else if (Platform.isIOS) {
//         // iOS - Use native contact editor via method channel
//         const platform = MethodChannel('com.cmtevents.contact_editor');
//         final bool result = await platform.invokeMethod('openContactEditor', {
//           'senderName': params!["senderName"],
//           'senderPhone': params["senderPhone"],
//           'senderEmail': params["senderEmail"],
//           'senderCompany': params["senderCompany"],
//           'senderJobTitle': params["senderJobTitle"],
//         });

//         if (mounted) {
//           if (result) {
//             ScaffoldMessenger.of(context).showSnackBar(
//               const SnackBar(
//                 content: Text("Contact saved successfully"),
//                 backgroundColor: Colors.green,
//                 duration: Duration(seconds: 1),
//               ),
//             );
//           } else {
//             ScaffoldMessenger.of(context).showSnackBar(
//               const SnackBar(
//                 content: Text("Contact editing cancelled"),
//                 backgroundColor: Colors.red,
//                 duration: Duration(seconds: 1),
//               ),
//             );
//           }
//         }
//         return;
//       }
//     } catch (e) {
//       if (mounted) {
//         ScaffoldMessenger.of(context).showSnackBar(
//           SnackBar(
//             content: Text("Failed to open contact editor: ${e.toString()}"),
//             duration: const Duration(seconds: 2),
//             action: SnackBarAction(
//               label: "Retry",
//               onPressed: () => _saveContactWithEditor(params),
//             ),
//           ),
//         );
//       }

//       // Fallback to vCard method if intent fails
//       await _saveWithVCardFallback(params);
//     }
//   }

//   Future<void> _saveWithVCardFallback(Map<String, dynamic>? params) async {
//     try {
//       if (params == null) throw Exception("No contact data provided");

//       // Use existing vCard if available, otherwise generate basic one
//       final vCard = params["vcard"];

//       final dir = await getTemporaryDirectory();
//       final file = File(
//           '${dir.path}/contact_${DateTime.now().millisecondsSinceEpoch}.vcf');
//       await file.writeAsString(vCard);

//       final uri = Uri.file(file.path);
//       final launched = await launchUrl(
//         uri,
//         mode: LaunchMode.externalApplication,
//       );

//       if (!launched) {
//         throw Exception("Failed to launch contact app");
//       }

//       // Delete the file after some time
//       Future.delayed(const Duration(seconds: 10), () => file.delete());
//     } catch (e) {
//       if (mounted) {
//         ScaffoldMessenger.of(context).showSnackBar(
//           SnackBar(
//             content: Text("Failed to add contact: ${e.toString()}"),
//             duration: const Duration(seconds: 3),
//             action: SnackBarAction(
//               label: "Retry",
//               onPressed: () => _saveContactWithEditor(params),
//             ),
//           ),
//         );
//       }
//     }
//   }

//   // Method to send FCM notification
//   Future<void> sendFCMNotification({
//     required String callerAgoraId,
//     required String callerName,
//     required String callerFCMToken,
//     required String channelName,
//     required String rtcToken,
//     required String callType,
//     required String callUUID,
//     required String fcmToken,
//     required String jwtToken,
//   }) async {
//     // Prepare FCM message payload
//     final messageData = {
//       "message": {
//         "token": fcmToken,
//         "notification": {
//           "title": "Incoming Call",
//           "body": "$callerName is calling you"
//         },
//         "data": {
//           "callerAgoraId": callerAgoraId,
//           "callerName": callerName,
//           "fcmToken": callerFCMToken,
//           "channelName": channelName,
//           "token": rtcToken,
//           "callType": callType,
//           "callUUID": callUUID,
//           "jwtToken": jwtToken,
//           "status": "CALLING",
//           "type": "CALL_RESPONSE",
//           "click_action": "FLUTTER_NOTIFICATION_CLICK",
//         },
//         "android": {
//           "priority": "high",
//           "notification": {
//             "sound": "default",
//             "default_sound": true,
//             "channel_id": "calls_channel"
//           }
//         },
//         "apns": {
//           "headers": {"apns-priority": "10"},
//           "payload": {
//             "aps": {"sound": "default", "badge": 1, "content-available": 1},
//             "voip": "1" // Indicate this is a VoIP notification (iOS)
//           }
//         }
//       }
//     };

//     try {
//       // Send HTTP request to FCM API
//       final response = await http.post(
//         Uri.parse(Constants.FCM_API_URL),
//         headers: {
//           "Authorization": "Bearer $jwtToken",
//           "Content-Type": "application/json"
//         },
//         body: jsonEncode(messageData),
//       );

//       if (response.statusCode != 200) {
//         final errorData = jsonDecode(response.body);
//         throw Exception("FCM Error: ${errorData.toString()}");
//       }

//       print("✅ FCM notification sent successfully");
//     } catch (e) {
//       print("❌ Failed to send FCM notification: $e");
//       throw e;
//     }
//   }

//   void _showError(String message) {
//     if (!mounted) return;

//     ScaffoldMessenger.of(Get.context!).showSnackBar(
//       SnackBar(
//         content: Text(message),
//         duration: const Duration(seconds: 3),
//         behavior: SnackBarBehavior.floating,
//         margin: const EdgeInsets.all(10),
//         shape: RoundedRectangleBorder(
//           borderRadius: BorderRadius.circular(10),
//         ),
//       ),
//     );
//   }

//   @override
//   Widget build(BuildContext context) {
//     return SafeArea(
//       child: Scaffold(
//         appBar: AppBar(
//           foregroundColor: Colors.white,
//           backgroundColor: Color(0xffc01058),
//           leading: SizedBox(
//             width: 100,
//             child: Row(
//               children: [
//                 SizedBox(
//                   width: 50,
//                   child: IconButton(
//                     icon: Icon(Icons.arrow_back),
//                     onPressed: () {
//                       _messages.clear();
//                       _processedMessages.clear();
//                       Navigator.pop(context);
//                     },
//                   ),
//                 ),
//               ],
//             ),
//           ),
//           title: Transform.translate(
//             offset: Offset(-30, 0),
//             child: SizedBox(
//               width: MediaQuery.of(context).size.width * 0.6, // Adjust width
//               child: Row(
//                 children: [
//                   widget.chatImage != ''
//                       ? CachedNetworkImage(
//                           imageUrl: widget.chatImage,
//                           placeholder: (context, url) => Container(
//                             width: 40,
//                             height: 40,
//                             child: Center(
//                               child: SizedBox(
//                                 width: 20,
//                                 height: 20,
//                                 child: CircularProgressIndicator(),
//                               ),
//                             ),
//                           ),
//                           errorWidget: (context, url, error) =>
//                               Icon(Icons.error),
//                           imageBuilder: (context, imageProvider) => Container(
//                             width: 40,
//                             height: 40,
//                             decoration: BoxDecoration(
//                               border: Border.all(color: Colors.white, width: 1),
//                               shape: BoxShape.circle,
//                               image: DecorationImage(
//                                 image: imageProvider,
//                                 fit: BoxFit.cover,
//                               ),
//                             ),
//                           ),
//                         )
//                       : Container(
//                           width: 40,
//                           height: 40,
//                           decoration: BoxDecoration(
//                             shape: BoxShape.circle,
//                             image: DecorationImage(
//                               image: const AssetImage(
//                                   'assets/background_image.png'),
//                               fit: BoxFit.cover,
//                             ),
//                           ),
//                         ),
//                   SizedBox(width: 10),
//                   Expanded(
//                     // Prevents overflow
//                     child: Column(
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       children: [
//                         Text(
//                           widget.chatName,
//                           style: const TextStyle(
//                               fontSize: 16, color: Colors.white),
//                           overflow: TextOverflow.ellipsis,
//                           maxLines: 1,
//                         ),
//                         Text(
//                           "$_status",
//                           style: TextStyle(fontSize: 12, color: Colors.white70),
//                         ),
//                       ],
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           ),
//         ),
//         body: Column(
//           children: [
//             Expanded(
//               child: Stack(
//                 children: [
//                   Container(
//                     padding: EdgeInsets.symmetric(vertical: 10, horizontal: 20),
//                     color: Colors.grey[200],
//                     child: ListView.builder(
//                       addAutomaticKeepAlives: false,
//                       cacheExtent: 1000,
//                       controller: _scrollController,
//                       reverse: true,
//                       itemCount: _processedMessages.length,
//                       itemBuilder: (context, index) {
//                         final processedMessage = _processedMessages[index];
//                         if (processedMessage["isDate"] == true) {
//                           return Center(
//                             child: Padding(
//                               padding:
//                                   const EdgeInsets.symmetric(vertical: 8.0),
//                               child: Container(
//                                 decoration: BoxDecoration(
//                                     color: Colors.white,
//                                     borderRadius: BorderRadius.circular(8.0)),
//                                 padding: EdgeInsets.symmetric(
//                                     horizontal: 10, vertical: 5),
//                                 child: Text(
//                                   processedMessage["date"]!,
//                                   style: const TextStyle(
//                                     fontSize: 12,
//                                     fontWeight: FontWeight.bold,
//                                     color: Colors.black54,
//                                   ),
//                                 ),
//                               ),
//                             ),
//                           );
//                         }
//                         final message =
//                             processedMessage['message'] as ChatMessage;
//                         final isMe = message.from ==
//                             ChatClient.getInstance.currentUserId;
//                         return _buildMessageBubble(message, isMe);
//                       },
//                     ),
//                   ),
//                   if (isLoading)
//                     Positioned(
//                       bottom: 10,
//                       left: MediaQuery.of(context).size.width / 2 - 15,
//                       child: SizedBox(),
//                     ),
//                 ],
//               ),
//             ),
//             MessageInput(
//               chatAgoraId: widget.chatAgoraId,
//               messageController: _messageController,
//               sendMessage: _sendMessage,
//               sendVCard: _sendVCard,
//               sendScheduleMeet: _sendMeetingRequest,
//             )
//           ],
//         ),
//       ),
//     );
//   }

//   Future<void> _sendMeetingRequest(Map<String, dynamic> meetingDetails) async {
//     if (!mounted) return;

//     try {
//       final creatorName = storage.read("user")?["fullname"];
//       final currentUserAgoraId =
//           ChatClient.getInstance.currentUserId?.toString() ?? '';
//       final chatUserAgoraId = widget.chatAgoraId;

//       final response = await http.post(
//         Uri.parse("https://${Constants.url}/meet/create-meeting"),
//         headers: {"Content-Type": "application/json"},
//         body: jsonEncode({
//           "creatorName": creatorName,
//           "creatorId": currentUserAgoraId,
//           "attendeeId": chatUserAgoraId,
//           "title": meetingDetails['title'].toString(),
//           "utcDateTime": meetingDetails['utcDateTime'].toString(),
//           "status": "Pending",
//         }),
//       );

//       if (response.statusCode == 200) {
//         final responseBody = jsonDecode(response.body);
//         final message = responseBody["message"];

//         ScaffoldMessenger.of(context).showSnackBar(
//           SnackBar(
//             content: Text(message),
//             backgroundColor: Colors.blueAccent,
//             duration: Duration(seconds: 1),
//           ),
//         );
//         if (mounted) {
//           widget.onSchedule(_findTabIndex('schedule'));
//           Get.back();
//         }
//       } else {
//         _showError("Failed to create meet: ${response.statusCode}");
//       }
//     } on Exception catch (e) {
//       // Handle general errors
//       _handleSendError('Failed to schedule meeting: ${e.toString()}');
//     }
//   }

//   // Future<void> _sendMeetingRequest(Map<String, dynamic> meetingDetails) async {
//   //   if (!mounted) return;

//   //   try {
//   //     final currentUserId = ChatClient.getInstance.currentUserId?.toString() ?? '';

//   //     // Create the message with proper type conversion
//   //     final message = ChatMessage.createCustomSendMessage(
//   //       targetId: widget.chatAgoraId,
//   //       event: "schedule_meeting",
//   //       params: {
//   //         'title': meetingDetails['title'].toString(),
//   //         'dateTime': meetingDetails['dateTime'].toString(),
//   //         'formattedDate': meetingDetails['formattedDate'].toString(),
//   //         'senderId': currentUserId,
//   //         'senderName': user["fullname"]?.toString() ?? 'Unknown',
//   //       },
//   //     );

//   //     // Configure push notification
//   //     // message.attributes = {
//   //     //   'em_force_notification': true,
//   //     //   'em_push_title': 'Meeting Scheduled',
//   //     //   'em_push_content':
//   //     //       '${meetingDetails['title']} at ${meetingDetails['formattedDate']}',
//   //     //   'em_apns_ext': {
//   //     //     'em_push_title': 'Meeting Scheduled',
//   //     //     'em_push_subtitle': meetingDetails['formattedDate'].toString(),
//   //     //   },
//   //     // };
//   //     message.attributes = {
//   //       'em_force_notification': true,
//   //       'em_ignore_notification': false, // Critical for Android
//   //       'em_push_title': 'Meeting Scheduled',
//   //       'em_push_content':
//   //           '${meetingDetails['title']} at ${meetingDetails['formattedDate']}',
//   //       'em_apns_ext': {
//   //         'em_push_title': 'Meeting Scheduled',
//   //         'em_push_subtitle': meetingDetails['formattedDate'].toString(),
//   //         'em_push_mutable_content': 1, // Required for iOS customization
//   //       },
//   //       'em_push_ext': {
//   //         // Android-specific overrides
//   //         'em_push_title': 'Meeting Scheduled',
//   //         'em_push_content':
//   //             '${meetingDetails['title']} at ${meetingDetails['formattedDate']}',
//   //       }
//   //     };

//   //     // Update UI
//   //     if (mounted) {
//   //       setState(() {
//   //         _messages.insert(0, message);
//   //         _processMessages();
//   //       });
//   //       WidgetsBinding.instance.addPostFrameCallback((_) {
//   //         if (mounted) _scrollController.jumpTo(0);
//   //       });
//   //     }

//   //     // Send message and schedule notification
//   //     await ChatClient.getInstance.chatManager.sendMessage(message);
//   //     // await _scheduleMeetingNotification(meetingDetails);

//   //     // Notify parent component
//   //     final currentTimestamp = DateTime.now().millisecondsSinceEpoch;
//   //     widget.onMessageSent(widget.chatAgoraId, currentTimestamp);
//   //   } on PlatformException catch (e) {
//   //     // Handle platform-specific errors
//   //     _handleSendError('Failed to send meeting request: ${e.message}');
//   //   } on Exception catch (e) {
//   //     // Handle general errors
//   //     _handleSendError('Failed to schedule meeting: ${e.toString()}');
//   //   }
//   // }

//   void _handleSendError(String errorMessage) {
//     print(errorMessage);
//     if (mounted) {
//       ScaffoldMessenger.of(context).showSnackBar(
//         SnackBar(content: Text(errorMessage)),
//       );
//     }
//   }

// //   Future<void> _scheduleMeetingNotification(Map<String, dynamic> meeting) async {
// //     try {
// //       // Get the timestamp directly (it's already in millisecondsSinceEpoch)
// //       final timestamp = meeting['dateTime'] as int;
// //       if (timestamp == 0) throw Exception('Invalid timestamp');

// //       final scheduledDate = DateTime.fromMillisecondsSinceEpoch(timestamp);

// //       final notificationId = scheduledDate.millisecondsSinceEpoch ~/ 1000;

// //       await AwesomeNotifications().createNotification(
// //         content: NotificationContent(
// //           id: notificationId,
// //           channelKey: 'scheduled_meetings',
// //           title: meeting['title']?.toString() ?? 'Meeting',
// //           body: 'Meeting with ${widget.chatName} at ${meeting['formattedDate']}',
// //           payload: {
// //             'type': 'meeting_reminder',
// //             'chatId': widget.chatAgoraId,
// //             'meetingData': jsonEncode(meeting),
// //           },
// //         ),
// //         schedule: NotificationCalendar.fromDate(
// //           date: scheduledDate.subtract(const Duration(minutes: 5)),
// //           allowWhileIdle: true,
// //         ),
// //       );
// //     } catch (e, stackTrace) {
// //       print('Error in _scheduleMeetingNotification: $e\n$stackTrace');
// //       rethrow;
// //     }
// //   }
//   int _findTabIndex(String pageIdentifier) {
//     final availableTabs = [
//       'home',
//       'agenda',
//       if (event['cmtconnect'] ?? false) 'cmtconnect',
//       if (event['networking'] ?? false) 'networking',
//       if (event['cmtconnect'] ?? false) 'schedule',
//       // if (event['watchLive'] ?? false) 'watchlive',
//     ];

//     return availableTabs.indexOf(pageIdentifier);
//   }
// }

// class MessageInput extends StatefulWidget {
//   final String chatAgoraId;
//   final TextEditingController messageController;
//   final Function sendMessage;
//   final Function sendVCard;
//   final Function sendScheduleMeet;

//   const MessageInput({
//     required this.chatAgoraId,
//     required this.messageController,
//     required this.sendMessage,
//     required this.sendVCard,
//     required this.sendScheduleMeet,
//     Key? key,
//   }) : super(key: key);

//   @override
//   _MessageInputState createState() => _MessageInputState();
// }

// class _MessageInputState extends State<MessageInput>
//     with SingleTickerProviderStateMixin {
//   late AnimationController _animationController;
//   late Animation<double> _iconAnimation;
//   bool _showOptions = false;
//   final LayerLink _layerLink = LayerLink();
//   OverlayEntry? _overlayEntry;

//   @override
//   void initState() {
//     super.initState();
//     _animationController = AnimationController(
//       vsync: this,
//       duration: const Duration(milliseconds: 200),
//     );
//     _iconAnimation = CurvedAnimation(
//       parent: _animationController,
//       curve: Curves.easeInOut,
//     );
//   }

//   @override
//   void dispose() {
//     _animationController.dispose();
//     _removeOverlay();
//     super.dispose();
//   }

//   void _toggleOptions() {
//     FocusManager.instance.primaryFocus?.unfocus();
//     setState(() {
//       _showOptions = !_showOptions;
//       if (_showOptions) {
//         _animationController.forward();
//         _showOverlay();
//       } else {
//         _animationController.reverse();
//         _removeOverlay();
//       }
//     });
//   }

//   // Custom Add/Close icon widget
//   Widget _buildAddCloseIcon() {
//     return AnimatedBuilder(
//       animation: _iconAnimation,
//       builder: (context, child) {
//         return Transform.rotate(
//           angle: _iconAnimation.value * 0.8, // Optional rotation effect
//           child: Stack(
//             alignment: Alignment.center,
//             children: [
//               Opacity(
//                 opacity: 1 - _iconAnimation.value,
//                 child: const Icon(
//                   Icons.add,
//                   color: Color(0xffc01058),
//                   size: 36,
//                 ),
//               ),
//               Opacity(
//                 opacity: _iconAnimation.value,
//                 child: const Icon(
//                   Icons.add,
//                   color: Color(0xffc01058),
//                   size: 36,
//                 ),
//               ),
//             ],
//           ),
//         );
//       },
//     );
//   }

//   void _showOverlay() {
//     _overlayEntry = OverlayEntry(
//       builder: (context) => Positioned(
//         width: 230,
//         child: CompositedTransformFollower(
//           link: _layerLink,
//           showWhenUnlinked: false,
//           offset: const Offset(10, -140),
//           child: Material(
//             elevation: 2,
//             borderRadius: BorderRadius.circular(8),
//             child: Container(
//               padding: EdgeInsets.symmetric(vertical: 10),
//               decoration: BoxDecoration(
//                 color: Colors.white,
//                 borderRadius: BorderRadius.circular(8),
//                 boxShadow: [
//                   BoxShadow(
//                     color: Colors.black.withAlpha(20),
//                     blurRadius: 10,
//                     spreadRadius: 2,
//                   ),
//                 ],
//               ),
//               child: Column(
//                 mainAxisSize: MainAxisSize.min,
//                 children: [
//                   _buildOptionItem(Icons.account_box_rounded, "Send Contact"),
//                   Divider(thickness: 0.5, color: Colors.grey[300]),
//                   _buildOptionItem(
//                       Icons.event_note_sharp, "Schedule a Meeting"),
//                 ],
//               ),
//             ),
//           ),
//         ),
//       ),
//     );
//     Overlay.of(context).insert(_overlayEntry!);
//   }

//   void _showScheduleMeetingDialog() {
//     try {
//       showDialog(
//         context: context,
//         builder: (context) {
//           try {
//             return ScheduleMeetingDialog(
//               onSchedule: (meetingDetails) {
//                 try {
//                   widget.sendScheduleMeet(meetingDetails);
//                 } catch (e) {
//                   print('Error in onSchedule callback: $e');
//                   if (mounted) {
//                     ScaffoldMessenger.of(context).showSnackBar(
//                       const SnackBar(
//                           content: Text('Failed to process meeting')),
//                     );
//                   }
//                 }
//               },
//             );
//           } catch (e) {
//             print('Error building dialog: $e');
//             return AlertDialog(
//               title: const Text('Error'),
//               content: const Text('Could not load meeting scheduler'),
//               actions: [
//                 TextButton(
//                   onPressed: () => Navigator.pop(context),
//                   child: const Text('OK'),
//                 ),
//               ],
//             );
//           }
//         },
//       );
//     } catch (e, stackTrace) {
//       print('Error showing dialog: $e\n$stackTrace');
//     }
//   }

//   Widget _buildOptionItem(IconData icon, String text) {
//     return ListTile(
//       dense: true,
//       leading: Icon(icon, size: 30, color: Color(0xffc01058)),
//       title: Text(text, style: TextStyle(fontSize: 14)),
//       onTap: () {
//         _removeOverlay();
//         setState(() {
//           _showOptions = false;
//           _animationController.reverse();
//         });
//         if (text == "Send Contact") {
//           widget.sendVCard();
//         } else if (text == "Schedule a Meeting") {
//           _showScheduleMeetingDialog();
//         }
//       },
//     );
//   }

//   void _removeOverlay() {
//     _overlayEntry?.remove();
//     _overlayEntry = null;
//   }

//   @override
//   Widget build(BuildContext context) {
//     return CompositedTransformTarget(
//       link: _layerLink,
//       child: Container(
//         padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 1.0),
//         color: Colors.grey[200],
//         child: Column(
//           children: [
//             if (_showOptions) const SizedBox(height: 8),
//             Row(
//               children: [
//                 IconButton(
//                   icon: _buildAddCloseIcon(),
//                   onPressed: _toggleOptions,
//                 ),
//                 Expanded(
//                   child: Container(
//                     constraints: const BoxConstraints(
//                       minHeight: 45,
//                       maxHeight: 120,
//                     ),
//                     decoration: BoxDecoration(
//                       color: Colors.white,
//                       borderRadius: BorderRadius.circular(24.0),
//                     ),
//                     child: TextField(
//                       controller: widget.messageController,
//                       maxLines: null,
//                       minLines: 1,
//                       keyboardType: TextInputType.multiline,
//                       textInputAction: TextInputAction.newline,
//                       decoration: const InputDecoration(
//                         hintText: 'Type a message',
//                         border: InputBorder.none,
//                         contentPadding: EdgeInsets.symmetric(
//                             horizontal: 16.0, vertical: 11.5),
//                       ),
//                     ),
//                   ),
//                 ),
//                 IconButton(
//                   icon: Container(
//                     padding: const EdgeInsets.only(left: 5),
//                     height: 40,
//                     width: 40,
//                     decoration: const BoxDecoration(
//                       color: Color(0xffc01058),
//                       borderRadius:
//                           BorderRadius.all(Radius.elliptical(9999.0, 9999.0)),
//                     ),
//                     child: const Icon(
//                       Icons.send,
//                       color: Colors.white,
//                       size: 22,
//                     ),
//                   ),
//                   onPressed: () => widget.sendMessage(),
//                 ),
//               ],
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
