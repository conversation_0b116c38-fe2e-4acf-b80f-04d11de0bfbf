import 'dart:convert';

import 'package:agora_chat_sdk/agora_chat_sdk.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:cmtmeet/pages/chat_page.dart';
import 'package:cmtmeet/service/firebase_service.dart';
// import 'package:cmtmeet/pages/chat_page_china.dart';
// import 'package:cmtmeet/service/firebase_service_china.dart';
import 'package:cmtmeet/service/websocket_service.dart';
import 'package:cmtmeet/utils/constants.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:get_storage/get_storage.dart';

class CmtConnectPage extends StatefulWidget {
  final String initialSection;
  final Function(int) onTabChange;
  const CmtConnectPage({
    super.key,
    this.initialSection = 'Online',
    required this.onTabChange,
  });

  @override
  _CmtConnectPageState createState() => _CmtConnectPageState();
}

class _CmtConnectPageState extends State<CmtConnectPage> {
  final WebSocketService webSocketService = Get.find<WebSocketService>();
  final GetStorage storage = GetStorage();
  late final String? eventCode;
  final Map<String, String> _userStatus = {};
  String _searchText = "";
  late String _selectedSection;
  Future<List<dynamic>>? _filteredAttendeesFuture;
  late void Function(dynamic, dynamic, dynamic) _statusListener;
  Set<String> _selectedIndustrySegments = {};
  static bool isAttendeesFetching = false;
  bool _navigatingToChat = false;

  int _lastMessageTime = DateTime.now().millisecondsSinceEpoch;
  final Map<String, int> lastMessageTimestamps = {}; // Stores Agora ID → Timestamp
  TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    ChatHandlerManager.initializeConnect(
      updateState: _updateConnectState,
      timestamps: lastMessageTimestamps,
    );
    refreshAttendees();
    _selectedSection = widget.initialSection;
    // Check for chat redirection
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _handleNotificationRedirection();
    });

    _statusListener = (userId, eventCode, status) {
      final key = "$userId-$eventCode";
      if (mounted) {
        setState(() {
          _userStatus[key] = status;
          _refreshFilteredAttendees();
        });
      }
    };

    webSocketService.addStatusListener(_statusListener);

    setState(() {
      _userStatus.addAll(webSocketService.userStatuses);
    });

    eventCode = storage.read("event")?["eventCode"];
    _loadInitialMessages();
    _refreshFilteredAttendees();
    print('CMT Connect Page Initialized');
  }

  void _updateConnectState() {
    if (mounted) {
      setState(() {
        _lastMessageTime = DateTime.now().millisecondsSinceEpoch;
      });
    }
  }

  void _handleNotificationRedirection() {
    final args = Get.arguments;
    if (args != null && args['openChatWith'] != null) {
      final agoraId = args['openChatWith'];
      // Clear the parameter immediately after reading
      Get.arguments['openChatWith'] = '';
      _openChatForUser(agoraId);
    }
  }

  Future<void> _openChatForUser(String agoraId) async {
    final attendees = storage.read<List<dynamic>>('attendees') ?? [];

    final attendee = attendees.firstWhere(
      (a) => a['agoraid'] == agoraId,
      orElse: () => null,
    );

    if (attendee != null) {
      setState(() => _navigatingToChat = true);

      await Get.to(() => ChatScreen(
            chatId: attendee["id"].toString(),
            chatAgoraId: attendee["agoraid"].toString(),
            chatImage: attendee["profileimg"],
            chatName: attendee["fullname"],
            chatJob: attendee["jobtitle"],
            chatCompany: attendee["companyname"],
            onExit: () => _reinitializeChatListener(),
            onMessageSent: (agoraId, timestamp) {
              lastMessageTimestamps[agoraId] = timestamp;
              _refreshFilteredAttendees();
            },
            onSchedule: (index) => widget.onTabChange(index),
          ));

      setState(() => _navigatingToChat = false);
    }
  }

  Future<void> refreshAttendees() async {
    if (isAttendeesFetching) return;
    isAttendeesFetching = true;
    try {
      final agoraid = storage.read("user")?["agoraid"];
      final url = Uri.parse("https://${Constants.url}/auth/refresh-attendees");
      final response = await http.post(
        url,
        headers: {"Content-Type": "application/json"},
        body: jsonEncode({"agoraid": agoraid}),
      );

      final responseData = await compute(jsonDecode, response.body);

      if (responseData["message"] == "Attendees fetched") {
        await storage.write("attendees", responseData["attendees"]);
      }
    } catch (e) {
      print('Attendees refresh failed: $e');
    } finally {
      isAttendeesFetching = false;
    }
  }

  @override
  void dispose() {
    webSocketService.removeStatusListener(_statusListener);
    ChatHandlerManager.currentScreen = null;
    ChatHandlerManager.updateConnectPageState = null;
    if (!_navigatingToChat) {
      ChatHandlerManager.initializeGlobal();
    }

    print('CMT Connect Page Disposed');
    super.dispose();
  }

  /// Fetch last messages for all users and store their timestamps
  Future<void> _loadInitialMessages() async {
    ChatCursorResult<ChatConversation> result = await ChatClient.getInstance.chatManager
        .fetchConversationsByOptions(options: ConversationFetchOptions(pageSize: 40));
    List<ChatConversation> attendeesMessages = await result.data;
    for (var attendeeMessages in attendeesMessages) {
      final attendeeId = await attendeeMessages.id;
      final latestMessage = await attendeeMessages.latestMessage();
      if (latestMessage != null) {
        lastMessageTimestamps[attendeeId] = latestMessage.serverTime;
      }
    }
  }

  /// Filter attendees based on search text, industry segments, and section
  Future<List<dynamic>> _filterAttendees(List<dynamic> attendees) async {
    final List<dynamic> filteredAttendees = [];

    // If "Recent" section is selected, update timestamps for users with unread messages
    if (_selectedSection == "Recent") {
      await _updateRecentMessages();
    }

    // Process filtering in parallel
    final List<Future<bool>> filteredFutures = attendees.map((attendee) async {
      final name = attendee["fullname"]?.toLowerCase() ?? "";
      final status = _userStatus['${attendee["id"].toString()}-$eventCode'] ?? "offline";
      final industrySegment = attendee["industrysegment"] ?? "";
      final userAgoraId = attendee["agoraid"].toString();
      final attendeeType = attendee["attendeetype"];

      // Match search text
      final matchesSearch =
          _searchText.isEmpty || name.contains(_searchText.toLowerCase());

      // Match industry segment
      final matchesIndustry = _selectedIndustrySegments.isEmpty ||
          _selectedIndustrySegments.contains(industrySegment);

      if (_selectedSection == "Online") {
        return matchesSearch && matchesIndustry && status == "online";
      } else if (_selectedSection == "Speakers") {
        return matchesSearch && matchesIndustry && attendeeType == 6;
      } else if (_selectedSection == "Recent") {
        // Use cached timestamp if available
        if (lastMessageTimestamps.containsKey(userAgoraId)) {
          return matchesSearch && matchesIndustry;
        }
        return false;
      }

      // Default: Show all users matching filters
      return matchesSearch && matchesIndustry && attendeeType != 6;
    }).toList();

    // Execute filtering in parallel
    final results = await Future.wait(filteredFutures);

    // Collect valid attendees
    for (int i = 0; i < attendees.length; i++) {
      if (results[i]) filteredAttendees.add(attendees[i]);
    }

    // Sort recent attendees by newest message timestamp
    if (_selectedSection == "Recent") {
      filteredAttendees.sort((a, b) {
        int timeA = lastMessageTimestamps[a["agoraid"]] ?? 0;
        int timeB = lastMessageTimestamps[b["agoraid"]] ?? 0;
        return timeB.compareTo(timeA); // Newest first
      });
    }

    return filteredAttendees;
  }

  /// Update recent messages for users with unread messages
  Future<void> _updateRecentMessages() async {
    ChatCursorResult<ChatConversation> result = await ChatClient.getInstance.chatManager
        .fetchConversationsByOptions(options: ConversationFetchOptions(pageSize: 40));
    List<ChatConversation> attendeesMessages = await result.data;
    for (var attendeeMessages in attendeesMessages) {
      final attendeeId = await attendeeMessages.id;
      final latestMessage = await attendeeMessages.latestMessage();
      if (latestMessage != null) {
        lastMessageTimestamps[attendeeId] = latestMessage.serverTime;
      }
    }
  }

  /// Refresh the filtered attendees list
  void _refreshFilteredAttendees() {
    final List<dynamic> attendees = storage.read<List<dynamic>>('attendees') ?? [];
    setState(() {
      _filteredAttendeesFuture = _filterAttendees(attendees);
    });
  }

  /// Update unread counts and refresh the list
  void updateUnreadCounts() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          _refreshFilteredAttendees();
        });
      }
    });
  }

  /// Get unread message count for a user
  Future<int> _getUnreadCount(String userAgoraId) async {
    ChatConversation? conversation = await ChatClient.getInstance.chatManager
        .getConversation(userAgoraId, type: ChatConversationType.Chat);

    return await conversation?.unreadCount() ?? 0;
  }

  // ... (Rest of the code remains unchanged)
  void _showFilterDialog() {
    List<String> availableSegments = _getAvailableIndustrySegments();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              title: Text("Select Industry Segments"),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: availableSegments.map((segment) {
                    return CheckboxListTile(
                      title: Text(segment),
                      value: _selectedIndustrySegments.contains(segment),
                      onChanged: (bool? selected) {
                        setDialogState(() {
                          if (selected == true) {
                            _selectedIndustrySegments.add(segment);
                          } else {
                            _selectedIndustrySegments.remove(segment);
                          }
                        });
                      },
                    );
                  }).toList(),
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context); // Close dialog
                  },
                  child: Text("Cancel"),
                ),
                TextButton(
                  onPressed: () {
                    setState(() {}); // Refresh UI with selected filters
                    Navigator.pop(context);
                    _refreshFilteredAttendees(); // Apply filter
                  },
                  child: Text("Apply"),
                ),
              ],
            );
          },
        );
      },
    );
  }

  List<String> _getAvailableIndustrySegments() {
    final attendees = storage.read<List<dynamic>>('attendees') ?? [];
    final segments = attendees
        .map((attendee) => attendee["industrysegment"]?.toString() ?? "Unknown")
        .toSet()
        .toList();
    segments.sort();
    return segments;
  }

  void _reinitializeChatListener() {
    updateUnreadCounts();
    ChatHandlerManager.initializeConnect(
      updateState: _updateConnectState,
      timestamps: lastMessageTimestamps,
    );
  }

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return Scaffold(
      body: Column(
        children: [
          // Search Bar
          Padding(
            padding: const EdgeInsets.all(12.0),
            child: Container(
              height: 45,
              child: TextField(
                controller: _searchController,
                onChanged: (value) {
                  setState(() {
                    _searchText = value;
                  });
                  _refreshFilteredAttendees();
                },
                cursorHeight: 20,
                decoration: InputDecoration(
                  contentPadding: const EdgeInsets.symmetric(vertical: 0),
                  hintText: "Search attendees",
                  prefixIcon: Icon(Icons.search_sharp),
                  suffixIcon: IconButton(
                    splashRadius: 5,
                    icon: Icon(Icons.filter_list),
                    onPressed: () {
                      _showFilterDialog();
                    },
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(30.0),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(30.0),
                    borderSide: BorderSide(
                      color: Color.fromARGB(255, 175, 16, 82),
                    ),
                  ),
                  filled: true,
                  fillColor: Colors.grey[200],
                ),
              ),
            ),
          ),

          Column(
            children: [
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: ["Online", "Attendees", "Speakers", "Recent"].map((section) {
                    final isSelected = _selectedSection == section;
                    return GestureDetector(
                      onTap: () {
                        setState(() {
                          _selectedSection = section;
                          _searchText = ""; // Clear search text
                          _searchController.clear();
                        });
                        _refreshFilteredAttendees();
                      },
                      child: Container(
                        // width: width / 3.5,
                        padding: EdgeInsets.symmetric(vertical: 3.0, horizontal: 16.0),
                        margin: EdgeInsets.only(left: 8.0, right: 8.0, bottom: 10.0),
                        decoration: BoxDecoration(
                          color: isSelected
                              ? Color.fromARGB(255, 175, 16, 82)
                              : Colors.grey[300],
                          borderRadius: BorderRadius.circular(20.0),
                        ),
                        child: Text(
                          section,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: isSelected ? Colors.white : Colors.black,
                            fontSize: 16.0,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
              SizedBox(
                height: 1.0,
                width: width,
                child: Container(color: Colors.grey[400]),
              ),
            ],
          ),

          // Attendees List
          Expanded(
            child: FutureBuilder<List<dynamic>>(
              future: _filteredAttendeesFuture,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                } else if (snapshot.hasError) {
                  // return const Center(child: Text('Error loading attendees'));
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'Error loading attendees',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        SizedBox(height: 10), // Add some spacing
                        Text(
                          'Error: ${snapshot.error}',
                          style: TextStyle(fontSize: 14, color: Colors.red),
                        ),
                        SizedBox(height: 10), // Add some spacing
                        Text(
                          'Stack Trace: ${snapshot.stackTrace}',
                          style: TextStyle(fontSize: 12, color: Colors.grey),
                        ),
                      ],
                    ),
                  );
                } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
                  return const Center(child: Text('No attendees available'));
                }

                final filteredAttendees = snapshot.data!;
                return ListView.builder(
                  itemCount: filteredAttendees.length,
                  itemBuilder: (context, index) {
                    final attendee = filteredAttendees[index] as Map<String, dynamic>;
                    final userAgoraId = attendee["agoraid"].toString();
                    final userId = attendee["id"].toString();
                    final status = _userStatus['$userId-$eventCode'] ?? 'offline';

                    return InkWell(
                      child: Column(
                        children: [
                          Container(
                            // height: height * 0.2,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 10.0, vertical: 7.0),
                            child: Row(
                              children: [
                                Container(
                                  width: width * 0.15,
                                  margin: EdgeInsets.only(right: 15),
                                  child: Stack(
                                    children: [
                                      attendee['profileimg'] != ''
                                          ? CachedNetworkImage(
                                              imageUrl: attendee['profileimg'],
                                              placeholder: (context, url) => Container(
                                                width: 90,
                                                height: 90,
                                                child: Center(
                                                  child: SizedBox(
                                                    width: 20,
                                                    height: 20,
                                                    child: CircularProgressIndicator(),
                                                  ),
                                                ),
                                              ),
                                              errorWidget: (context, url, error) =>
                                                  Icon(Icons.error),
                                              imageBuilder: (context, imageProvider) =>
                                                  Container(
                                                width: 60,
                                                height: 60,
                                                decoration: BoxDecoration(
                                                  shape: BoxShape.circle,
                                                  image: DecorationImage(
                                                    image: imageProvider,
                                                    fit: BoxFit.cover,
                                                  ),
                                                ),
                                              ),
                                            )
                                          : Container(
                                              width: 60,
                                              height: 60,
                                              decoration: BoxDecoration(
                                                shape: BoxShape.circle,
                                                image: DecorationImage(
                                                  image: const AssetImage(
                                                      'assets/background_image.png'),
                                                  fit: BoxFit.cover,
                                                ),
                                              ),
                                            ),
                                      Positioned(
                                        bottom: 3,
                                        right: 3,
                                        child: CircleAvatar(
                                          radius: 7,
                                          child: CircleAvatar(
                                            radius: 5,
                                            backgroundColor: status == 'online'
                                                ? Color.fromARGB(255, 0, 153, 18)
                                                : status == 'meeting'
                                                    ? Colors.blue
                                                    : Colors.grey,
                                          ),
                                          backgroundColor: Colors.white,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Container(
                                  width: width * 0.55,
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        attendee["fullname"] ?? '',
                                        style: TextStyle(
                                          fontSize: 18.0,
                                          fontWeight: FontWeight.bold,
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                      attendee["jobtitle"] == ''
                                          ? const SizedBox()
                                          : Text(
                                              '${attendee["jobtitle"] ?? ''}',
                                              style: TextStyle(
                                                fontSize: 14.0,
                                              ),
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                      attendee["companyname"] == ''
                                          ? const SizedBox()
                                          : Text(
                                              attendee["companyname"] ?? '',
                                              style: TextStyle(
                                                fontSize: 14.0,
                                              ),
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                    ],
                                  ),
                                ),
                                Container(
                                  width: width * 0.20,
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                    crossAxisAlignment: CrossAxisAlignment.end,
                                    children: [
                                      Row(
                                        mainAxisAlignment: MainAxisAlignment.end,
                                        children: [
                                          FutureBuilder<int>(
                                            // Use _lastMessageTime as a key to force a rebuild
                                            key: ValueKey(_lastMessageTime),
                                            future: _getUnreadCount(userAgoraId),
                                            builder: (context, snapshot) {
                                              if (snapshot.connectionState ==
                                                  ConnectionState.waiting) {
                                                return const SizedBox();
                                              }
                                              if (snapshot.hasData &&
                                                  snapshot.data! > 0) {
                                                return Container(
                                                  padding: const EdgeInsets.symmetric(
                                                      vertical: 2, horizontal: 10),
                                                  decoration: BoxDecoration(
                                                    color:
                                                        Color.fromARGB(255, 175, 16, 82),
                                                    borderRadius:
                                                        BorderRadius.circular(12),
                                                  ),
                                                  child: Text(
                                                    '${snapshot.data}',
                                                    style: const TextStyle(
                                                        color: Colors.white,
                                                        fontWeight: FontWeight.bold),
                                                  ),
                                                );
                                              }
                                              return const SizedBox();
                                            },
                                          ),
                                        ],
                                      ),
                                      SizedBox(height: 7),
                                      Icon(Icons.arrow_forward_ios_sharp)
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(
                            height: 1.0,
                            width: width * 0.95,
                            child: Container(color: Colors.grey[400]),
                          ),
                        ],
                      ),
                      onTap: () {
                        final chatId = userId;
                        final chatAgoraId = userAgoraId;

                        Get.to(() => ChatScreen(
                              chatId: chatId,
                              chatAgoraId: chatAgoraId,
                              chatImage: attendee["profileimg"],
                              chatName: attendee["fullname"],
                              chatJob: attendee["jobtitle"],
                              chatCompany: attendee["companyname"],
                              onExit: () => _reinitializeChatListener(),
                              onMessageSent: (agoraId, timestamp) {
                                lastMessageTimestamps[agoraId] = timestamp;
                                _refreshFilteredAttendees();
                              },
                              onSchedule: (index) => widget.onTabChange(index),
                            ));
                      },
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
