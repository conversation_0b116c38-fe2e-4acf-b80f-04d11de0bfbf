import 'package:cmtmeet/utils/constants.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:url_launcher/url_launcher.dart';

class ConsentPage extends StatefulWidget {
  ConsentPage({super.key});

  @override
  State<ConsentPage> createState() => _ConsentPageState();
}

class _ConsentPageState extends State<ConsentPage> {
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          title: Text('User Consent'),
          foregroundColor: Colors.white,
          backgroundColor: Color(0xffc01058),
        ),
        body: SingleChildScrollView(
          padding: EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'User Consent',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedB<PERSON>(height: 16),
              Text(
                'By continuing to use this app, you agree to the following:',
                style: TextStyle(fontSize: 16),
              ),
              <PERSON><PERSON><PERSON><PERSON>(height: 16),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildConsentItem(
                    bullet: true,
                    index: 1,
                    title: 'Data Usage for Core Functionality',
                    children: [
                      'Our app displays externally collected information from our backend server/website during event registration.',
                      'This information may be viewed by other registered participants who are securely logged in, for networking purposes only.',
                      'Your details are not visible to participants of other events.',
                      'We may share minimal data, such as (user id, FCM token) with trusted third-party services (Agora & Google Firebase) solely to enable core app features.',
                      'This data is handled securely and not used for advertising or resale.',
                    ],
                  ),
                  SizedBox(height: 16),
                  _buildConsentItem(
                    bullet: false,
                    index: 2,
                    title: 'Profile update (Optional)',
                    children: [
                      'We collect only the minimum information necessary to provide optional profile features, such as:',
                      '●  Full Name\n●  Job Title\n●  Company Name\n●  Country\n●  Phone/Mobile Number\n●  Email',
                      'Filling out your profile helps improve communication and networking with other business participants but is entirely optional.',
                      'Users can optionally sync their name, email ID, and profile picture from LinkedIn using the “Sign in with LinkedIn” option, with their consent. This feature is entirely optional and not required to use the app.',
                    ],
                  ),
                  SizedBox(height: 16),
                  _buildConsentItem(
                    bullet: true,
                    index: 3,
                    title: 'Microphone and Camera Access',
                    children: [
                      'We request access to your microphone and camera to support real-time audio and video communication features.',
                      'We do not record your conversations.',
                      'We do not track or monitor your audio/video activity or chat messages.',
                      'Permissions are requested only when you\'re actively using these features.',
                      'Access to your camera and microphone is strictly limited to business conferences and event-related meetings within the app.',
                    ],
                  ),
                  SizedBox(height: 16),
                  _buildConsentItem(
                    bullet: false,
                    index: 4,
                    title: 'Your Privacy is Important',
                    children: [
                      'We do not sell or rent your personal data.',
                      'All information is safeguarded using standard industry security practices.',
                    ],
                  ),
                  SizedBox(height: 16),
                  _buildConsentItem(
                    bullet: false,
                    index: 5,
                    title: 'Acknowledgement',
                    children: [
                      'By checking the box and signing in to the app, you acknowledge and consent to the above.',
                    ],
                  ),
                  SizedBox(height: 16),
                  _buildConsentItem(
                    bullet: false,
                    index: 6,
                    title: 'Contact Us',
                    children: [
                      'If you have any questions, feedback, or concerns regarding this consent statement, please contact us at: <EMAIL>',
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildConsentItem(
      {required bool bullet,
      required int index,
      required String title,
      required List<String> children}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          text: TextSpan(
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
            children: [
              TextSpan(text: '$index. '),
              TextSpan(text: title),
            ],
          ),
        ),
        SizedBox(height: 8),
        ...children.map((text) {
          if (text.contains('<EMAIL>')) {
            final parts = text.split('<EMAIL>');
            return Padding(
              padding: const EdgeInsets.only(left: 16.0, top: 4.0, bottom: 4.0),
              child: RichText(
                text: TextSpan(
                  style: const TextStyle(fontSize: 16, color: Colors.black),
                  children: [
                    TextSpan(text: parts[0]),
                    TextSpan(
                      text: '<EMAIL>',
                      style: TextStyle(
                        color: Constants.colorApp,
                        fontFamily: 'Roboto',
                      ),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () async {
                          final Uri emailLaunchUri = Uri(
                            scheme: 'mailto',
                            path: '<EMAIL>',
                          );
                          try {
                            await launchUrl(
                              emailLaunchUri,
                              mode: LaunchMode.externalApplication,
                            );
                          } catch (e) {
                            Fluttertoast.showToast(
                              msg: 'Could not launch email client. Error: $e',
                              toastLength: Toast.LENGTH_SHORT,
                              gravity: ToastGravity.BOTTOM,
                              timeInSecForIosWeb: 1,
                              backgroundColor: Colors.red,
                              textColor: Colors.white,
                              fontSize: 16.0,
                            );
                          }
                        },
                    ),
                    if (parts.length > 1) TextSpan(text: parts[1]),
                  ],
                ),
              ),
            );
          }
          return Padding(
            padding: EdgeInsets.only(left: 16.0, top: 4.0, bottom: 4.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                bullet ? Text('●  ', style: TextStyle(fontSize: 14)) : SizedBox(),
                Expanded(
                  child: Text(
                    text,
                    style: TextStyle(fontSize: 16),
                  ),
                ),
              ],
            ),
          );
        }),
      ],
    );
  }
}
