import 'package:cmtmeet/custom_widgets/custom_button_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class GetStartPage extends StatelessWidget {
  const GetStartPage({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: <PERSON>af<PERSON>(
        backgroundColor: Colors.white,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                'assets/cmt-logo.png',
                width: 200,
                height: 180,
              ),
              CustomElevatedButton(
                onPressed: () {
                  Get.toNamed('/onboarding');
                },
                text: 'Get Started',
                width: 150,
                height: 42,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
