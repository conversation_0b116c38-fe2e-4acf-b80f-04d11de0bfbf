import 'package:cmtmeet/pages/profile_page.dart';
import 'package:cmtmeet/pages/sponsor_logo.dart';
import 'package:cmtmeet/service/websocket_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

class HomePage extends StatefulWidget {
  final Function(int) onTabChange;
  HomePage({Key? key, required this.onTabChange});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final WebSocketService webSocketService = Get.find<WebSocketService>();
  late void Function(dynamic, dynamic, dynamic) _statusListener;
  late void Function(Map<String, dynamic>) _roomListeners;

  final storage = GetStorage();
  late Map<String, dynamic> user;
  late Map<String, dynamic> event;
  late Map<String, dynamic> sponsors;

  Map<String, String> _userStatus = {};
  Map<String, dynamic> roomDetails = {};
  int _onlineCount = 0;
  int _roomOnlineCount = 0;

  @override
  void initState() {
    super.initState();
    user = storage.read("user") ?? {};
    event = storage.read("event") ?? {};
    sponsors = storage.read("sponsors") ?? {};

    _statusListener = (userId, eventCode, status) {
      final key = "$userId-$eventCode";

      if (mounted) {
        setState(() {
          _userStatus[key] = status;
          _updateOnlineCount();
        });
      }
    };

    webSocketService.addStatusListener(_statusListener);

    setState(() {
      _userStatus.addAll(webSocketService.userStatuses);
    });

    _updateOnlineCount();

    _roomListeners = (updatedRooms) {
      if (mounted) {
        setState(() {
          updatedRooms.forEach((roomKey, roomData) {
            roomDetails[roomKey] = Map<String, dynamic>.from(roomData);
          });
          _updateRoomOnlineCount();
        });
      }
    };

    webSocketService.addRoomListener(_roomListeners);

    setState(() {
      roomDetails.addAll(webSocketService.roomDetails);
    });

    _updateRoomOnlineCount();
  }

  void _updateOnlineCount() {
    final newCount = _userStatus.values.where((status) => status == "online").length;
    setState(() {
      _onlineCount = newCount;
    });
  }

  void _updateRoomOnlineCount() {
    final newCount = roomDetails.values.fold<int>(
      0,
      (sum, room) => sum + ((room['members'] as List?)?.length ?? 0),
    );
    setState(() {
      _roomOnlineCount = newCount;
    });
  }

  @override
  void dispose() {
    webSocketService.removeStatusListener(_statusListener);
    webSocketService.removeRoomListener(_roomListeners);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final sponsorsData = sponsors['data'] as List?;
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(15.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 0),
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Colors.grey, width: 1.0), // Bottom border
                ),
              ),
              child: Row(
                children: [
                  // Left Column (70%) - Welcome & User Name
                  Expanded(
                    flex: 7, // 70%
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "Welcome!",
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Colors.black54,
                          ),
                        ),
                        SizedBox(height: 1),
                        Text(
                          user["fullname"] ?? '',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Color.fromARGB(255, 143, 20, 20),
                          ),
                        ),
                        Text(
                          "Email: ${user["email"]}",
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Colors.black54,
                          ),
                        ),
                        Text(
                          "Phone: ${user['phone']}",
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Colors.black54,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Right Column (30%) - Edit Profile Button
                  Expanded(
                    flex: 3, // 30%
                    child: SizedBox(
                      // height: 30, // Button height
                      child: ElevatedButton(
                        onPressed: () async {
                          // Add your edit profile function here
                          final needsRefresh = await Get.to(() => ProfilePage());
                          if (needsRefresh == true && mounted) {
                            setState(() {
                              user = storage.read("user") ?? {};
                            });
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          padding: EdgeInsets.zero, // No padding
                          backgroundColor: const Color.fromRGBO(0, 32, 177, 1),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20.0), // Rounded corners
                          ),
                        ),
                        child: const Text(
                          "Edit Profile",
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 10),
            // Image Card
            Card(
              margin: const EdgeInsets.all(0),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10.0),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(10.0),
                child: Image.network(
                  'https://cdn.cmtevents.com/cmtstaticcontent/eventdatas/${event['eventCode']}/banner/${event['eventCode']}_740.jpg',
                  width: double.infinity,
                  fit: BoxFit.cover,
                  height: 170, // Adjust height as needed
                ),
              ),
            ),

            if (sponsors['count'] > 0)
              Container(
                color: Colors.white, // White background
                child: SizedBox(
                  height: 100, // Adjust height as needed
                  child: SponsorLogos(
                      sponsors: sponsorsData?.cast<Map<String, dynamic>>() ?? []),
                ),
              ),

            const SizedBox(height: 20),

            Card(
              margin: const EdgeInsets.all(0),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10.0),
              ),
              elevation: 2,
              color: Colors.white,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 10.0),
                    decoration: const BoxDecoration(
                      color: Colors.black54,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(10.0),
                        topRight: Radius.circular(10.0),
                      ),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 5,
                          child: Text(
                            "Agenda",
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        Expanded(flex: 5, child: Container()),
                      ],
                    ),
                  ),
                  SizedBox(height: 10),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 10.0),
                    child: Text(
                      "Detailed information on timings and speakers for each session.",
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  SizedBox(height: 5),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 10.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        SizedBox(
                          width: width * 0.4,
                          child: ElevatedButton(
                            onPressed: () {
                              widget.onTabChange(1);
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Color(0xffc01058),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(20.0),
                              ),
                              padding: EdgeInsets.symmetric(horizontal: 20, vertical: 0),
                            ),
                            child: Text(
                              "View",
                              style: TextStyle(color: Colors.white),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 20),

            if (event['cmtconnect'])
              Card(
                margin: const EdgeInsets.all(0),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10.0),
                ),
                elevation: 2,
                color: Colors.white,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Container(
                      padding:
                          const EdgeInsets.symmetric(vertical: 8.0, horizontal: 10.0),
                      decoration: const BoxDecoration(
                        color: Colors.black54,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(10.0),
                          topRight: Radius.circular(10.0),
                        ),
                      ),
                      child: Row(
                        children: [
                          // Left Column: Time
                          const Expanded(
                            flex: 5,
                            child: Text(
                              "CMT Connect",
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          Expanded(flex: 5, child: Container()),
                        ],
                      ),
                    ),
                    const SizedBox(height: 10),
                    const Padding(
                      padding: EdgeInsets.symmetric(horizontal: 10.0),
                      child: Text(
                        "Here is where you can meet everyone and make 1-to-1 video calls.",
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.black,
                        ),
                      ),
                    ),
                    const SizedBox(height: 5),
                    Padding(
                      padding:
                          const EdgeInsets.symmetric(horizontal: 10.0, vertical: 10.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: width * 0.4,
                            child: ElevatedButton(
                              onPressed: () {
                                // print(_userStatus);
                                print(user);
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.grey[300],
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(20.0),
                                ),
                                padding:
                                    EdgeInsets.symmetric(horizontal: 20, vertical: 0),
                              ),
                              child: Text(
                                '${_onlineCount - 1} Online',
                                style: TextStyle(color: Colors.black),
                              ),
                            ),
                          ),
                          SizedBox(width: 10),
                          SizedBox(
                            width: width * 0.4,
                            child: ElevatedButton(
                              onPressed: () {
                                final targetIndex = _findTabIndex('cmtconnect');
                                if (targetIndex != -1) widget.onTabChange(targetIndex);
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: const Color(0xffc01058),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(20.0),
                                ),
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 20, vertical: 0),
                              ),
                              child: const Text(
                                "Join Now",
                                style: TextStyle(color: Colors.white),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            if (event['cmtconnect']) SizedBox(height: 20),

            if (event['networking'])
              Card(
                margin: const EdgeInsets.all(0),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10.0),
                ),
                elevation: 2,
                color: Colors.white,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Container(
                      padding:
                          const EdgeInsets.symmetric(vertical: 8.0, horizontal: 10.0),
                      decoration: const BoxDecoration(
                        color: Colors.black54,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(10.0),
                          topRight: Radius.circular(10.0),
                        ),
                      ),
                      child: Row(
                        children: [
                          const Expanded(
                            flex: 5,
                            child: Text(
                              "Networking",
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          Expanded(flex: 5, child: Container()),
                        ],
                      ),
                    ),
                    const SizedBox(height: 10),
                    const Padding(
                      padding: EdgeInsets.symmetric(horizontal: 10.0),
                      child: Text(
                        "Our lounge tables are especially set up for you to start and/or join group conversations.",
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.black,
                        ),
                      ),
                    ),
                    const SizedBox(height: 5),
                    Padding(
                      padding:
                          const EdgeInsets.symmetric(horizontal: 10.0, vertical: 10.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: width * 0.4,
                            child: ElevatedButton(
                              onPressed: () {
                                print(_roomOnlineCount);
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.grey[300],
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(20.0),
                                ),
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 20, vertical: 0),
                              ),
                              child: Text(
                                '$_roomOnlineCount Online',
                                style: TextStyle(color: Colors.black),
                              ),
                            ),
                          ),
                          const SizedBox(width: 10),
                          SizedBox(
                            width: width * 0.4,
                            child: ElevatedButton(
                              onPressed: () {
                                final targetIndex = _findTabIndex('networking');
                                if (targetIndex != -1) widget.onTabChange(targetIndex);
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: const Color(0xffc01058),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(20.0),
                                ),
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 20, vertical: 0),
                              ),
                              child: const Text(
                                "Join Now",
                                style: TextStyle(color: Colors.white),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            // if (event['networking']) SizedBox(height: 20),

            // if (event['watchLive'])
            //   Card(
            //     margin: const EdgeInsets.all(0),
            //     shape: RoundedRectangleBorder(
            //       borderRadius: BorderRadius.circular(10.0),
            //     ),
            //     elevation: 2,
            //     color: Colors.white,
            //     child: Column(
            //       crossAxisAlignment: CrossAxisAlignment.stretch,
            //       children: [
            //         Container(
            //           padding: const EdgeInsets.symmetric(
            //               vertical: 8.0, horizontal: 10.0),
            //           decoration: const BoxDecoration(
            //             color: Colors.black54,
            //             borderRadius: BorderRadius.only(
            //               topLeft: Radius.circular(10.0),
            //               topRight: Radius.circular(10.0),
            //             ),
            //           ),
            //           child: Row(
            //             children: [
            //               const Expanded(
            //                 flex: 5,
            //                 child: Text(
            //                   "Watch Live",
            //                   style: TextStyle(
            //                     color: Colors.white,
            //                     fontSize: 16,
            //                     fontWeight: FontWeight.bold,
            //                   ),
            //                 ),
            //               ),
            //               Expanded(flex: 5, child: Container()),
            //             ],
            //           ),
            //         ),
            //         const SizedBox(height: 10),
            //         const Padding(
            //           padding: EdgeInsets.symmetric(horizontal: 10.0),
            //           child: Text(
            //             "Where panel discussions and speaker sessions take place via livestream.",
            //             style: TextStyle(
            //               fontSize: 16,
            //               color: Colors.black,
            //             ),
            //           ),
            //         ),
            //         const SizedBox(height: 5),
            //         Padding(
            //           padding: const EdgeInsets.symmetric(
            //               horizontal: 10.0, vertical: 10.0),
            //           child: Row(
            //             mainAxisAlignment: MainAxisAlignment.center,
            //             children: [
            //               SizedBox(
            //                 width: width * 0.4,
            //                 child: ElevatedButton(
            //                   onPressed: () {
            //                     print('object');
            //                   },
            //                   style: ElevatedButton.styleFrom(
            //                     backgroundColor: Colors.grey[300],
            //                     shape: RoundedRectangleBorder(
            //                       borderRadius: BorderRadius.circular(20.0),
            //                     ),
            //                     padding: const EdgeInsets.symmetric(
            //                         horizontal: 20, vertical: 0),
            //                   ),
            //                   child: const Text(
            //                     "0 Online",
            //                     style: TextStyle(color: Colors.black),
            //                   ),
            //                 ),
            //               ),
            //               const SizedBox(width: 10),
            //               SizedBox(
            //                 width: width * 0.4,
            //                 child: ElevatedButton(
            //                   onPressed: () {
            //                     // Action for "Join Now" button
            //                     final targetIndex = _findTabIndex('watchlive');
            //                     if (targetIndex != -1)
            //                       widget.onTabChange(targetIndex);
            //                   },
            //                   style: ElevatedButton.styleFrom(
            //                     backgroundColor: Colors.grey[300],
            //                     shape: RoundedRectangleBorder(
            //                       borderRadius: BorderRadius.circular(20.0),
            //                     ),
            //                     padding: const EdgeInsets.symmetric(
            //                         horizontal: 20, vertical: 0),
            //                   ),
            //                   child: const Text(
            //                     "Join Now",
            //                     style: TextStyle(color: Colors.black),
            //                   ),
            //                 ),
            //               ),
            //             ],
            //           ),
            //         ),
            //       ],
            //     ),
            //   ),
          ],
        ),
      ),
    );
  }

  int _findTabIndex(String pageIdentifier) {
    final availableTabs = [
      'home',
      'agenda',
      if (event['cmtconnect'] ?? false) 'cmtconnect',
      if (event['networking'] ?? false) 'networking',
      if (event['watchLive'] ?? false) 'watchlive',
    ];

    return availableTabs.indexOf(pageIdentifier);
  }
}
