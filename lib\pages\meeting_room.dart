import 'dart:async';

import 'package:agora_rtc_engine/agora_rtc_engine.dart';
import 'package:cmtmeet/utils/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get_storage/get_storage.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

class MeetingRoom extends StatefulWidget {
  final String room;
  final String token;
  MeetingRoom({required this.room, required this.token});

  @override
  State<MeetingRoom> createState() => _MeetingRoomState();
}

class _MeetingRoomState extends State<MeetingRoom> with WidgetsBindingObserver {
  final GetStorage storage = GetStorage();
  late RtcEngine _engine;
  late int userId;
  int? remoteUserId;
  bool isJoined = false;
  late bool remoteVideoState = false;
  late bool remoteAudioState = false;
  bool isMicOn = true;
  bool isVideoOn = true;

  // Volume streaming implementation
  final _volumeStreamController = StreamController<Map<int, int>>.broadcast();
  Stream<Map<int, int>> get volumeStream => _volumeStreamController.stream;

  @override
  void initState() {
    super.initState();
    _enableWakeLock();
    userId = storage.read("user")["id"];
    _initializeAgora();
    WidgetsBinding.instance.addObserver(this);
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
  }

  @override
  void dispose() {
    _disableWakeLock();
    try {
      WidgetsBinding.instance.removeObserver(this);
      _terminateSession();
    } catch (e) {
      debugPrint('Error cleaning up Agora engine: $e');
    }
    super.dispose();
  }

  Future<void> _initializeAgora() async {
    try {
      await [Permission.microphone, Permission.camera].request();
      _engine = await createAgoraRtcEngine();
      await _engine.initialize(RtcEngineContext(appId: Constants.appId));

      await _engine.enableVideo();
      await _engine.startPreview();
      await _engine.enableAudio();

      _engine.enableAudioVolumeIndication(
          interval: 200, smooth: 3, reportVad: true);

      _engine.registerEventHandler(RtcEngineEventHandler(
        onJoinChannelSuccess: (connection, elapsed) {
          setState(() => isJoined = true);
        },
        onUserJoined: (connection, remoteUid, elapsed) {
          setState(() {
            remoteUserId = remoteUid;
            remoteVideoState = true;
          });
        },
        onAudioVolumeIndication:
            (connection, speakers, speakerNumber, totalVolume) {
          final volumeMap = <int, int>{};
          for (var speaker in speakers) {
            volumeMap[speaker.uid == 0 ? userId : speaker.uid!] =
                speaker.volume ?? 0;
          }
          _volumeStreamController.add(volumeMap);
        },
        onUserOffline: (connection, remoteUid, reason) {
          setState(() {
            remoteUserId = null;
            remoteVideoState = false;
          });
        },
        onUserMuteVideo: (connection, remoteUid, muted) {
          setState(() {
            remoteVideoState = !muted;
          });
        },
        onUserMuteAudio: (connection, remoteUid, muted) {
          setState(() {
            remoteAudioState = !muted;
          });
        },
        onError: (err, msg) {
          debugPrint("Agora Error: $err, $msg");
        },
      ));
      await _joinChannel();
    } catch (e) {
      debugPrint("Error initializing Agora: $e");
    }
  }

  Future<void> _enableWakeLock() async {
    try {
      if (!(await WakelockPlus.enabled)) {
        await WakelockPlus.enable();
        debugPrint('Wakelock enabled');
      }
    } catch (e) {
      debugPrint('Error enabling wakelock: $e');
    }
  }

  Future<void> _disableWakeLock() async {
    try {
      if (await WakelockPlus.enabled) {
        await WakelockPlus.disable();
        debugPrint('Wakelock disabled');
      }
    } catch (e) {
      debugPrint('Error disabling wakelock: $e');
    }
  }

  Future<void> _terminateSession() async {
    try {
      debugPrint('Terminating session...');
      if (isJoined) {
        await _engine.leaveChannel();
        isJoined = false;
      }
      await _engine.release();
      // webSocketService.leaveRoom();
      await _volumeStreamController.close();
      // await _socketSubscription?.cancel();

      if (mounted) {
        setState(() {
          remoteUserId = null;
          remoteVideoState = false;
          remoteAudioState = false;
        });
      }
    } catch (e) {
      debugPrint('Error terminating session: $e');
    } finally {
      // Reset orientation preferences
      SystemChrome.setPreferredOrientations(DeviceOrientation.values);
    }
  }

  Future<void> _joinChannel() async {
    try {
      await _engine.joinChannel(
        token: widget.token,
        channelId: widget.room,
        uid: userId,
        options: ChannelMediaOptions(
          channelProfile: ChannelProfileType.channelProfileLiveBroadcasting,
          clientRoleType: ClientRoleType.clientRoleBroadcaster,
          publishCameraTrack: isVideoOn,
          publishMicrophoneTrack: isMicOn,
          autoSubscribeAudio: true,
          autoSubscribeVideo: true,
        ),
      );
    } catch (e) {
      debugPrint("Error joining channel: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      appBar: AppBar(
        foregroundColor: Colors.white,
        backgroundColor: Color(0xffc01058),
        title: Text('1 - 1 Meeting Room', style: TextStyle(fontSize: 18)),
        actions: [
          IconButton(
            icon: Icon(Icons.notifications),
            onPressed: () {
              // Get.toNamed('/notifications');
            },
          ),
          IconButton(
            icon: Icon(Icons.account_circle),
            onPressed: () {
              // Get.toNamed('/profile');
            },
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: Container(
              padding: EdgeInsets.all(12),
              color: Colors.black,
              child: Column(
                children: [
                  Expanded(
                    child: _buildUserView(userId),
                  ),
                  SizedBox(height: 12),
                  Expanded(
                    child: remoteUserId == null
                        ? Container()
                        : _buildUserView(remoteUserId!),
                  ),
                ],
              ),
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(vertical: 10, horizontal: 20),
            color: Color.fromRGBO(35, 35, 35, 1),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Video Toggle
                _buildCircleButton(
                  icon: isVideoOn
                      ? Icons.videocam_sharp
                      : Icons.videocam_off_sharp,
                  isActive: isVideoOn,
                  onTap: _toggleVideo,
                ),
                SizedBox(width: 20),

                // Mic Toggle
                _buildCircleButton(
                  icon: isMicOn ? Icons.mic_sharp : Icons.mic_off_sharp,
                  isActive: isMicOn,
                  onTap: _toggleMic,
                ),
                SizedBox(width: 20),

                // Raise Hand
                // _buildCircleButton(
                //   icon: Icons.back_hand_sharp,
                //   isActive: false,
                //   onTap: () {
                //     // Implement raise hand functionality
                //   },
                // ),

                // End Call
                GestureDetector(
                  onTap: () {
                    _engine.leaveChannel();
                    // webSocketService.leaveRoom();
                    Navigator.pop(context);
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding: EdgeInsets.symmetric(vertical: 7, horizontal: 20),
                    child: Row(
                      children: [
                        Icon(Icons.call_end_sharp, color: Colors.white),
                        SizedBox(width: 10),
                        Text(
                          'End',
                          style: TextStyle(color: Colors.white),
                        )
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    ));
  }

  Widget _buildUserView(int uid) {
    Map<String, dynamic> getAttendee(List<dynamic> attendees, int uid) {
      try {
        return (attendees.cast<Map<String, dynamic>>()).firstWhere(
          (a) => int.tryParse(a["id"]?.toString() ?? '0') == uid,
          orElse: () => <String, dynamic>{},
        );
      } catch (e) {
        return <String, dynamic>{};
      }
    }

    List<dynamic> attendees = storage.read<List<dynamic>>('attendees') ?? [];
    final isLocalUser = uid == userId;
    final attendee = getAttendee(attendees, uid);
    final isVideoEnabled = isLocalUser ? isVideoOn : remoteVideoState;
    final isAudioEnabled = isLocalUser ? isMicOn : remoteAudioState;

    return Container(
      decoration: BoxDecoration(
        color: Color.fromRGBO(64, 64, 64, 1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Color.fromRGBO(96, 96, 96, 1),
          width: 3,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Stack(
          children: [
            isLocalUser
                ? (isJoined
                    ? (isVideoOn
                        ? AgoraVideoView(
                            controller: VideoViewController(
                              rtcEngine: _engine,
                              canvas: VideoCanvas(uid: 0),
                            ),
                          )
                        : _buildBlankView())
                    : Center(child: CircularProgressIndicator()))
                : (isVideoEnabled
                    ? AgoraVideoView(
                        controller: VideoViewController.remote(
                          rtcEngine: _engine,
                          canvas: VideoCanvas(uid: uid),
                          connection: RtcConnection(channelId: widget.room),
                        ),
                      )
                    : _buildBlankRemoteView(uid: uid)),
            // Name Tag
            Positioned(
              top: 8,
              left: 8,
              child: isVideoEnabled
                  ? Container(
                      padding: EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: Colors.black54,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        isLocalUser ? 'You' : '${attendee["fullname"]}',
                        style: TextStyle(color: Colors.white),
                      ),
                    )
                  : SizedBox(),
            ),
            // Mic and Video Status
            Positioned(
              bottom: 8,
              left: 8,
              child: Container(
                padding: EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: Colors.black54,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Row(
                  children: [
                    Icon(
                      isLocalUser
                          ? (isVideoOn ? Icons.videocam : Icons.videocam_off)
                          : (isVideoEnabled
                              ? Icons.videocam
                              : Icons.videocam_off),
                      size: 18,
                      color: Colors.white,
                    ),
                    SizedBox(width: 5),
                    Icon(
                      isLocalUser
                          ? (isMicOn ? Icons.mic : Icons.mic_off)
                          : (isAudioEnabled ? Icons.mic : Icons.mic_off),
                      size: 17,
                      color: Colors.white,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCircleButton({
    required IconData icon,
    required VoidCallback onTap,
    bool isActive = true,
    Color color = const Color.fromRGBO(78, 78, 78, 1),
  }) {
    return GestureDetector(
      onTap: onTap,
      child: CircleAvatar(
        radius: 20,
        backgroundColor: isActive ? Colors.white : color,
        child:
            Icon(icon, color: isActive ? Colors.black : Colors.white, size: 27),
      ),
    );
  }

  Widget _buildBlankView() {
    return Container(
      color: Colors.black,
      child: Center(
        child: Icon(
          Icons.videocam_off_sharp,
          size: 70,
          color: Colors.red,
        ),
      ),
    );
  }

  Widget _buildBlankRemoteView({int? uid}) {
    final attendees = storage.read<List<dynamic>>('attendees') ?? [];
    final attendee = uid != null
        ? attendees.firstWhere((a) => int.tryParse(a["id"].toString()) == uid,
            orElse: () => {})
        : {};

    return Container(
      color: Colors.grey[800],
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircleAvatar(
              radius: 30,
              backgroundColor: Colors.grey[600],
              child: Icon(
                Icons.person,
                size: 40,
                color: Colors.white,
              ),
            ),
            if (attendee.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Text(
                  attendee["fullname"] ?? 'User',
                  style: TextStyle(color: Colors.white),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Future<void> _toggleMic() async {
    setState(() {
      isMicOn = !isMicOn;
    });
    await _engine.muteLocalAudioStream(!isMicOn);
  }

  Future<void> _toggleVideo() async {
    try {
      setState(() => isVideoOn = !isVideoOn);
      await _engine.muteLocalVideoStream(!isVideoOn);
      await _engine.enableLocalVideo(isVideoOn);
    } catch (e) {
      debugPrint("Error toggling video: $e");
      setState(() => isVideoOn = !isVideoOn); // Revert on error
    }
  }
}
