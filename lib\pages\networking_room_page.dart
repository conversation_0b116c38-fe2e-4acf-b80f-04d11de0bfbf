import 'package:cmtmeet/service/websocket_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_profile_picture/flutter_profile_picture.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

class NetworkingRoomPage extends StatefulWidget {
  const NetworkingRoomPage({super.key});

  @override
  State<NetworkingRoomPage> createState() => _NetworkingRoomPageState();
}

class _NetworkingRoomPageState extends State<NetworkingRoomPage> {
  final WebSocketService webSocketService = Get.find<WebSocketService>();

  final storage = GetStorage();
  final Map<String, String> _userStatus = {};
  late void Function(dynamic, dynamic, dynamic) _statusListener;
  late String eventCode;

  String _selectedSection = "All Rooms";
  late Map<String, dynamic> rooms;

  Map<String, dynamic> roomsDetails = {};

  @override
  void initState() {
    super.initState();
    rooms = storage.read("rooms") ?? {"data": [], "count": 0};
    eventCode = GetStorage().read("event")?["eventCode"] ?? '';
    _statusListener = (userId, eventCode, status) {
      final key = "$userId-$eventCode";
      if (mounted) {
        setState(() {
          _userStatus[key] = status;
        });
      }
    };

    webSocketService.addStatusListener(_statusListener);

    setState(() {
      _userStatus.addAll(webSocketService.userStatuses);
    });
    _initializeWebSocketListeners();
  }

  void _initializeWebSocketListeners() {
    webSocketService.addRoomListener(_handleRoomUpdate);

    setState(() {
      roomsDetails.addAll(webSocketService.roomDetails);
    });
  }

  void _handleRoomUpdate(Map<String, dynamic> updatedRooms) {
    if (mounted) {
      setState(() {
        updatedRooms.forEach((roomKey, roomData) {
          roomsDetails[roomKey] = Map<String, dynamic>.from(roomData);
        });
      });
    }
  }

  @override
  void dispose() {
    webSocketService.removeRoomListener(_handleRoomUpdate);
    webSocketService.removeStatusListener(_statusListener);
    super.dispose();
  }

  List<Map<String, dynamic>> getSortedRooms() {
    List<Map<String, dynamic>> roomList = List.from(rooms["data"] ?? []);
    roomList.sort((a, b) {
      int aNum = int.parse(a["room"]!.replaceAll("Room", ""));
      int bNum = int.parse(b["room"]!.replaceAll("Room", ""));
      return aNum.compareTo(bNum);
    });
    return roomList;
  }

  List<Map<String, dynamic>> getFilteredRooms() {
    final sortedRooms = getSortedRooms();
    List<Map<String, dynamic>> allRooms = [];

    // Add the real rooms first
    allRooms.addAll(sortedRooms);

    // Add placeholder rooms to reach the total count
    for (int i = sortedRooms.length; i < rooms["count"]!; i++) {
      allRooms.add({
        "room": "Room${i + 1}",
        "roomName": "Open Room",
        "hostId": 0,
        "logoUrl": "",
        "fullname": "Speaker Name ${i + 1}",
        "attendeetype": 0,
      });
    }

    // Filter based on selected section
    switch (_selectedSection) {
      case "Sponsor's Rooms":
        return allRooms.where((room) => room["attendeetype"] == 4).toList();
      case "Speaker's Rooms":
        return allRooms.where((room) => room["attendeetype"] == 6).toList();
      case "Open Rooms":
        return allRooms
            .where((room) =>
                room["attendeetype"] != 4 && room["attendeetype"] != 6)
            .toList();
      default:
        return allRooms;
    }
  }

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final filteredRooms = getFilteredRooms();

    return Scaffold(
      body: Container(
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.symmetric(vertical: 10.0),
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    "All Rooms",
                    "Sponsor's Rooms",
                    "Speaker's Rooms",
                    "Open Rooms"
                  ].map((section) {
                    final isSelected = _selectedSection == section;
                    return GestureDetector(
                      onTap: () {
                        setState(() {
                          _selectedSection = section;
                        });
                      },
                      child: Container(
                        padding: EdgeInsets.symmetric(
                            vertical: 3.0, horizontal: 18.0),
                        margin: EdgeInsets.symmetric(horizontal: 8.0),
                        decoration: BoxDecoration(
                          color: isSelected
                              ? Color.fromARGB(255, 175, 16, 82)
                              : Colors.grey[300],
                          borderRadius: BorderRadius.circular(20.0),
                        ),
                        child: Text(
                          section,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: isSelected ? Colors.white : Colors.black,
                            fontSize: 16.0,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),
            SizedBox(
              height: 1.0,
              width: width,
              child: Container(color: Colors.grey[400]),
            ),
            Expanded(
              child: Container(
                padding: EdgeInsets.only(right: 12, left: 12, top: 10),
                color: Colors.grey[200],
                child: filteredRooms.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.meeting_room_outlined,
                              size: 50,
                              color: Colors.grey,
                            ),
                            SizedBox(height: 16),
                            Text(
                              _getNoRoomsMessage(),
                              style: TextStyle(
                                fontSize: 18,
                                color: Colors.grey,
                              ),
                            ),
                          ],
                        ),
                      )
                    : ListView.builder(
                        itemCount: filteredRooms.length,
                        itemBuilder: (context, index) {
                          final roomData = filteredRooms[index];
                          final roomName = roomData["room"] as String;
                          final onlineCount =
                              roomsDetails['${eventCode}_${roomName}']
                                          ['members']
                                      .length ??
                                  0;
                          final roomDetails =
                              roomsDetails['${eventCode}_${roomName}'] ?? {};
                          final members =
                              (roomDetails["members"] as List<dynamic>?)
                                      ?.cast<int>() ??
                                  [];

                          return _buildRoomCard(
                            roomName,
                            roomData["attendeetype"] as int,
                            roomData["roomName"] as String,
                            roomData["hostId"] as int,
                            roomData["logoUrl"] as String,
                            roomData["fullname"] as String,
                            roomData["jobtitle"]?.toString() ?? '',
                            roomData["companyname"]?.toString() ?? '',
                            onlineCount,
                            members,
                            width,
                          );
                        },
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getNoRoomsMessage() {
    switch (_selectedSection) {
      case "Sponsor's Rooms":
        return "No sponsor rooms available";
      case "Speaker's Rooms":
        return "No speaker rooms available";
      case "Open Rooms":
        return "No open rooms available";
      default:
        return "No rooms available";
    }
  }

  Widget _buildRoomCard(
    String room,
    int attendeeType,
    String roomName,
    int hostId,
    String imageUrl,
    String speakerName,
    String speakerJobTitle,
    String speakerCompany,
    int onlineCount,
    List members,
    double width,
  ) {
    final isFull = onlineCount >= 6; // Maximum capacity is 6
    List<dynamic> attendees = storage.read<List<dynamic>>('attendees') ?? [];
    final hostStatus = _userStatus['$hostId-$eventCode'] ?? 'offline';
    return Card(
      margin: EdgeInsets.only(bottom: 20),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10.0),
      ),
      elevation: 2,
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Container(
            padding:
                const EdgeInsets.symmetric(vertical: 5.0, horizontal: 10.0),
            decoration: BoxDecoration(
              color: members.isEmpty ? Colors.black38 : Colors.green,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(10.0),
                topRight: Radius.circular(10.0),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  roomName,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  formatRoomName(room),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          Container(
            child: Column(
              children: [
                attendeeType == 6
                    ? Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 15.0, vertical: 15.0),
                        child: Row(
                          children: [
                            Stack(
                              children: [
                                Container(
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                        color: Color.fromARGB(255, 97, 97, 97),
                                        width: 1),
                                  ),
                                  child: ClipOval(
                                    child: imageUrl.isEmpty
                                        ? Image.asset(
                                            'assets/background_image.png',
                                            width: 50,
                                            height: 50,
                                            fit: BoxFit.cover)
                                        : Image.network(imageUrl,
                                            width: 50,
                                            height: 50,
                                            fit: BoxFit.cover),
                                  ),
                                ),
                                Positioned(
                                  bottom: 1,
                                  right: 1,
                                  child: CircleAvatar(
                                    radius: 8,
                                    child: CircleAvatar(
                                        radius: 6,
                                        backgroundColor: hostStatus == 'online'
                                            ? Color.fromARGB(255, 0, 153, 18)
                                            : Colors.grey),
                                    backgroundColor: Colors.white,
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(width: 15),
                            Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  speakerName,
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                speakerJobTitle == ''
                                    ? SizedBox()
                                    : Text(
                                        speakerJobTitle,
                                        style: TextStyle(
                                            fontSize: 12,
                                            color: Colors.black54),
                                      ),
                                speakerCompany == ''
                                    ? SizedBox()
                                    : Text(
                                        speakerCompany,
                                        style: TextStyle(
                                            fontSize: 12,
                                            color: Colors.black54),
                                      ),
                              ],
                            ),
                          ],
                        ),
                      )
                    : Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: 15.0, vertical: 15.0),
                        child: imageUrl.isEmpty
                            ? Image.asset('assets/CMT_logo.png',
                                width: 70, height: 70, fit: BoxFit.cover)
                            : Image.network(imageUrl),
                      ),
                members.isEmpty
                    ? SizedBox()
                    : Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 15.0, vertical: 7.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            for (final memberId in members)
                              for (final attendee in attendees)
                                if (attendee['id'] == memberId)
                                  Padding(
                                    padding: const EdgeInsets.only(right: 8.0),
                                    child: Stack(children: [
                                      ProfilePicture(
                                        name: attendee['fullname'] ?? 'Unknown',
                                        role: attendee['companyname'] ?? '',
                                        radius: 21,
                                        fontsize: 15,
                                        tooltip: true,
                                      ),
                                      Positioned(
                                        bottom: 3,
                                        right: 2,
                                        child: CircleAvatar(
                                          radius: 5,
                                          child: CircleAvatar(
                                              radius: 4,
                                              backgroundColor: Colors.green),
                                          backgroundColor: Colors.white,
                                        ),
                                      ),
                                    ]),
                                  ),
                          ],
                        )),
                SizedBox(
                  height: 1.0,
                  width: width * 0.88,
                  child: Container(color: Colors.grey[400]),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 10.0, vertical: 5.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      GestureDetector(
                        onTap: () {
                          print(members);
                        },
                        child: Container(
                          width: width / 2.4,
                          padding: EdgeInsets.only(left: 5),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Container(
                                decoration: BoxDecoration(
                                  color: onlineCount == 0
                                      ? Colors.grey
                                      : Colors.red,
                                  shape: BoxShape.circle,
                                ),
                                padding: EdgeInsets.all(6),
                                child: Text(
                                  onlineCount.toString(),
                                  style: TextStyle(color: Colors.white),
                                ),
                              ),
                              SizedBox(width: 6),
                              Text(
                                'Online',
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color: Colors.black,
                                  fontSize: 16.0,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      ElevatedButton(
                        onPressed: isFull
                            ? null
                            : () {
                                webSocketService
                                    .joinRoom(room.replaceAll(' ', ''));
                              },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: isFull
                              ? Colors.grey
                              : Color.fromARGB(255, 175, 16, 82),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20.0),
                            side: BorderSide(
                                color: Color.fromARGB(255, 175, 16, 82),
                                width: 1),
                          ),
                          padding: EdgeInsets.symmetric(
                              vertical: 4.0, horizontal: 16.0),
                          minimumSize: Size(width / 2.4, 0),
                        ),
                        child: Text(
                          isFull ? 'Full' : 'Join',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16.0,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                )
              ],
            ),
          )
        ],
      ),
    );
  }

  String formatRoomName(String roomName) {
    return roomName.replaceAllMapped(
        RegExp(r'(\d+)'), (match) => '-${match.group(1)}');
  }
}
