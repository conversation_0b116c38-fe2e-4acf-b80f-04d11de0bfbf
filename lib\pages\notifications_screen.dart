import 'package:cmtmeet/pages/signing_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:intl/intl.dart';

class NotificationItem {
  final String id;
  final String title;
  final String body;
  final DateTime timestamp;
  final IconData icon;

  NotificationItem({
    required this.id,
    required this.title,
    required this.body,
    required this.timestamp,
    required this.icon,
  });
}

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  final List<NotificationItem> _notifications = [
    // NotificationItem(
    //   id: '1',
    //   title: 'New Message',
    //   body: 'You have received a new message from <PERSON>',
    //   timestamp: DateTime.now().subtract(const Duration(minutes: 5)),
    //   icon: Icons.message,
    // ),
    // NotificationItem(
    //   id: '2',
    //   title: 'Meeting Reminder',
    //   body: 'Team meeting starts in 15 minutes',
    //   timestamp: DateTime.now().subtract(const Duration(hours: 1)),
    //   icon: Icons.calendar_today,
    // ),
    // NotificationItem(
    //   id: '3',
    //   title: 'System Update',
    //   body: 'New version available for download',
    //   timestamp: DateTime.now().subtract(const Duration(days: 1)),
    //   icon: Icons.system_update,
    // ),
  ];

  void _deleteNotification(String id) {
    setState(() {
      _notifications.removeWhere((notification) => notification.id == id);
    });
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Notification deleted')),
    );
  }

  void _onNotificationTap(NotificationItem notification) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(notification.title),
        content: Text(notification.body),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          foregroundColor: Colors.white,
          backgroundColor: const Color(0xffc01058),
          title: const Text('Notifications'),
          actions: [
            IconButton(
              icon: const Icon(Icons.delete_sweep),
              onPressed: () {
                setState(() {
                  _notifications.clear();
                });
              },
            ),
          ],
        ),
        body: _notifications.isEmpty
            ? TextButton(
                child: Center(child: Text('No notifications')),
                onPressed: () => print(globalFcmToken),
              )
            : ListView.separated(
                itemCount: _notifications.length,
                separatorBuilder: (context, index) =>
                    const Divider(height: 1, thickness: 0.5),
                itemBuilder: (context, index) {
                  final notification = _notifications[index];
                  return Slidable(
                    key: Key(notification.id),
                    endActionPane: ActionPane(
                      motion: const DrawerMotion(),
                      extentRatio: 0.25,
                      children: [
                        SlidableAction(
                          onPressed: (_) =>
                              _deleteNotification(notification.id),
                          backgroundColor: Colors.red,
                          foregroundColor: Colors.white,
                          icon: Icons.delete,
                          label: 'Delete',
                        ),
                      ],
                    ),
                    child: InkWell(
                      onTap: () => _onNotificationTap(notification),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 12),
                        child: Row(
                          children: [
                            Icon(notification.icon, size: 24),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    notification.title,
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodyMedium
                                        ?.copyWith(
                                          fontWeight: FontWeight.w500,
                                        ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(notification.body,
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodySmall),
                                  const SizedBox(height: 4),
                                  Text(
                                    DateFormat('h:mm a')
                                        .format(notification.timestamp),
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodySmall
                                        ?.copyWith(color: Colors.grey.shade600),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
      ),
    );
  }
}
