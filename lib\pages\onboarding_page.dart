import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:get/get.dart';

class OnboardingPage extends StatefulWidget {
  const OnboardingPage({super.key});

  @override
  State<OnboardingPage> createState() => _OnboardingPageState();
}

class _OnboardingPageState extends State<OnboardingPage>
    with SingleTickerProviderStateMixin {
  final pageController = PageController();
  final currentIndex = 0.obs;
  late final AnimationController animationController;

  @override
  void initState() {
    super.initState();
    animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );
  }

  @override
  void dispose() {
    animationController.dispose();
    pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: const Color.fromRGBO(192, 16, 88, 1.0),
        body: Stack(
          children: [
            <PERSON>View(
              controller: pageController,
              onPageChanged: (index) {
                currentIndex.value = index;
                animationController.forward(from: 0);
              },
              children: [
                buildPage("assets/onboarding_1.png", "Welcome to CMT Meet !",
                    "We're excited to help you get started with your video conferencing experience."),
                buildPage("assets/onboarding_2.png", "",
                    "CMT Meet is designed to make your virtual meetings seamless and productive."),
              ],
            ),
            Positioned(
              bottom: 10,
              left: 0,
              right: 0,
              child: Obx(
                () => Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: List.generate(2, (index) {
                        return AnimatedContainer(
                          duration: Duration(milliseconds: 300),
                          margin: EdgeInsets.symmetric(horizontal: 5),
                          height: 8,
                          width: currentIndex.value == index ? 20 : 10,
                          decoration: BoxDecoration(
                            color: currentIndex.value == index
                                ? Colors.white
                                : Colors.grey,
                            borderRadius: BorderRadius.circular(5),
                          ),
                        );
                      }),
                    ),
                    SizedBox(height: 20),
                    TextButton(
                      onPressed: () {
                        if (currentIndex.value < 1) {
                          pageController.nextPage(
                            duration: Duration(milliseconds: 300),
                            curve: Curves.easeInOut,
                          );
                        } else {
                          Get.toNamed('/login');
                        }
                      },
                      child: Text(
                        currentIndex.value < 1 ? 'Next' : 'Finish',
                        style: TextStyle(color: Colors.white, fontSize: 20),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget buildPage(String image, String? titleText, String subTitleText) {
    return Padding(
      padding: EdgeInsets.all(20.0),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(image, height: 200, width: double.infinity)
                .animate()
                .fadeIn(duration: 600.ms)
                .move(
                    begin: Offset(0, 50), end: Offset(0, 0), duration: 700.ms),
            SizedBox(height: 30),
            Text(
              titleText!,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 22,
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ).animate().fadeIn(duration: 600.ms).move(
                begin: Offset(0, 50), end: Offset(0, 0), duration: 700.ms),
            SizedBox(height: 20),
            Text(
              subTitleText,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 18,
                color: Colors.white,
              ),
            ).animate().fadeIn(duration: 600.ms).move(
                begin: Offset(0, 50), end: Offset(0, 0), duration: 700.ms),
          ],
        ),
      ),
    );
  }
}
