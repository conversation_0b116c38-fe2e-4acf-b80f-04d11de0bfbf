import 'package:cmtmeet/utils/constants.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:url_launcher/url_launcher.dart';

class PrivacyPolicyPage extends StatefulWidget {
  const PrivacyPolicyPage({super.key});

  @override
  State<PrivacyPolicyPage> createState() => _PrivacyPolicyPageState();
}

class _PrivacyPolicyPageState extends State<PrivacyPolicyPage> {
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Privacy Policy'),
          foregroundColor: Colors.white,
          backgroundColor: const Color(0xffc01058),
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Privacy Policy for CMT Meet App',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                'Effective Date: 15.04.2025',
                style: TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 16),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildPolicyItem(
                    index: 1,
                    title: 'Introduction',
                    children: [
                      'We respect your privacy and are committed to protecting your personal data. This Privacy Policy explains how we collect, use, and share your data when you use the CMT Meet mobile application.',
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildPolicyItem(
                    index: 2,
                    title: 'Data Collection',
                    children: [
                      'We collect data during the event registration process on our official website: www.cmtevents.com. This includes:',
                      '● Delegate Name, Job Title, Company, Country, Phone/Mobile Number, Email Address, etc.',
                      'Only registered delegates can securely log in to the CMT Meet app using credentials provided by our admin team. These credentials are valid only during the event period.',
                      'Optional Profile Information:',
                      '\t● Full Name\n ● Job Title\n ● Company Name\n ● Country\n ● Phone/Mobile Number\n ● Email Address',
                      'This profile update is not mandatory. Even without updating, the app displays already collected delegate information from our backend server.',
                      'Users can optionally sync their name, email ID, and profile picture from LinkedIn using the “Sign in with LinkedIn” option, with their consent. This feature is entirely optional and not required to use the app.',
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildPolicyItem(
                    index: 3,
                    title: 'Permissions and Access:',
                    children: [
                      'We may request access to:',
                      '● Camera and Microphone: Strictly used for live chat, video/audio calls, Q&A sessions, and networking during events.',
                      '● We do not record or monitor any audio, video, or chat content.',
                      '● We do not collect GPS or location data from your device.',
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildPolicyItem(
                    index: 4,
                    title: 'Push Notifications',
                    children: [
                      '● We use Firebase Cloud Messaging (FCM) to send offline push notifications (e.g., new chat messages) while the app is paused or running in the background. You may disable these notifications anytime via your device settings.',
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildPolicyItem(
                    index: 5,
                    title: 'Data Sharing',
                    children: [
                      'We do not sell or rent your data. We may share limited data (e.g., user ID) with trusted service providers like:',
                      '● Agora (for chat, audio/video calls)',
                      '● Firebase (for hosting, notifications, analytics)',
                      'These providers follow strict data protection and security standards.',
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildPolicyItem(
                    index: 6,
                    title: 'User Control',
                    children: [
                      'You are in control of your data. You can:',
                      '● Deny camera/microphone permissions from device settings',
                      '● Disable push notifications via app/device settings',
                      '● Edit or remove your profile details (job title, phone number, email, etc.)',
                      '● Request to view or delete your data by contacting us',
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildPolicyItem(
                    index: 7,
                    title: 'Data Security',
                    children: [
                      'We use standard industry practices to protect your data, including:',
                      '● Data encryption',
                      '● Secure transmission protocols',
                      '● Limited access to personal data',
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildPolicyItem(
                    index: 8,
                    title: 'Changes to This Privacy Policy',
                    children: [
                      'We may update this Privacy Policy from time to time to reflect changes in our practices, operational needs, or legal requirements. We will notify users of any changes by posting the updated Privacy Policy within the app.',
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildPolicyItem(
                    index: 9,
                    title: 'Contact Us',
                    children: [
                      'If you have any questions, feedback, or concerns about this Privacy Policy, please contact us at: <EMAIL>',
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPolicyItem(
      {required int index, required String title, required List<String> children}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          text: TextSpan(
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
            children: [
              TextSpan(text: '$index. '),
              TextSpan(text: title),
            ],
          ),
        ),
        const SizedBox(height: 8),
        ...children.map(
          (text) {
            if (text.contains('<EMAIL>')) {
              final parts = text.split('<EMAIL>');
              return Padding(
                padding: const EdgeInsets.only(left: 16.0, top: 4.0, bottom: 4.0),
                child: RichText(
                  text: TextSpan(
                    style: const TextStyle(fontSize: 16, color: Colors.black),
                    children: [
                      TextSpan(text: parts[0]),
                      TextSpan(
                        text: '<EMAIL>',
                        style: TextStyle(
                          color: Constants.colorApp,
                          fontFamily: 'Roboto',
                        ),
                        recognizer: TapGestureRecognizer()
                          ..onTap = () async {
                            final Uri emailLaunchUri = Uri(
                              scheme: 'mailto',
                              path: '<EMAIL>',
                            );
                            try {
                              await launchUrl(
                                emailLaunchUri,
                                mode: LaunchMode.externalApplication,
                              );
                            } catch (e) {
                              Fluttertoast.showToast(
                                msg: 'Could not launch email client. Error: $e',
                                toastLength: Toast.LENGTH_SHORT,
                                gravity: ToastGravity.BOTTOM,
                                timeInSecForIosWeb: 1,
                                backgroundColor: Colors.red,
                                textColor: Colors.white,
                                fontSize: 16.0,
                              );
                            }
                          },
                      ),
                      if (parts.length > 1) TextSpan(text: parts[1]),
                    ],
                  ),
                ),
              );
            }
            return Padding(
              padding: const EdgeInsets.only(left: 16.0, top: 4.0, bottom: 4.0),
              child: text.startsWith('●')
                  ? Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('● ', style: TextStyle(fontSize: 16)),
                        Expanded(
                          child: Text(
                            text.substring(2),
                            style: const TextStyle(fontSize: 16),
                          ),
                        ),
                      ],
                    )
                  : Text(
                      text,
                      style: const TextStyle(fontSize: 16),
                    ),
            );
          },
        ),
      ],
    );
  }
}
