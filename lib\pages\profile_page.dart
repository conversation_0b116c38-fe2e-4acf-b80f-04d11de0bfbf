import 'dart:convert';
import 'package:cmtmeet/pages/consent_page.dart';
import 'package:cmtmeet/pages/privacy_policy_page.dart';
import 'package:cmtmeet/utils/constants.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:cached_network_image/cached_network_image.dart';
import 'package:linkedin_login/linkedin_login.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  final storage = GetStorage();
  late Map<String, dynamic> user;
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _profileImgController = TextEditingController();
  final TextEditingController _fullNameController = TextEditingController();
  final TextEditingController _jobTitleController = TextEditingController();
  final TextEditingController _companyNameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _mobileController = TextEditingController();
  final TextEditingController _aboutYouController = TextEditingController();

  bool _isLoading = false;
  bool _isFetchingLinkedIn = false;
  String? _errorText;
  bool _consentGiven = true;
  bool _isLinkedInSigned = false;
  bool _logoutUser = false;

  @override
  void initState() {
    super.initState();
    user = storage.read("user") ?? {};
    _loadUserData();
  }

  void _loadUserData() {
    _fullNameController.text = user['fullname'] ?? '';
    _jobTitleController.text = user['jobtitle'] ?? '';
    _companyNameController.text = user['companyname'] ?? '';
    _emailController.text = user['email'] ?? '';
    _phoneController.text = user['phone'] ?? '';
    _mobileController.text = user['mobile'] ?? '';
    _aboutYouController.text = user['aboutyou'] ?? '';
    _profileImgController.text = user['profileimg'] ?? '';
    _isLinkedInSigned = user['islinkedinsigned'] ?? false;
  }

  Widget _buildLinkedInWidget() {
    return SafeArea(
      child: LinkedInUserWidget(
        destroySession: _logoutUser,
        redirectUrl: Constants.redirectUrl,
        clientId: Constants.clientId,
        clientSecret: Constants.clientSecret,
        appBar: AppBar(
          title: const Text('LinkedIn Login'),
        ),
        onError: (final UserFailedAction e) {
          debugPrint('Error: ${e.toString()}');
          setState(() {
            _isFetchingLinkedIn = false;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to fetch LinkedIn profile: ${e.toString()}'),
              backgroundColor: Colors.red,
            ),
          );
        },
        onGetUserProfile: (final UserSucceededAction linkedInUser) async {
          setState(() {
            _fullNameController.text = linkedInUser.user.name ?? _fullNameController.text;
            _emailController.text = linkedInUser.user.email ?? _emailController.text;
            _profileImgController.text = linkedInUser.user.picture ?? '';
            _isLinkedInSigned = true;
            _logoutUser = false;
            _isFetchingLinkedIn = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('LinkedIn profile fetched successfully!'),
              backgroundColor: Colors.green,
            ),
          );

          Navigator.pop(context);
        },
      ),
    );
  }

  Future<void> _fetchLinkedInProfile() async {
    setState(() => _isFetchingLinkedIn = true);

    Navigator.push(
      context,
      MaterialPageRoute<void>(
        builder: (context) => _buildLinkedInWidget(),
        fullscreenDialog: true,
      ),
    );
  }

  Future<void> _submitForm() async {
    FocusManager.instance.primaryFocus?.unfocus();

    if (!_consentGiven) {
      setState(() {
        _errorText = "Please accept the privacy policy and consent to continue";
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorText = null;
    });

    try {
      final url = Uri.parse("https://${Constants.url}/auth/update-profile");
      final response = await http.post(
        url,
        headers: {"Content-Type": "application/json"},
        body: jsonEncode({
          "id": user['id'],
          "fullName": _fullNameController.text.trim(),
          "jobTitle": _jobTitleController.text.trim(),
          "companyName": _companyNameController.text.trim(),
          "email": _emailController.text.trim(),
          "phone": _phoneController.text.trim(),
          "mobile": _mobileController.text.trim(),
          "aboutYou": _aboutYouController.text.trim(),
          "profileImg": _profileImgController.text.trim(),
          "isLinkedInSigned": _isLinkedInSigned,
        }),
      );

      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200) {
        // Update local storage with new user data - FIXED: Use .text to get values
        Map<String, dynamic> updatedUserData = {
          ...user, // Preserve existing user data
          "fullname": _fullNameController.text.trim(),
          "jobtitle": _jobTitleController.text.trim(),
          "email": _emailController.text.trim(),
          "companyname": _companyNameController.text.trim(),
          "phone": _phoneController.text.trim(),
          "mobile": _mobileController.text.trim(),
          "aboutyou": _aboutYouController.text.trim(),
          "profileimg": _profileImgController.text.trim(),
          "islinkedinsigned": _isLinkedInSigned,
        };

        await storage.write("user", updatedUserData);

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Profile updated successfully!'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 3),
            ),
          );
        }

        // Optionally refresh the current screen to show updates
        if (mounted) {
          setState(() {
            Get.back(result: true);
          });
        }
      } else {
        if (mounted) {
          setState(() {
            _errorText = responseData["message"] ?? "Failed to update profile";
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorText = "Connection error. Please try again.";
        });
      }
      debugPrint('Profile update error: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Widget _buildProfilePicture() {
    return _profileImgController.text.isNotEmpty
        ? CachedNetworkImage(
            imageUrl: _profileImgController.text,
            placeholder: (context, url) => Center(
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(),
              ),
            ),
            errorWidget: (context, url, error) => Icon(Icons.error),
            imageBuilder: (context, imageProvider) => CircleAvatar(
              radius: 50,
              backgroundImage: imageProvider,
            ),
          )
        : Icon(
            Icons.account_circle,
            size: 100,
            color: Colors.grey[400],
          );
  }

  Widget _buildLinkedInButton() {
    return Column(
      children: [
        const Text(
          'Update your profile details from LinkedIn:',
          textAlign: TextAlign.center,
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        SizedBox(height: 10),
        _isFetchingLinkedIn
            ? const CircularProgressIndicator()
            : GestureDetector(
                onTap: _fetchLinkedInProfile,
                child: Image.asset(
                  'assets/LinkedIn_Button.png',
                  width: 190,
                  fit: BoxFit.contain,
                ),
              ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          foregroundColor: Colors.white,
          backgroundColor: const Color(0xffc01058),
          title: const Text('Profile Update'),
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(30),
          child: Center(
            child: ConstrainedBox(
              constraints: BoxConstraints(maxWidth: width > 600 ? 600 : double.infinity),
              child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    Row(
                      children: [
                        Expanded(
                          flex: 3,
                          child: _buildProfilePicture(),
                        ),
                        Expanded(
                          flex: 7,
                          child: Container(
                            width: width * 0.5,
                            padding: EdgeInsets.symmetric(horizontal: 20),
                            child: _buildLinkedInButton(),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 15),
                    _buildTextFormField(_fullNameController, 'Full Name'),
                    const SizedBox(height: 16),
                    _buildTextFormField(_jobTitleController, 'Job Title'),
                    const SizedBox(height: 16),
                    _buildTextFormField(_companyNameController, 'Company Name'),
                    const SizedBox(height: 16),
                    _buildTextFormField(_emailController, 'Email',
                        keyboardType: TextInputType.emailAddress),
                    const SizedBox(height: 16),
                    _buildTextFormField(_phoneController, 'Phone',
                        keyboardType: TextInputType.phone),
                    const SizedBox(height: 16),
                    _buildTextFormField(_mobileController, 'Mobile',
                        keyboardType: TextInputType.phone),
                    const SizedBox(height: 16),
                    _buildTextFormField(
                      _aboutYouController,
                      'About You (Max 500 characters)',
                      maxLines: 3,
                      maxLength: 500,
                    ),
                    if (_errorText != null)
                      Column(
                        children: [
                          SizedBox(height: 10),
                          Text(
                            _errorText!,
                            textAlign: TextAlign.center,
                            style: const TextStyle(color: Colors.red),
                          ),
                        ],
                      ),
                    const SizedBox(height: 10),
                    _buildPolicyText(),
                    const SizedBox(height: 10),
                    ElevatedButton(
                      onPressed: (_isLoading || !_consentGiven) ? () {} : _submitForm,
                      style: ElevatedButton.styleFrom(
                        foregroundColor: Colors.white,
                        backgroundColor:
                            (!_consentGiven) ? Colors.grey[600] : Constants.colorApp,
                        minimumSize: const Size(double.infinity, 50),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                      child: _isLoading
                          ? const SizedBox(
                              width: 24,
                              height: 24,
                              child: CircularProgressIndicator(
                                color: Colors.white,
                                strokeWidth: 2,
                              ),
                            )
                          : const Text(
                              'Update Profile',
                              style: TextStyle(color: Colors.white, fontSize: 16),
                            ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _jobTitleController.dispose();
    _companyNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _mobileController.dispose();
    _aboutYouController.dispose();
    super.dispose();
  }

  Widget _buildTextFormField(
    TextEditingController controller,
    String label, {
    double? height,
    int? maxLines = 1,
    int? maxLength,
    TextInputType? keyboardType,
  }) {
    return Container(
      height: height ?? (maxLines == 1 ? 43 : null),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: Colors.grey[900],
        border: Border.all(
          width: 1,
          color: Colors.grey,
        ),
      ),
      child: TextFormField(
        controller: controller,
        maxLines: maxLines,
        maxLength: maxLength,
        keyboardType: keyboardType,
        decoration: InputDecoration(
          counterText: '',
          contentPadding: EdgeInsets.symmetric(
            horizontal: 10,
            vertical: maxLines == 1 ? 0 : 10,
          ),
          border: InputBorder.none,
          hintText: label,
          hintStyle: const TextStyle(
            fontSize: 15,
            fontFamily: 'verdana_regular',
            fontWeight: FontWeight.w400,
            color: Colors.grey,
          ),
          filled: true,
          focusedBorder: OutlineInputBorder(
            borderSide: BorderSide(
              color: Constants.colorApp,
              width: 2.0,
            ),
            borderRadius: BorderRadius.circular(10),
          ),
          enabledBorder: OutlineInputBorder(
            borderSide: const BorderSide(
              color: Colors.transparent,
              width: 1.5,
            ),
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      ),
    );
  }

  Widget _buildPolicyText() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Checkbox(
          value: _consentGiven,
          onChanged: (value) {
            setState(() {
              _consentGiven = value!;
              // Clear error when consent is given
              if (_consentGiven && _errorText?.contains('consent') == true) {
                _errorText = null;
              }
            });
          },
        ),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              RichText(
                text: TextSpan(
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 12,
                    fontFamily: 'Roboto',
                    letterSpacing: 0.5,
                  ),
                  children: [
                    TextSpan(
                      text: 'By updating the profile, I provide my ',
                    ),
                    TextSpan(
                      text: 'consent',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Constants.colorApp,
                        decoration: TextDecoration.underline,
                      ),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          Get.to(() => ConsentPage());
                        },
                    ),
                    TextSpan(
                      text: ' for the app to use my data as described in the ',
                    ),
                    TextSpan(
                      text: 'privacy policy',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Constants.colorApp,
                        decoration: TextDecoration.underline,
                      ),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          Get.to(() => PrivacyPolicyPage());
                        },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
