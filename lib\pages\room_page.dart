import 'dart:async';
import 'dart:math';

import 'package:agora_rtc_engine/agora_rtc_engine.dart';
import 'package:cmtmeet/service/firebase_service.dart';
import 'package:cmtmeet/service/websocket_service.dart';
import 'package:cmtmeet/utils/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

class RoomPage extends StatefulWidget {
  final String room;
  final String roomName;
  final String token;
  final bool isHost;
  final String roomType;
  RoomPage(
      {required this.room,
      required this.roomName,
      required this.token,
      required this.isHost,
      required this.roomType});

  @override
  State<RoomPage> createState() => _RoomPageState();
}

class _RoomPageState extends State<RoomPage> with WidgetsBindingObserver {
  final WebSocketService webSocketService = Get.find<WebSocketService>();
  StreamSubscription? _socketSubscription;
  final GetStorage storage = GetStorage();
  late RtcEngine _engine;
  List<int> remoteUids = []; // Track remote users
  Map<int, bool> remoteVideoStates = {};
  Map<int, bool> remoteAudioStates = {};
  int? localUid; // Track local user ID
  bool isJoined = false;
  bool isMicOn = true;
  bool isVideoOn = true;

  late bool _isHost;

  // Volume streaming implementation
  final _volumeStreamController = StreamController<Map<int, int>>.broadcast(
    onListen: () => debugPrint('Volume stream subscribed'),
    onCancel: () => debugPrint('Volume stream unsubscribed'),
  );
  Stream<Map<int, int>> get volumeStream => _volumeStreamController.stream;

  @override
  void initState() {
    super.initState();
    _enableWakeLock();
    userBusyStatus.value = true;
    // setUserBusyStatus(true);
    _isHost = widget.isHost;
    localUid = storage.read("user")?["id"] ?? Random().nextInt(100000);
    _initializeAgora();
    _initWebSocketListener();
    WidgetsBinding.instance.addObserver(this);
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    debugPrint('App lifecycle state changed: $state');
    switch (state) {
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
        _disableWakeLock();
        _handleBackgroundState();
        break;
      case AppLifecycleState.resumed:
        _enableWakeLock();
        _handleForegroundState();
        break;
      case AppLifecycleState.detached:
      case AppLifecycleState.hidden:
        _terminateSession();
        break;
    }
  }

  Future<void> _enableWakeLock() async {
    try {
      if (!(await WakelockPlus.enabled)) {
        await WakelockPlus.enable();
        debugPrint('Wakelock enabled');
      }
    } catch (e) {
      debugPrint('Error enabling wakelock: $e');
    }
  }

  Future<void> _disableWakeLock() async {
    try {
      if (await WakelockPlus.enabled) {
        await WakelockPlus.disable();
        debugPrint('Wakelock disabled');
      }
    } catch (e) {
      debugPrint('Error disabling wakelock: $e');
    }
  }

  Future<void> _handleBackgroundState() async {
    debugPrint('App going to background');
    await _engine.muteLocalAudioStream(true);
    await _engine.muteLocalVideoStream(true);
  }

  Future<void> _handleForegroundState() async {
    debugPrint('App returning to foreground');
    if (!isJoined) {
      await _initializeAgora();
    } else {
      await _engine.muteLocalAudioStream(!isMicOn);
      await _engine.muteLocalVideoStream(!isVideoOn);
    }
  }

  Future<void> _terminateSession() async {
    try {
      debugPrint('Terminating session...');
      if (isJoined) {
        await _engine.leaveChannel();
        isJoined = false;
      }
      await _engine.release();
      webSocketService.leaveRoom();
      await _volumeStreamController.close();
      await _socketSubscription?.cancel();

      if (mounted) {
        setState(() {
          remoteUids.clear();
          remoteVideoStates.clear();
          remoteAudioStates.clear();
        });
      }
    } catch (e) {
      debugPrint('Error terminating session: $e');
    } finally {
      // Reset orientation preferences
      SystemChrome.setPreferredOrientations(DeviceOrientation.values);
    }
  }

  Future<void> _initializeAgora() async {
    try {
      await [Permission.microphone, Permission.camera].request();
      _engine = await createAgoraRtcEngine();
      await _engine.initialize(RtcEngineContext(appId: Constants.appId));

      await _engine.enableVideo();
      await _engine.startPreview();
      await _engine.enableAudio();

      _engine.enableAudioVolumeIndication(interval: 200, smooth: 3, reportVad: true);

      _engine.registerEventHandler(RtcEngineEventHandler(
        onJoinChannelSuccess: (connection, elapsed) {
          setState(() {
            isJoined = true;
          });
        },
        onUserJoined: (connection, remoteUid, elapsed) {
          setState(() {
            remoteUids.add(remoteUid);
            remoteVideoStates[remoteUid] = true;
          });
        },
        onAudioVolumeIndication: (connection, speakers, speakerNumber, totalVolume) {
          final volumeMap = <int, int>{};
          for (var speaker in speakers) {
            volumeMap[speaker.uid == 0 ? localUid! : speaker.uid!] = speaker.volume ?? 0;
          }
          _volumeStreamController.add(volumeMap);
        },
        onUserOffline: (connection, remoteUid, reason) {
          setState(() {
            remoteUids.remove(remoteUid);
            remoteVideoStates.remove(remoteUid);
          });
        },
        onUserMuteVideo: (connection, remoteUid, muted) {
          setState(() {
            remoteVideoStates[remoteUid] = !muted;
          });
        },
        onUserMuteAudio: (connection, remoteUid, muted) {
          setState(() {
            remoteAudioStates[remoteUid] = !muted;
          });
        },
        onError: (err, msg) {
          debugPrint("Agora Error: $err, $msg");
        },
      ));
      await _joinChannel();
    } catch (e) {
      debugPrint("Error initializing Agora: $e");
    }
  }

  void _initWebSocketListener() {
    _socketSubscription = webSocketService.socketResponseStream.listen((data) {
      if (!mounted) return;

      if (data['type'] == 'userJoined') {
        _showUserToast(
          message: "${data['fullname'] ?? 'User'} joined",
          icon: Icons.person_add_alt_1,
          color: Colors.green,
        );
      } else if (data['type'] == 'userLeft') {
        _showUserToast(
          message: "${data['fullname'] ?? 'User'} left",
          icon: Icons.person_remove,
          color: Colors.red,
        );
      } else if (data['type'] == 'newHost') {
        final newHostId = data['hostId'];
        setState(() {
          _isHost = newHostId == localUid;
        });

        // Show host transfer notification
        final attendees = storage.read<List<dynamic>>('attendees') ?? [];
        final newHost = attendees.firstWhere(
            (a) => int.tryParse(a["id"].toString()) == newHostId,
            orElse: () => {'fullname': 'You'});

        _showUserToast(
          message: newHost['fullname'] == 'You'
              ? "You are the host"
              : "${newHost['fullname']} is now the host",
          icon: Icons.star,
          color: Colors.blue,
        );
      }
    });
  }

  void _showUserToast({
    required String message,
    required IconData icon,
    required Color color,
  }) {
    Get.rawSnackbar(
      messageText: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 18, color: Colors.white),
          SizedBox(width: 8),
          Text(
            message,
            style: TextStyle(color: Colors.white),
          ),
        ],
      ),
      duration: const Duration(seconds: 2),
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: color.withAlpha(150),
      margin: const EdgeInsets.only(bottom: 100),
      borderRadius: 12,
      animationDuration: const Duration(milliseconds: 300),
      isDismissible: true,
      forwardAnimationCurve: Curves.easeOutBack,
      shouldIconPulse: false,
    );
  }

  @override
  void dispose() {
    _disableWakeLock();
    userBusyStatus.value = false;
    // setUserBusyStatus(false);
    try {
      WidgetsBinding.instance.removeObserver(this);
      _terminateSession();
    } catch (e) {
      debugPrint('Error cleaning up Agora engine: $e');
    }
    super.dispose();
  }

  Future<void> _joinChannel() async {
    try {
      await _engine.joinChannel(
        token: widget.token,
        channelId: widget.room,
        uid: localUid!, // Use the actual user ID here
        options: ChannelMediaOptions(
          channelProfile: ChannelProfileType.channelProfileLiveBroadcasting,
          clientRoleType: ClientRoleType.clientRoleBroadcaster,
          publishCameraTrack: isVideoOn,
          publishMicrophoneTrack: isMicOn,
          autoSubscribeAudio: true,
          autoSubscribeVideo: true,
        ),
      );
    } catch (e) {
      debugPrint("Error joining channel: $e");
    }
  }

  Future<void> _toggleMic() async {
    setState(() {
      isMicOn = !isMicOn;
    });
    await _engine.muteLocalAudioStream(!isMicOn);
  }

  Future<void> _toggleVideo() async {
    try {
      setState(() => isVideoOn = !isVideoOn);
      await _engine.muteLocalVideoStream(!isVideoOn);
      await _engine.enableLocalVideo(isVideoOn);
    } catch (e) {
      debugPrint("Error toggling video: $e");
      setState(() => isVideoOn = !isVideoOn); // Revert on error
    }
  }

  List<int> _getSortedUserIds() {
    // Combine local and remote users
    final allParticipants = [localUid!, ...remoteUids];

    // Sort with host first if needed
    if (widget.isHost) {
      return allParticipants;
    }

    // For non-hosts, the natural order is fine
    return allParticipants;
  }

  @override
  Widget build(BuildContext context) {
    final sortedUserIds = _getSortedUserIds();
    final userCount = sortedUserIds.length;
    List<dynamic> attendees = storage.read<List<dynamic>>('attendees') ?? [];

    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          foregroundColor: Colors.white,
          backgroundColor: Color(0xffc01058),
          title: Text(formatRoomName(widget.roomName), style: TextStyle(fontSize: 18)),
          actions: [
            IconButton(
              icon: Icon(Icons.notifications),
              onPressed: () {
                // Get.toNamed('/notifications');
              },
            ),
            IconButton(
              icon: Icon(Icons.account_circle),
              onPressed: () {
                // Get.toNamed('/profile');
              },
            ),
          ],
        ),
        body: Column(
          children: [
            // Video Call Grid Layout
            Expanded(
              child: Container(
                padding: EdgeInsets.all(12.0),
                color: Colors.black,
                child: Column(
                  children: [
                    // Scrollable grid view
                    Expanded(
                      child: GridView.builder(
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 2,
                          childAspectRatio: 0.75,
                          crossAxisSpacing: 8,
                          mainAxisSpacing: 8,
                        ),
                        itemCount: userCount,
                        itemBuilder: (context, index) {
                          final uid = sortedUserIds[index];

                          final isLocalUser = uid == localUid;
                          Map<String, dynamic> getAttendee(
                              List<dynamic> attendees, int uid) {
                            try {
                              return (attendees.cast<Map<String, dynamic>>()).firstWhere(
                                (a) => int.tryParse(a["id"]?.toString() ?? '0') == uid,
                                orElse: () => <String, dynamic>{},
                              );
                            } catch (e) {
                              return <String, dynamic>{};
                            }
                          }

                          final attendee = getAttendee(attendees, uid);
                          final isVideoEnabled =
                              isLocalUser ? isVideoOn : remoteVideoStates[uid] ?? true;
                          final isAudioEnabled =
                              isLocalUser ? isMicOn : remoteAudioStates[uid] ?? true;
                          return Container(
                            decoration: BoxDecoration(
                              color: Color.fromRGBO(64, 64, 64, 1),
                              borderRadius: BorderRadius.circular(7),
                              border: Border.all(
                                color: Color.fromRGBO(96, 96, 96, 1),
                                width: 3,
                              ),
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(7),
                              child: Stack(
                                children: [
                                  // Video view
                                  isLocalUser
                                      ? isJoined
                                          ? isVideoOn
                                              ? AgoraVideoView(
                                                  controller: VideoViewController(
                                                    rtcEngine: _engine,
                                                    canvas: VideoCanvas(uid: 0),
                                                  ),
                                                )
                                              : _buildBlankView()
                                          : const Center(
                                              child: CircularProgressIndicator(),
                                            )
                                      : isVideoEnabled
                                          ? AgoraVideoView(
                                              controller: VideoViewController.remote(
                                                rtcEngine: _engine,
                                                canvas: VideoCanvas(uid: uid),
                                                connection:
                                                    RtcConnection(channelId: widget.room),
                                              ),
                                            )
                                          : _buildBlankRemoteView(uid: uid),
                                  // User info overlay
                                  Positioned(
                                    top: 8,
                                    left: 8,
                                    child: isVideoEnabled
                                        ? Container(
                                            padding: EdgeInsets.all(4),
                                            decoration: BoxDecoration(
                                              color: Colors.black54,
                                              borderRadius: BorderRadius.circular(4),
                                            ),
                                            child: Text(
                                              isLocalUser
                                                  ? widget.roomType != 'open'
                                                      ? 'You ${_isHost ? "(Host)" : ''}'
                                                      : 'You'
                                                  : '${attendee["fullname"]}',
                                              style: TextStyle(color: Colors.white),
                                            ))
                                        : SizedBox(),
                                  ),
                                  Positioned(
                                    bottom: 8,
                                    left: 8,
                                    child: Container(
                                        padding: EdgeInsets.all(4),
                                        decoration: BoxDecoration(
                                          color: Colors.black54,
                                          borderRadius: BorderRadius.circular(4),
                                        ),
                                        child: Row(
                                          children: [
                                            Icon(
                                              isLocalUser
                                                  ? (isVideoOn
                                                      ? Icons.videocam_sharp
                                                      : Icons.videocam_off_sharp)
                                                  : (isVideoEnabled
                                                      ? Icons.videocam_sharp
                                                      : Icons.videocam_off_sharp),
                                              size: 18,
                                              color: Colors.white,
                                            ),
                                            SizedBox(width: 5),
                                            Icon(
                                              isLocalUser
                                                  ? (isMicOn
                                                      ? Icons.mic_sharp
                                                      : Icons.mic_off_sharp)
                                                  : (isAudioEnabled
                                                      ? Icons.mic_sharp
                                                      : Icons.mic_off_sharp),
                                              size: 17,
                                              color: Colors.white,
                                            )
                                          ],
                                        )),
                                  ),
                                  // Host indicator
                                  if (_isHost && isLocalUser)
                                    Positioned(
                                      top: 8,
                                      right: 8,
                                      child: Container(
                                        padding: EdgeInsets.all(4),
                                        decoration: BoxDecoration(
                                          color: Colors.red,
                                          borderRadius: BorderRadius.circular(4),
                                        ),
                                        child: Text(
                                          'HOST',
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 10,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Bottom Bar with Video Controls
            Container(
              padding: EdgeInsets.symmetric(vertical: 10, horizontal: 20),
              color: Color.fromRGBO(35, 35, 35, 1),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Video Toggle
                  _buildCircleButton(
                    icon: isVideoOn ? Icons.videocam_sharp : Icons.videocam_off_sharp,
                    isActive: isVideoOn,
                    onTap: _toggleVideo,
                  ),
                  SizedBox(width: 20),

                  // Mic Toggle
                  _buildCircleButton(
                    icon: isMicOn ? Icons.mic_sharp : Icons.mic_off_sharp,
                    isActive: isMicOn,
                    onTap: _toggleMic,
                  ),
                  SizedBox(width: 20),

                  // Raise Hand
                  // _buildCircleButton(
                  //   icon: Icons.back_hand_sharp,
                  //   isActive: false,
                  //   onTap: () {
                  //     // Implement raise hand functionality
                  //   },
                  // ),

                  // End Call
                  GestureDetector(
                    onTap: () {
                      _engine.leaveChannel();
                      webSocketService.leaveRoom();
                      Navigator.pop(context);
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: EdgeInsets.symmetric(vertical: 7, horizontal: 20),
                      child: Row(
                        children: [
                          Icon(Icons.call_end_sharp, color: Colors.white),
                          SizedBox(width: 10),
                          Text(
                            'End',
                            style: TextStyle(color: Colors.white),
                          )
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCircleButton({
    required IconData icon,
    required VoidCallback onTap,
    bool isActive = true,
    Color color = const Color.fromRGBO(78, 78, 78, 1),
  }) {
    return GestureDetector(
      onTap: onTap,
      child: CircleAvatar(
        radius: 20,
        backgroundColor: isActive ? Colors.white : color,
        child: Icon(icon, color: isActive ? Colors.black : Colors.white, size: 27),
      ),
    );
  }

  Widget _buildBlankView() {
    return Container(
      color: Colors.black,
      child: Center(
        child: Icon(
          Icons.videocam_off_sharp,
          size: 70,
          color: Colors.red,
        ),
      ),
    );
  }

  Widget _buildBlankRemoteView({int? uid}) {
    final attendees = storage.read<List<dynamic>>('attendees') ?? [];
    final attendee = uid != null
        ? attendees.firstWhere((a) => int.tryParse(a["id"].toString()) == uid,
            orElse: () => {})
        : {};

    return Container(
      color: Colors.grey[800],
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircleAvatar(
              radius: 30,
              backgroundColor: Colors.grey[600],
              child: Icon(
                Icons.person,
                size: 40,
                color: Colors.white,
              ),
            ),
            if (attendee.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Text(
                  attendee["fullname"] ?? 'User',
                  style: TextStyle(color: Colors.white),
                ),
              ),
          ],
        ),
      ),
    );
  }

  String formatRoomName(String roomName) {
    return roomName.replaceAllMapped(RegExp(r'(\d+)'), (match) => ' - ${match.group(1)}');
  }
}
