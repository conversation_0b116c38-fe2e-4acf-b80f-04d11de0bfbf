import 'dart:convert';
import 'dart:io';

import 'package:add_2_calendar/add_2_calendar.dart';
import 'package:android_intent_plus/android_intent.dart';
import 'package:cmtmeet/pages/meeting_room.dart';
import 'package:cmtmeet/utils/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';

class MeetingItem {
  final String id;
  final String title;
  final DateTime time;
  final String status;
  final bool isSent;
  final String userWith;

  MeetingItem({
    required this.id,
    required this.title,
    required this.time,
    required this.status,
    required this.isSent,
    required this.userWith,
  });
}

class ScheduleMeetingPage extends StatefulWidget {
  const ScheduleMeetingPage({super.key});

  @override
  State<ScheduleMeetingPage> createState() => _ScheduleMeetingPageState();
}

class _ScheduleMeetingPageState extends State<ScheduleMeetingPage> {
  final GetStorage storage = GetStorage();
  late List<dynamic> attendees;
  bool isLoading = false;

  String _selectedSection = "Upcoming";
  List<MeetingItem> upcomingMeetings = [];
  List<MeetingItem> completedMeetings = [];

  @override
  void initState() {
    super.initState();
    attendees = storage.read<List<dynamic>>('attendees') ?? [];
    loadMeetings();
  }

  Future<void> loadMeetings({bool showLoader = true}) async {
    if (showLoader) setState(() => isLoading = true);
    final fetchedMeetings = await fetchMeetings();
    final processed = processMeetings(fetchedMeetings);

    // Split into upcoming and completed meetings
    final now = DateTime.now();
    upcomingMeetings = processed
        .where((m) =>
            m.time.isAfter(now) || now.isBefore(m.time.add(const Duration(hours: 1))))
        .toList();

    completedMeetings = processed
        .where((m) => m.time.add(const Duration(hours: 1)).isBefore(now))
        .toList();

    // Sort meetings
    upcomingMeetings
        .sort((a, b) => a.time.compareTo(b.time)); // Ascending (earliest first)
    completedMeetings
        .sort((a, b) => b.time.compareTo(a.time)); // Descending (most recent first)

    setState(() {
      isLoading = false;
    });
  }

  Future<Map<String, dynamic>> fetchMeetings() async {
    final agoraId = storage.read("user")?["agoraid"]?.toString();

    if (agoraId == null || agoraId.isEmpty) {
      _showError("User ID is missing.");
      return {};
    }

    try {
      final response = await http.post(
        Uri.parse("https://${Constants.url}/meet/fetch-meetings"),
        headers: {"Content-Type": "application/json"},
        body: jsonEncode({"agoraId": agoraId}),
      );

      if (response.statusCode == 200) {
        final body = jsonDecode(response.body);
        print(body);
        return body["meetings"] ?? {};
      } else {
        _showError("Failed to fetch meetings: ${response.statusCode}");
      }
    } catch (e, stackTrace) {
      print("❌ Error fetching meetings: $e\n$stackTrace");
      _showError("Failed to fetch meetings. Please try again.");
    }

    return {};
  }

  void _showError(String message) {
    if (!mounted) return;

    ScaffoldMessenger.of(Get.context!).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(10),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  List<MeetingItem> processMeetings(Map<String, dynamic> meetings) {
    final sent = meetings['sent'] as List<dynamic>;
    final received = meetings['received'] as List<dynamic>;

    return [
      ...sent.map((m) => MeetingItem(
            id: m['MeetingId'],
            title: m['Title'],
            time: DateTime.parse(m['UtcDateTime']).toLocal(),
            status: m['Status'],
            isSent: true,
            userWith: m['AttendeeId'],
          )),
      ...received.map((m) => MeetingItem(
            id: m['MeetingId'],
            title: m['Title'],
            time: DateTime.parse(m['UtcDateTime']).toLocal(),
            status: m['Status'],
            isSent: false,
            userWith: m['CreatorId'],
          )),
    ];
  }

  void _deleteMeeting(String id) async {
    setState(() => isLoading = true);
    try {
      final response = await http.post(
        Uri.parse("https://${Constants.url}/meet/delete-meeting"),
        headers: {"Content-Type": "application/json"},
        body: jsonEncode({"meetingId": id}),
      );

      if (response.statusCode == 200) {
        // Update both lists
        upcomingMeetings.removeWhere((m) => m.id == id);
        completedMeetings.removeWhere((m) => m.id == id);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Meeting deleted')),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to delete meeting')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error deleting meeting')),
      );
    } finally {
      setState(() => isLoading = false);
    }
  }

  Future<Map<String, dynamic>> getAttendee(String agoraId) async {
    final match = attendees.firstWhereOrNull((a) => a['agoraid'] == agoraId);
    if (match == null) throw Exception('Attendee not found');
    return match;
  }

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final currentMeetings =
        _selectedSection == "Upcoming" ? upcomingMeetings : completedMeetings;

    return Scaffold(
      body: Container(
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.symmetric(vertical: 10.0, horizontal: 8.0),
              child: Row(
                children: ["Upcoming", "Completed"].map((section) {
                  final isSelected = _selectedSection == section;
                  return Expanded(
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          _selectedSection = section;
                        });
                      },
                      child: Container(
                        padding: EdgeInsets.symmetric(vertical: 3.0),
                        margin: EdgeInsets.symmetric(horizontal: 6.0),
                        decoration: BoxDecoration(
                          color: isSelected
                              ? Color.fromARGB(255, 175, 16, 82)
                              : Colors.grey[300],
                          borderRadius: BorderRadius.circular(20.0),
                        ),
                        child: Text(
                          section,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: isSelected ? Colors.white : Colors.black,
                            fontSize: 16.0,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
            SizedBox(
              height: 1.0,
              width: width,
              child: Container(color: Colors.grey[400]),
            ),
            Expanded(
              child: Container(
                padding: EdgeInsets.only(right: 12, left: 12, top: 10),
                color: Colors.grey[200],
                child: isLoading
                    ? Center(child: CircularProgressIndicator())
                    : currentMeetings.isEmpty
                        ? Center(
                            child: Text('No ${_selectedSection.toLowerCase()} meetings'))
                        : RefreshIndicator(
                            onRefresh: () async => await loadMeetings(showLoader: false),
                            child: ListView.builder(
                              itemCount: currentMeetings.length,
                              itemBuilder: (context, index) {
                                final meeting = currentMeetings[index];
                                return Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 8.0, vertical: 4.0),
                                  child: Slidable(
                                    key: Key(meeting.id),
                                    endActionPane: ActionPane(
                                      motion: const DrawerMotion(),
                                      extentRatio: 0.25,
                                      children: [
                                        SlidableAction(
                                          onPressed: (_) => _deleteMeeting(meeting.id),
                                          backgroundColor: Colors.red,
                                          foregroundColor: Colors.white,
                                          icon: Icons.delete,
                                          label: 'Delete',
                                        ),
                                      ],
                                    ),
                                    child: FutureBuilder<Widget>(
                                      future: MeetingCard(meeting),
                                      builder: (context, snapshot) {
                                        if (snapshot.connectionState ==
                                            ConnectionState.waiting) {
                                          return const Card(
                                            margin: EdgeInsets.symmetric(vertical: 6.0),
                                            child: Padding(
                                              padding: EdgeInsets.all(24.0),
                                              child: Center(
                                                  child: CircularProgressIndicator()),
                                            ),
                                          );
                                        } else if (snapshot.hasError) {
                                          return Card(
                                            margin:
                                                const EdgeInsets.symmetric(vertical: 6.0),
                                            child: Padding(
                                              padding: const EdgeInsets.all(24.0),
                                              child: Center(
                                                child: Text('Error loading meeting'),
                                              ),
                                            ),
                                          );
                                        } else {
                                          return snapshot.data!;
                                        }
                                      },
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
              ),
            )
          ],
        ),
      ),
    );
  }

  Future<Widget> MeetingCard(MeetingItem meeting) async {
    final attendee = await getAttendee(meeting.userWith);
    final dateTime = meeting.time;
    final now = DateTime.now();
    final canJoin =
        now.isAfter(dateTime) && now.isBefore(dateTime.add(const Duration(hours: 1)));
    final meetingStarted = now.isAfter(dateTime);
    final isCompleted = meeting.time.add(const Duration(hours: 1)).isBefore(now);

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 6.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10.0),
      ),
      elevation: 2,
      color: Colors.white,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 10.0),
            decoration: BoxDecoration(
              color:
                  isCompleted ? Colors.grey : (canJoin ? Colors.green : Colors.black54),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(10.0),
                topRight: Radius.circular(10.0),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 5,
                  child: Text(
                    DateFormat('dd MMM yyyy').format(dateTime),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Expanded(
                  flex: 5,
                  child: Text(
                    DateFormat('hh:mm a').format(dateTime),
                    textAlign: TextAlign.end,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 10),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: Column(
              children: [
                Row(
                  children: [
                    const Icon(Icons.event_note_sharp,
                        size: 50, color: Color(0xffc01058)),
                    const SizedBox(width: 12),
                    Flexible(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            attendee['fullname'],
                            style: TextStyle(fontWeight: FontWeight.bold, fontSize: 17),
                          ),
                          Text(
                            meeting.title,
                            style: const TextStyle(fontSize: 14),
                            softWrap: true,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 10),
                        ],
                      ),
                    )
                  ],
                ),
                Container(height: 0.5, color: Colors.grey[500]),
                Container(
                  padding: EdgeInsets.symmetric(vertical: 12),
                  child: isCompleted
                      ? Row(
                          // Simplified UI for completed meetings
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            Text(
                              'Closed',
                              style: TextStyle(
                                  color: Colors.grey[600], fontWeight: FontWeight.bold),
                            ),
                            GestureDetector(
                              onTap: () => navigateToCMTConnectPage(meeting.userWith),
                              child: Text(
                                'Chat',
                                style: TextStyle(
                                    color: Color(0xffc01058),
                                    fontWeight: FontWeight.bold),
                              ),
                            ),
                          ],
                        )
                      : Row(
                          // Original UI for upcoming/active meetings
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            if (meeting.status == 'Accepted')
                              GestureDetector(
                                onTap:
                                    meetingStarted ? null : () => _addToCalendar(meeting),
                                child: Text(
                                  'Add to Calendar',
                                  style: TextStyle(
                                      color: meetingStarted
                                          ? Colors.grey[500]
                                          : const Color(0xffc01058),
                                      fontWeight: FontWeight.bold),
                                ),
                              ),
                            if (meeting.status == 'Pending' && !meeting.isSent)
                              GestureDetector(
                                onTap: () => _sendMeetingStatus(
                                    meeting.id, meeting.userWith, 'Declined'),
                                child: Text(
                                  'Decline',
                                  style: TextStyle(
                                      color: const Color(0xffc01058),
                                      fontWeight: FontWeight.bold),
                                ),
                              ),
                            GestureDetector(
                              onTap: () => navigateToCMTConnectPage(meeting.userWith),
                              child: Text(
                                'Chat',
                                style: TextStyle(
                                    color: Color(0xffc01058),
                                    fontWeight: FontWeight.bold),
                              ),
                            ),
                            if (meeting.status == 'Pending' && meeting.isSent)
                              Text(
                                'Pending',
                                style: TextStyle(
                                    color: Colors.grey[500], fontWeight: FontWeight.bold),
                              ),
                            if (meeting.status == 'Pending' && !meeting.isSent)
                              GestureDetector(
                                onTap: () => _sendMeetingStatus(
                                    meeting.id, meeting.userWith, 'Accepted'),
                                child: Text(
                                  'Accept',
                                  style: TextStyle(
                                      color: const Color(0xffc01058),
                                      fontWeight: FontWeight.bold),
                                ),
                              ),
                            if (meeting.status == 'Accepted')
                              GestureDetector(
                                onTap: canJoin ? () => _startMeeting(meeting) : null,
                                child: Text(
                                  (canJoin ? 'Attend Meeting' : 'Accepted'),
                                  style: TextStyle(
                                      color: canJoin
                                          ? const Color(0xffc01058)
                                          : Colors.grey[500],
                                      fontWeight: FontWeight.bold),
                                ),
                              ),
                            if (meeting.status == 'Declined')
                              Text(
                                'Declined',
                                style: TextStyle(
                                    color: Colors.grey[500], fontWeight: FontWeight.bold),
                              ),
                          ],
                        ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _sendMeetingStatus(
      String meetingId, String creatorId, String status) async {
    final attendeeName = storage.read("user")["fullname"].toString();

    try {
      setState(() => isLoading = true);
      final response = await http.post(
        Uri.parse("https://${Constants.url}/meet/meeting-status"),
        headers: {"Content-Type": "application/json"},
        body: jsonEncode({
          "attendeeName": attendeeName,
          "creatorId": creatorId,
          "meetingId": meetingId,
          "status": status,
        }),
      );

      if (response.statusCode == 200) {
        final responseBody = jsonDecode(response.body);
        final message = responseBody["message"];

        // Immediately update the local state
        setState(() {
          // Update in upcoming meetings
          final upcomingIndex = upcomingMeetings.indexWhere((m) => m.id == meetingId);
          if (upcomingIndex != -1) {
            upcomingMeetings[upcomingIndex] = MeetingItem(
              id: meetingId,
              title: upcomingMeetings[upcomingIndex].title,
              time: upcomingMeetings[upcomingIndex].time,
              status: status,
              isSent: upcomingMeetings[upcomingIndex].isSent,
              userWith: upcomingMeetings[upcomingIndex].userWith,
            );
          }

          // Update in completed meetings
          final completedIndex = completedMeetings.indexWhere((m) => m.id == meetingId);
          if (completedIndex != -1) {
            completedMeetings[completedIndex] = MeetingItem(
              id: meetingId,
              title: completedMeetings[completedIndex].title,
              time: completedMeetings[completedIndex].time,
              status: status,
              isSent: completedMeetings[completedIndex].isSent,
              userWith: completedMeetings[completedIndex].userWith,
            );
          }
        });

        // Then refresh from server to ensure consistency
        await loadMeetings(showLoader: false);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(message),
              duration: Duration(seconds: 2),
            ),
          );
        }
      } else {
        _showError("Failed to update meeting status: ${response.statusCode}");
      }
    } catch (e, stackTrace) {
      print("❌ Error updating meeting status: $e\n$stackTrace");
      _showError("Failed to update meeting status. Please try again.");
    } finally {
      if (mounted) {
        setState(() => isLoading = false);
      }
    }
  }

  void navigateToCMTConnectPage(String AgoraId) {
    Get.offAllNamed(
      '/user/dashboard',
      arguments: {
        'initialTab': 2,
        'connectSection': 'Online',
        'openChatWith': AgoraId,
      },
    );
  }

  Future<void> _startMeeting(MeetingItem meeting) async {
    final callerName = storage.read("user")["fullname"].toString();

    try {
      final response = await http.post(
        Uri.parse("https://${Constants.url}/meet/start-meeting"),
        headers: {"Content-Type": "application/json"},
        body: jsonEncode({
          "callerName": callerName,
          "calleeAgoraId": meeting.userWith,
          "channelName": meeting.id,
        }),
      );

      if (response.statusCode == 200) {
        final responseBody = jsonDecode(response.body);
        final token = responseBody["token"];

        Get.to(() => MeetingRoom(room: meeting.id, token: token));
      } else {
        _showError("Failed to start meet: ${response.statusCode}");
      }
    } catch (e, stackTrace) {
      print("❌ Error starting meet: $e\n$stackTrace");
      _showError("Failed to start meet. Please try again.");
    }
  }

  Future<void> _addToCalendar(MeetingItem meeting) async {
    try {
      final attendee = await getAttendee(meeting.userWith);
      final dateTime = meeting.time;

      if (Platform.isAndroid) {
        final intent = AndroidIntent(
          action: 'android.intent.action.INSERT',
          data: 'content://com.android.calendar/events',
          arguments: {
            'title': meeting.title,
            'description': 'Meeting with ${attendee['fullname'] ?? ''}',
            'beginTime': dateTime.millisecondsSinceEpoch,
            'endTime': dateTime.add(const Duration(hours: 1)).millisecondsSinceEpoch,
            'allDay': false,
          },
        );
        await intent.launch();
      } else if (Platform.isIOS) {
        final Event event = Event(
          title: meeting.title,
          description: 'Meeting with ${attendee['fullname'] ?? ''}',
          startDate: dateTime,
          endDate: dateTime.add(const Duration(hours: 1)),
          iosParams: const IOSParams(
            reminder: Duration(minutes: 10),
          ),
        );
        await Add2Calendar.addEvent2Cal(event);
      }
    } catch (e, stackTrace) {
      print('Error adding to calendar: $e\n$stackTrace');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to add to calendar: ${e.toString()}'),
            action: SnackBarAction(
              label: 'Retry',
              onPressed: () => _addToCalendar(meeting),
            ),
          ),
        );
      }
    }
  }
}

class ScheduleMeetingDialog extends StatefulWidget {
  final Function(Map<String, dynamic>) onSchedule;

  const ScheduleMeetingDialog({required this.onSchedule, Key? key}) : super(key: key);

  @override
  _ScheduleMeetingDialogState createState() => _ScheduleMeetingDialogState();
}

class _ScheduleMeetingDialogState extends State<ScheduleMeetingDialog> {
  final storage = GetStorage();
  late Map<String, dynamic> event;
  late DateTime eventEndDate;
  DateTime _selectedDate = DateTime.now();
  TimeOfDay _selectedTime = TimeOfDay.now();
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _dateController = TextEditingController();
  final TextEditingController _timeController = TextEditingController();

  String? _titleError;

  @override
  void initState() {
    event = storage.read("event") ?? {};
    eventEndDate = DateTime.parse(event["endDate"]);
    super.initState();
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now(),
      lastDate: eventEndDate,
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
        _dateController.text = DateFormat('dd MMM yyyy').format(_selectedDate);
      });
    }
  }

  Future<void> _selectTime(BuildContext context) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _selectedTime,
    );
    if (picked != null && picked != _selectedTime) {
      setState(() {
        _selectedTime = picked;
        _timeController.text = _selectedTime.format(context);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final scheduledDateTime = DateTime(
      _selectedDate.year,
      _selectedDate.month,
      _selectedDate.day,
      _selectedTime.hour,
      _selectedTime.minute,
    );
    _dateController.text = DateFormat('dd MMM yyyy').format(_selectedDate); // 05 Apr 2025
    _timeController.text = _selectedTime.format(context); // 05:00 PM

    return AlertDialog(
      titlePadding: EdgeInsets.only(left: 15, right: 15, top: 14, bottom: 10),
      contentPadding: EdgeInsets.symmetric(horizontal: 20, vertical: 15),
      actionsPadding: EdgeInsets.only(right: 20, left: 20, top: 10, bottom: 18),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(12.0)),
      ),
      title: Row(
        children: [
          Icon(Icons.event_note_sharp, size: 35, color: Color(0xffc01058)),
          SizedBox(width: 10),
          Text(
            'Schedule Meeting',
            style: TextStyle(fontSize: 20),
          ),
        ],
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildTextFormField(_titleController, 'Title', errorText: _titleError),
            SizedBox(height: 18),
            _buildTextFormField(
              _dateController,
              'Select Date',
              suffixIcon: Icons.calendar_month_outlined,
              onTap: () => _selectDate(context),
            ),
            SizedBox(height: 18),
            _buildTextFormField(
              _timeController,
              'Select Time',
              suffixIcon: Icons.access_time,
              onTap: () => _selectTime(context),
            ),
          ],
        ),
      ),
      actions: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            GestureDetector(
              onTap: () => Navigator.pop(context),
              child: Container(
                  width: width * 0.27,
                  padding: EdgeInsets.symmetric(vertical: 7),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(30),
                    color: Colors.grey[300],
                  ),
                  child: Text(
                    'Cancel',
                    textAlign: TextAlign.center,
                    style: TextStyle(color: Colors.grey[700]),
                  )),
            ),
            GestureDetector(
              onTap: () {
                if (_titleController.text.isEmpty) {
                  setState(() {
                    _titleError = 'Please enter a title';
                  });
                  return;
                }

                final utcDateTime = scheduledDateTime.toUtc();

                widget.onSchedule({
                  'title': _titleController.text,
                  'utcDateTime': utcDateTime.toIso8601String(),
                });
                Navigator.pop(context);
              },
              child: Container(
                width: width * 0.27,
                padding: EdgeInsets.symmetric(vertical: 7),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(30),
                  color: Color(0xffc01058),
                ),
                child: Text(
                  'Schedule',
                  textAlign: TextAlign.center,
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTextFormField(
    TextEditingController controller,
    String label, {
    IconData? suffixIcon,
    VoidCallback? onTap,
    String? errorText, // Add errorText parameter
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: 50,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.0),
            color: Colors.grey[900],
            border: Border.all(
              width: 1,
              color: errorText != null ? Colors.red : Colors.grey,
            ),
          ),
          child: TextFormField(
            controller: controller,
            readOnly: onTap != null,
            onTap: onTap,
            onChanged: (value) {
              // Clear error when user starts typing
              if (value.isNotEmpty && _titleError != null) {
                setState(() {
                  _titleError = null;
                });
              }
            },
            decoration: InputDecoration(
              suffixIcon: suffixIcon != null ? Icon(suffixIcon) : null,
              border: InputBorder.none,
              hintText: label,
              hintStyle: TextStyle(
                fontSize: 14,
                fontFamily: 'verdana_regular',
                fontWeight: FontWeight.w400,
              ),
              filled: true,
              focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(
                  color: errorText != null ? Colors.red : Constants.colorApp,
                  width: 2.0,
                ),
                borderRadius: BorderRadius.circular(10.0),
              ),
              enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(
                  color: errorText != null ? Colors.red : Colors.transparent,
                  width: 1.5,
                ),
                borderRadius: BorderRadius.circular(10.0),
              ),
            ),
          ),
        ),
        if (errorText != null) // Show error text if it exists
          Padding(
            padding: const EdgeInsets.only(left: 12.0, top: 4.0),
            child: Text(
              errorText,
              style: TextStyle(
                color: Colors.red,
                fontSize: 12,
              ),
            ),
          ),
      ],
    );
  }
}
