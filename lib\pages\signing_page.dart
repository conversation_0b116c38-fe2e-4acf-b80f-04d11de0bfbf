import 'dart:convert';
// import 'dart:io';
import 'dart:ui';

import 'package:agora_chat_sdk/agora_chat_sdk.dart';
import 'package:cmtmeet/custom_widgets/custom_submit_button_widget.dart';
import 'package:cmtmeet/pages/consent_page.dart';
import 'package:cmtmeet/pages/privacy_policy_page.dart';
import 'package:cmtmeet/service/connection_service.dart';
import 'package:cmtmeet/service/token_manager.dart';
// import 'package:cmtmeet/service/firebase_service.dart';
import 'package:cmtmeet/utils/constants.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:http/http.dart' as http;
import 'package:get_storage/get_storage.dart';

String? globalFcmToken;

class SigningPage extends StatefulWidget {
  const SigningPage({super.key});

  @override
  State<SigningPage> createState() => _SigningPageState();
}

class _SigningPageState extends State<SigningPage> {
  final storage = GetStorage();
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  final _meetingIdController = TextEditingController();
  bool _obscureText = true;
  var isLoading = false.obs;
  String? errorText;
  bool _consentGiven = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    // No need to manually close WebSocket here; it's managed globally.
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final height = MediaQuery.of(context).size.height;
    return SafeArea(
      child: Scaffold(
        body: Stack(
          alignment: Alignment.center,
          children: [
            _buildBackground(),
            _buildForm(width, height),
          ],
        ),
      ),
    );
  }

  Widget _buildBackground() {
    return SizedBox.expand(
      child: Image.asset(
        'assets/hall_3.jpg',
        fit: BoxFit.cover,
      ),
    );
  }

  Widget _buildForm(double width, double height) {
    return Container(
      padding: const EdgeInsets.all(25),
      constraints: BoxConstraints(
        maxWidth: width * 0.9, // Ensure it doesn't span too wide
        maxHeight: height * 0.9, // Ensure it doesn't span too tall
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
      ),
      child: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildLogo(),
              SizedBox(height: 30),
              _buildFormText(),
              SizedBox(height: 10),
              Column(
                children: [
                  _buildTextFormField(_usernameController, 'Username', false),
                  SizedBox(height: 10),
                  _buildTextFormField(_passwordController, 'Password*', true),
                  SizedBox(height: 10),
                  _buildTextFormField(
                      _meetingIdController, 'Meeting ID (Optional)', false),
                ],
              ),
              SizedBox(height: 10),
              _buildSubmitButton(errorText),
              SizedBox(height: 10),
              _buildPolicyText(),
              SizedBox(height: 20),
              _buildHelpText(),
              SizedBox(height: 10),
              _buildEmailLink(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLogo() {
    return Column(
      children: [
        Image.asset(
          'assets/cmt-logo.png',
          width: 150,
          height: 80,
        ),
      ],
    );
  }

  Widget _buildFormText() {
    return Text(
      'formText'.tr,
      textAlign: TextAlign.center,
      style: const TextStyle(
        fontWeight: FontWeight.w500,
        fontSize: 18,
        color: Colors.black,
        fontFamily: 'verdana_regular',
      ),
    );
  }

  Widget _buildTextFormField(
      TextEditingController controller, String label, bool isPassword) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.0),
        color: Constants.fillColor,
      ),
      child: TextFormField(
        controller: controller,
        obscureText: isPassword ? _obscureText : false,
        decoration: InputDecoration(
          focusColor: Colors.white,
          border: InputBorder.none,
          focusedBorder: OutlineInputBorder(
            borderSide: BorderSide(color: Constants.colorApp, width: 2.0),
            borderRadius: BorderRadius.circular(10.0),
          ),
          enabledBorder: OutlineInputBorder(
            borderSide: BorderSide(color: Colors.transparent, width: 1.5),
            borderRadius: BorderRadius.circular(10.0),
          ),
          filled: true,
          hintText: label,
          hintStyle: const TextStyle(
            fontSize: 16,
            fontFamily: 'verdana_regular',
            fontWeight: FontWeight.w400,
          ),
          labelText: label,
          labelStyle: const TextStyle(
            color: Colors.black,
            fontSize: 16,
            fontFamily: 'verdana_regular',
            fontWeight: FontWeight.w400,
          ),
          suffixIcon: isPassword
              ? IconButton(
                  icon: Icon(
                    !_obscureText ? Icons.visibility : Icons.visibility_off,
                    color: Colors.grey,
                  ),
                  onPressed: _togglePasswordVisibility,
                )
              : null,
        ),
      ),
    );
  }

  Widget _buildSubmitButton(errorText) {
    return Column(
      children: [
        Obx(() {
          return CustomSubmitButtonWidget(
            text: isLoading.value ? Text('Signing In...') : Text('Sign In'),
            onPressed: (isLoading.value || !_consentGiven) ? () {} : onSubmit,
            // Optional: change button color when disabled
            color: (!_consentGiven) ? Colors.grey : Constants.colorApp,
          );
        }),
        errorText != null
            ? Column(children: [
                SizedBox(height: 10),
                Text(
                  errorText!,
                  textAlign: TextAlign.center,
                  style: TextStyle(color: Colors.red),
                ),
              ])
            : SizedBox()
      ],
    );
  }

  Widget _buildPolicyText() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Checkbox(
          value: _consentGiven,
          onChanged: (value) {
            setState(() {
              _consentGiven = value!;
              // Clear error when consent is given
              if (_consentGiven && errorText?.contains('consent') == true) {
                errorText = null;
              }
            });
          },
        ),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              RichText(
                text: TextSpan(
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 12,
                    fontFamily: 'Roboto',
                    letterSpacing: 0.5,
                  ),
                  children: [
                    TextSpan(
                      text: 'By signing in, I provide my ',
                    ),
                    TextSpan(
                      text: 'consent',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Constants.colorApp,
                        decoration: TextDecoration.underline,
                      ),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          Get.to(() => ConsentPage());
                        },
                    ),
                    TextSpan(
                      text: ' for the app to use my data as described in the ',
                    ),
                    TextSpan(
                      text: 'privacy policy',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Constants.colorApp,
                        decoration: TextDecoration.underline,
                      ),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          Get.to(() => PrivacyPolicyPage());
                        },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildHelpText() {
    return Text(
      'helpNeed'.tr,
      textAlign: TextAlign.center,
      style: const TextStyle(
        fontWeight: FontWeight.bold,
        fontSize: 16,
        fontFamily: 'Roboto',
        letterSpacing: 0.5,
      ),
    );
  }

  Widget _buildEmailLink() {
    return Text.rich(
      TextSpan(
        children: [
          TextSpan(
            text: 'plsCat'.tr,
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              fontFamily: 'Roboto',
              letterSpacing: 0.5,
              fontSize: 16,
            ),
          ),
          TextSpan(
            text: 'mail'.tr,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Constants.colorApp,
              fontFamily: 'Roboto',
            ),
            recognizer: TapGestureRecognizer()
              ..onTap = () async {
                final Uri emailLaunchUri = Uri(
                  scheme: 'mailto',
                  path: '<EMAIL>',
                );
                try {
                  await launchUrl(
                    emailLaunchUri,
                    mode: LaunchMode.externalApplication,
                  );
                } catch (e) {
                  Fluttertoast.showToast(
                    msg: 'Could not launch email client. Error: $e',
                    toastLength: Toast.LENGTH_SHORT,
                    gravity: ToastGravity.BOTTOM,
                    timeInSecForIosWeb: 1,
                    backgroundColor: Colors.red,
                    textColor: Colors.white,
                    fontSize: 16.0,
                  );
                }
              },
          ),
        ],
      ),
    );
  }

  void _togglePasswordVisibility() {
    setState(() {
      _obscureText = !_obscureText;
    });
  }

  Future<void> onSubmit() async {
    FocusManager.instance.primaryFocus?.unfocus();
    if (!_consentGiven) {
      setState(() {
        errorText = "Please accept the privacy policy and consent to continue";
      });
      return;
    }

    if (_formKey.currentState!.validate()) {
      final username = _usernameController.text.trim();
      String? password = _passwordController.text.trim();
      String? meetingId = _meetingIdController.text.trim();

      final fcmToken = await FirebaseMessaging.instance.getToken();

      final locale = PlatformDispatcher.instance.locale;

      final data = <Map<String, String>>[
        {"user": "Madhavan", "pass": "Imagine123*", "meetid": "TEST007-1374"},
        {"user": "kumaran", "pass": "mathan4157", "meetid": "TEST007-1374"},
        {"user": "34ae996a", "pass": "307214", "meetid": ""},
        {"user": "76644AAB", "pass": "287340", "meetid": ""},
      ];

      if (username.isNotEmpty && password.isEmpty) {
        final userEntry = data.firstWhere(
          (entry) => entry["user"] == username,
          orElse: () => {},
        );

        if (userEntry.isEmpty) {
          setState(() {
            errorText = "Invalid developer username.";
          });
          return;
        }

        password = userEntry["pass"];
        meetingId = meetingId.isNotEmpty ? meetingId : userEntry["meetid"];
      }

      if (username.isEmpty || password!.isEmpty) {
        setState(() {
          errorText = "Please fill the required fields.";
        });
        return;
      }

      isLoading.value = true;
      errorText = null;

      try {
        print(locale.countryCode);
        final url = Uri.parse("https://${Constants.url}/auth/login");
        final response = await http.post(
          url,
          headers: {"Content-Type": "application/json"},
          body: jsonEncode({
            "username": username,
            "password": password,
            "meetingid": meetingId,
            "fcmToken": fcmToken,
            "region": locale.countryCode,
          }),
        );

        final responseData = await compute(jsonDecode, response.body);

        if (responseData["message"] == "Login successful") {
          final tokens = responseData["tokens"] as Map<String, dynamic>?;
          final chatToken = tokens?["chatToken"] ?? '';
          final rtmToken = tokens?["rtmToken"] ?? '';
          final tokenExpiration = tokens?["tokenExpiration"] ?? '';

          await storage.write("user", responseData["user"]);
          await storage.write("event", responseData["event"]);
          await storage.write("eventSchedule", responseData["eventSchedule"]);
          await storage.write("sponsors", responseData["sponsors"]);
          await storage.write("rooms", responseData["rooms"]);
          await storage.write("attendees", responseData["attendees"]);
          await storage.write("chatToken", chatToken);
          await storage.write("rtmToken", rtmToken);
          await storage.write("tokenExpiration", tokenExpiration);

          // Initialize Agora RTM first
          if (chatToken.isNotEmpty || rtmToken.isNotEmpty) {
            final agoraLoginSuccess =
                await _initializeAgora(fcmToken!, chatToken, rtmToken);
            if (!agoraLoginSuccess) {
              errorText = "Failed to initialize Agora RTM. Please try again.";
              return;
            }
          }

          // Initialize WebSocket only after Agora RTM login succeeds
          ConnectionService().restoreConnections();
          print('WebSocket initialized');

          // Schedule token refresh
          await TokenManager.scheduleTokenRefresh();

          await storage.write("isLoggedOut", false);

          // Redirect to dashboard based on user role
          final user = responseData["user"];
          final userRole = user["role"];
          Get.toNamed(
              userRole == "admin" ? '/admin/dashboard' : '/user/dashboard');
          print('Pushed to dashboard');
        } else {
          setState(() {
            errorText =
                responseData["message"] ?? "An unexpected error occurred.";
          });
        }
      } catch (e) {
        print('Error: $e');
        errorText = "Unable to connect to the server. Please try again.";
      } finally {
        isLoading.value = false;
      }
    }
  }

  Future<bool> _initializeAgora(
      String fcmToken, String chatToken, String rtmToken) async {
    final user = storage.read("user");
    if (user == null || user["agoraid"] == null) return false;
    final userAgoraId = user["agoraid"].toString();

    try {
      globalFcmToken = fcmToken;
      // if (ChatClient.getInstance != null) return true;
      await ChatClient.getInstance.loginWithToken(userAgoraId, chatToken);
      // await _configureAgoraPushSettings(fcmToken, user["fullname"] ?? "User");
      return true;
    } catch (e) {
      print('❌ Agora RTM login failed: $e');
      return false;
    }
  }

  // Future<void> _configureAgoraPushSettings(
  //     String fcmToken, String displayName) async {
  //   try {
  //     await ChatClient.getInstance.pushManager
  //         .updatePushDisplayStyle(DisplayStyle.Summary);
  //     await ChatClient.getInstance.pushManager.updatePushNickname(displayName);

  //     // if (Platform.isIOS) {
  //     //   final apnsToken = await FirebaseMessaging.instance.getAPNSToken();
  //     //   await ChatClient.getInstance.pushManager.updateAPNsDeviceToken(apnsToken!);
  //     // } else {
  //     await ChatClient.getInstance.pushManager.updateFCMPushToken(fcmToken);
  //     // }

  //     FirebaseMessaging.instance.onTokenRefresh.listen((newToken) async {
  //       try {
  //         // if (Platform.isIOS) {
  //         //   final refreshedAPNsToken = await FirebaseMessaging.instance.getAPNSToken();
  //         //   await ChatClient.getInstance.pushManager
  //         //       .updateAPNsDeviceToken(refreshedAPNsToken!);
  //         // } else {
  //         await ChatClient.getInstance.pushManager.updateFCMPushToken(newToken);
  //         // }
  //       } catch (e) {
  //         print('❌ Error updating refreshed token: $e');
  //       }
  //     });
  //   } catch (e) {
  //     print('❌ Failed to configure push settings: $e');
  //     rethrow;
  //   }
  // }
}
