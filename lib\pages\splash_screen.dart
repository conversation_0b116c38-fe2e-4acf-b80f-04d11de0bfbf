import 'package:flutter/material.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:get/get.dart';
import 'package:cmtmeet/service/auth_service.dart';
import 'package:get_storage/get_storage.dart';

// class SplashScreen extends StatefulWidget {
//   const SplashScreen({super.key});

//   @override
//   State<SplashScreen> createState() => _SplashScreenState();
// }

// class _SplashScreenState extends State<SplashScreen> {
//   final _authService = AuthService();
//   bool _isInitializing = false;

//   @override
//   void initState() {
//     super.initState();
//     _initializeApp();
//   }

//   Future<void> _initializeApp() async {
//     if (_isInitializing) return;
//     _isInitializing = true;

//     try {
//       final route = await _determineInitialRoute();
//       // FlutterNativeSplash.remove();
//       Get.offAllNamed(route);
//     } catch (e) {
//       // FlutterNativeSplash.remove();
//       Get.offAllNamed('/get-start');
//     } finally {
//       _isInitializing = false;
//     }
//   }

//   Future<String> _determineInitialRoute() async {
//     try {
//       if (await _authService.authenticate()) {
//         return _authService.getRoleBasedRoute();
//       }

//       if (GetStorage().read("isLoggedOut") ?? false) {
//         return '/login';
//       }

//       if (!_authService.isLoggedIn()) {
//         return '/get-start';
//       }

//       return _authService.getRoleBasedRoute();
//     } catch (e) {
//       debugPrint('Auth error: $e');
//       return '/get-start';
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     return SafeArea(
//       child: Scaffold(
//         body: Center(
//           child: Column(
//             mainAxisAlignment: MainAxisAlignment.center,
//             children: [
//               Image.asset(
//                 'assets/cmt-logo.png',
//                 width: 200,
//                 height: 180,
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }

class LaunchRouter extends StatelessWidget {
  const LaunchRouter({super.key});

  @override
  Widget build(BuildContext context) {
    // Remove splash *immediately*
    FlutterNativeSplash.remove();

    return FutureBuilder<String>(
      future: _determineInitialRoute(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.done) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            Get.offAllNamed(snapshot.data!);
          });
        }
        return Scaffold(
          // Optional: Branded loading UI
          body: Center(
            child:
                //  CircularProgressIndicator()
                Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  'assets/cmt-logo.png',
                  width: 200,
                  height: 180,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Future<String> _determineInitialRoute() async {
    final authService = AuthService();

    try {
      if (await authService.authenticate()) {
        return authService.getRoleBasedRoute();
      }

      if (GetStorage().read("isLoggedOut") ?? false) return '/login';
      if (!authService.isLoggedIn()) return '/get-start';

      return authService.getRoleBasedRoute();
    } catch (_) {
      return '/get-start';
    }
  }
}
