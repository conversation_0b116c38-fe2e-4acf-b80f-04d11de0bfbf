import 'package:flutter/material.dart';
import 'package:carousel_slider/carousel_slider.dart';

class Sponsor<PERSON>ogos extends StatefulWidget {
  final List<Map<String, dynamic>> sponsors;
  SponsorLogos({required this.sponsors});

  @override
  SponsorLogosState createState() => SponsorLogosState();
}

class SponsorLogosState extends State<SponsorLogos> {
  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize:
          MainAxisSize.min, // Ensures it doesn't take unnecessary space
      children: [
        const SizedBox(height: 10),
        Expanded(
          // Prevents overflow
          child: CarouselSlider(
            options: CarouselOptions(
              height: 150, // Adjust height if needed
              autoPlay: true,
              enlargeCenterPage: false,
              aspectRatio: 4 / 3,
              autoPlayInterval: const Duration(seconds: 2),
              autoPlayAnimationDuration: const Duration(milliseconds: 800),
              autoPlayCurve: Curves.fastOutSlowIn,
              pauseAutoPlayOnTouch: true,
            ),
            items: widget.sponsors.map((sponsor) {
              return Builder(
                builder: (BuildContext context) {
                  return Column(
                    mainAxisSize: MainAxisSize.min, // Avoid extra space
                    children: [
                      Text(
                        sponsor["typeOf"]?.toString() ?? "Unknown",
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 5),
                      Container(
                        width: double.infinity, // Full width of the screen
                        height: 60,
                        decoration: BoxDecoration(
                          color: Colors.white, // White background
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(5.0),
                          child: sponsor["logoUrl"] != null &&
                                  sponsor["logoUrl"] != ""
                              ? Image.network(
                                  sponsor["logoUrl"]?.toString() ??
                                      "", // Safe conversion
                                  fit: BoxFit.contain,
                                )
                              : const Icon(Icons.broken_image,
                                  size: 50,
                                  color: Colors
                                      .grey), // Fallback for missing images
                        ),
                      ),
                    ],
                  );
                },
              );
            }).toList(),
          ),
        ),
      ],
    );
  }
}
