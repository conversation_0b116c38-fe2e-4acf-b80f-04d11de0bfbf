import 'dart:io';

import 'package:agora_chat_sdk/agora_chat_sdk.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:cmtmeet/pages/agenda_page.dart';
import 'package:cmtmeet/pages/call_screen.dart';
import 'package:cmtmeet/pages/cmt_connect_page.dart';
import 'package:cmtmeet/pages/home_page.dart';
import 'package:cmtmeet/pages/networking_room_page.dart';
import 'package:cmtmeet/pages/notifications_screen.dart';
import 'package:cmtmeet/pages/privacy_policy_page.dart';
import 'package:cmtmeet/pages/profile_page.dart';
import 'package:cmtmeet/pages/schedule_meeting_page.dart';
import 'package:cmtmeet/service/call_service.dart';
import 'package:cmtmeet/service/firebase_service.dart';
import 'package:cmtmeet/service/websocket_service.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:in_app_update/in_app_update.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher.dart';

class UserDashboardPage extends StatefulWidget {
  const UserDashboardPage({super.key});

  @override
  State<UserDashboardPage> createState() => _UserDashboardPageState();
}

class _UserDashboardPageState extends State<UserDashboardPage> {
  final WebSocketService webSocketService = Get.find<WebSocketService>();
  final storage = GetStorage();
  final CallService _callService = CallService();
  late Map<String, dynamic> event;
  int _currentIndex = 0;

  late Map<String, dynamic> user;

  bool showCustomSnackbar = false;
  String snackbarMessage = "";

  @override
  void initState() {
    super.initState();
    user = storage.read("user") ?? {};
    event = storage.read("event") ?? {};

    // Check for initial tab argument
    final initialTab = Get.arguments?['initialTab'] as int?;
    if (initialTab != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _changeTab(initialTab);
      });
    }

    _checkNotificationPermission();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkForUpdates();
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  void _checkForUpdates() async {
    try {
      final remoteConfig = FirebaseRemoteConfig.instance;
      await remoteConfig.setDefaults({
        'force_update': false,
        'latest_version': '1.0.0',
        'whats_new': '',
      });
      await remoteConfig.fetchAndActivate();

      final forceUpdate = remoteConfig.getBool('force_update');
      final latestVersion = remoteConfig.getString('latest_version');

      final info = await PackageInfo.fromPlatform();
      final currentVersion = info.version;

      final isOutdated = currentVersion != latestVersion;

      if (forceUpdate || isOutdated) {
        _triggerInAppUpdate(remoteConfig.getString('whats_new'));
      }
    } catch (e) {
      print('Update check failed: $e');
    }
  }

  void _triggerInAppUpdate(String whatsNew) async {
    if (Platform.isAndroid) {
      try {
        final updateInfo = await InAppUpdate.checkForUpdate();

        if (updateInfo.updateAvailability ==
            UpdateAvailability.updateAvailable) {
          if (updateInfo.immediateUpdateAllowed) {
            await InAppUpdate.performImmediateUpdate();
          } else if (updateInfo.flexibleUpdateAllowed) {
            await InAppUpdate.startFlexibleUpdate();
            await InAppUpdate.completeFlexibleUpdate();
          } else {
            _showCustomUpdateDialog(whatsNew);
          }
        }
      } catch (e) {
        print('In-app update failed: $e');
      }
    } else if (Platform.isIOS) {
      _showiOSUpdateDialog(whatsNew);
    }
  }

  void _showiOSUpdateDialog(String whatsNew) {
    showDialog(
      context: context,
      builder: (_) => AlertDialog(
        title: Text('Update Available'),
        content: Text('What’s new:\n$whatsNew'),
        actions: [
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              const appStoreUrl =
                  'https://apps.apple.com/sg/app/cmt-meet/id6744887898';
              print(
                  "Url can launch from app: ${await canLaunchUrl(Uri.parse(appStoreUrl))}");
              if (await canLaunchUrl(Uri.parse(appStoreUrl))) {
                await launchUrl(Uri.parse(appStoreUrl),
                    mode: LaunchMode.externalApplication);
              }
            },
            child: Text('Update Now'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Later'),
          ),
        ],
      ),
    );
  }

  void _showCustomUpdateDialog(String whatsNew) {
    showDialog(
      context: context,
      builder: (_) => AlertDialog(
        title: Text('Update Available'),
        content: Text('What’s new:\n$whatsNew'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              InAppUpdate.performImmediateUpdate(); // or manual fallback
            },
            child: Text('Update Now'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Later'),
          ),
        ],
      ),
    );
  }

  void _checkNotificationPermission() async {
    final settings = await FirebaseMessaging.instance.getNotificationSettings();

    if (settings.authorizationStatus != AuthorizationStatus.authorized) {
      setState(() {
        showCustomSnackbar = true;
        snackbarMessage =
            "Notifications are disabled. Please enable them in Settings.";
      });
    }
  }

  void _changeTab(int index) {
    final pages = _filteredPages();
    if (index < pages.length) {
      setState(() {
        _currentIndex = index;
      });
    }
  }

  // Add these methods to your _UserDashboardPageState class
  List<Widget> _filteredPages() {
    final initialArgs = Get.arguments ?? {};
    return [
      HomePage(onTabChange: _changeTab), // Always show Home
      AgendaPage(),
      // if (event['cmtconnect'] ?? false) CmtConnectPage(),
      if (event['cmtconnect'] ?? false)
        CmtConnectPage(
          initialSection: initialArgs['connectSection'] ?? 'Online',
          onTabChange: _changeTab,
        ),
      if (event['networking'] ?? false) NetworkingRoomPage(),
      if (event['cmtconnect'] ?? false) ScheduleMeetingPage(),
      // if (event['watchLive'] ?? false)
      // Center(child: Text('Watch Live', style: TextStyle(fontSize: 20))),
    ];
  }

  List<Widget> _filteredPageNames() {
    return [
      Text('Home', style: TextStyle(fontSize: 18)),
      Text('Agenda', style: TextStyle(fontSize: 18)),
      if (event['cmtconnect'] ?? false)
        Text('CMT Connect', style: TextStyle(fontSize: 18)),
      if (event['networking'] ?? false)
        Text('Networking Room', style: TextStyle(fontSize: 18)),
      if (event['cmtconnect'] ?? false)
        Text('Meeting Schedule', style: TextStyle(fontSize: 18)),
      // if (event['watchLive'] ?? false) Text('Watch Live', style: TextStyle(fontSize: 18)),
    ];
  }

  List<BottomNavigationBarItem> _filteredNavItems() {
    return [
      BottomNavigationBarItem(
        icon: Icon(Icons.home),
        label: 'Home',
      ),
      BottomNavigationBarItem(
        icon: Icon(Icons.calendar_month),
        label: 'Agenda',
      ),
      if (event['cmtconnect'] ?? false)
        BottomNavigationBarItem(
          icon: Icon(Icons.handshake_outlined),
          label: 'Connect',
        ),
      if (event['networking'] ?? false)
        BottomNavigationBarItem(
          icon: Icon(Icons.diversity_3_sharp),
          label: 'Networking',
        ),
      if (event['cmtconnect'] ?? false)
        BottomNavigationBarItem(
          icon: Icon(Icons.event_note_sharp),
          label: 'Meeting',
        ),
      // if (event['watchLive'] ?? false)
      //   BottomNavigationBarItem(
      //     icon: Icon(Icons.podcasts_sharp),
      //     label: 'Watch Live',
      //   ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    final pages = _filteredPages();
    final pageNames = _filteredPageNames();
    final navItems = _filteredNavItems();
    // final height = MediaQuery.of(context).size.height;
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          if (Platform.isAndroid) {
            SystemNavigator.pop();
          } else {
            exit(0);
          }
        }
      },
      child: SafeArea(
        child: Scaffold(
          appBar: _ReactiveCallAppBar(
            userBusyStatus: userBusyStatus,
            callService: _callService,
            pageNames: pageNames,
            currentIndex: _currentIndex,
            showCustomSnackbar: showCustomSnackbar,
            showLogoutDialog: _showLogoutDialog,
          ),
          // ValueListenableBuilder<bool>(
          // valueListenable: userBusyStatus,
          // builder: (_, isBusy, __) => PreferredSize(
          //   preferredSize: Size.fromHeight(kToolbarHeight + (isBusy ? 50 : 0)),
          //   child: Column(
          //     mainAxisSize: MainAxisSize.min,
          //     children: [
          //       if (isBusy)
          //         GestureDetector(
          //           onTap: () {
          //             Get.to(() => CallScreen(
          //                   calleeAgoraId: _callService.calleeAgoraId,
          //                   calleeName: _callService.calleeName,
          //                   calleeJob: _callService.calleeJob,
          //                   calleeCompany: _callService.calleeCompany,
          //                   fcmToken: _callService.fcmToken,
          //                   jwtToken: _callService.jwtToken,
          //                   channelName: _callService.channelName,
          //                   token: '', // Add rtcToken if needed
          //                   callUUID: _callService.callUUID,
          //                   status: 'ongoing',
          //                   callType: _callService.callType,
          //                   callInitiator: _callService.callInitiator,
          //                   fromColdStart: false,
          //                 ));
          //           },
          //           child: Container(
          //             height: 50,
          //             width: double.infinity,
          //             color: Colors.green,
          //             alignment: Alignment.center,
          //             child: CallDurationIndicator(callService: CallService()),
          //                     // Text(
          //                     //   'Call in progress - tap to return',
          //                     //   style: TextStyle(color: Colors.white, fontSize: 12),
          //                     // ),
          //                   ),
          //                 ),
          //               AppBar(
          //                 foregroundColor: Colors.white,
          //                 backgroundColor: Color(0xffc01058),
          //                 title: pageNames.isNotEmpty && _currentIndex < pageNames.length
          //                     ? pageNames[_currentIndex]
          //                     : Text('Home',
          //                         style: TextStyle(fontSize: 18)), //_pagesName[_currentIndex],
          //                 actions: [
          //                   Container(
          //                     padding: EdgeInsets.all(10.0),
          //                     child: Row(
          //                       children: [
          //                         IconButton(
          //                             icon: Icon(showCustomSnackbar
          //                                 ? Icons.notifications_off_sharp
          //                                 : Icons.notifications_sharp),
          //                             onPressed: () {
          //                               // _callService.endCall();
          //                               // print(
          //                               //     "${_callService.acceptedCall} && ${!_callService.isCallEnded} && ${!_callService.isCallDeclined}");
          //                               print(userBusyStatus);
          //                               showCustomSnackbar
          //                                   ? _showSettingsDialog()
          //                                   : Get.to(() => NotificationsScreen());
          //                             }),
          //                         PopupMenuButton<String>(
          //                           offset: Offset(10, 50),
          //                           splashRadius: 0,
          //                           onSelected: (value) {
          //                             if (value == 'Profile') {
          //                               Get.to(() => ProfilePage());
          //                             } else if (value == 'Logout') {
          //                               _showLogoutDialog();
          //                             }
          //                           },
          //                           itemBuilder: (context) => [
          //                             PopupMenuItem(
          //                               value: 'Profile',
          //                               child: Row(
          //                                 children: [
          //                                   Icon(Icons.person, color: Colors.black54),
          //                                   SizedBox(width: 10),
          //                                   Text('Profile'),
          //                                 ],
          //                               ),
          //                             ),
          //                             PopupMenuItem(
          //                               value: 'Logout',
          //                               child: Row(
          //                                 children: [
          //                                   Icon(Icons.logout, color: Colors.black54),
          //                                   SizedBox(width: 10),
          //                                   Text('Logout'),
          //                                 ],
          //                               ),
          //                             ),
          //                           ],
          //                           child: Container(
          //                             height: 32,
          //                             width: 50,
          //                             decoration: BoxDecoration(
          //                               borderRadius: BorderRadius.circular(16),
          //                             ),
          //                             child: Row(
          //                               children: [
          //                                 Icon(Icons.account_circle),
          //                                 Icon(Icons.arrow_drop_down)
          //                               ],
          //                             ),
          //                           ),
          //                         ),
          //                       ],
          //                     ),
          //                   ),
          //                 ],
          //               ),
          //             ],
          //           ),
          //         ),),
          drawer: Drawer(
            child: Stack(
              children: [
                ListView(
                  padding: EdgeInsets.zero,
                  children: [
                    Container(
                      padding:
                          EdgeInsets.symmetric(vertical: 30, horizontal: 20),
                      color: Color.fromARGB(255, 214, 214, 214),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          user['profileimg'] != ''
                              ? CachedNetworkImage(
                                  imageUrl: user['profileimg'],
                                  placeholder: (context, url) => Container(
                                    width: 90,
                                    height: 90,
                                    child: Center(
                                      child: SizedBox(
                                        width: 20,
                                        height: 20,
                                        child: CircularProgressIndicator(),
                                      ),
                                    ),
                                  ),
                                  errorWidget: (context, url, error) =>
                                      Icon(Icons.error),
                                  imageBuilder: (context, imageProvider) =>
                                      CircleAvatar(
                                    radius: 40,
                                    backgroundImage: imageProvider,
                                  ),
                                )
                              : Icon(
                                  Icons.account_circle_sharp,
                                  size: 90,
                                  color: Color(0xffbbbbbb),
                                ),
                          Text(
                            user["fullname"] ?? '',
                            style: TextStyle(
                                fontSize: 15,
                                fontWeight: FontWeight.bold,
                                color: Color(0xffc01058)),
                          ),
                          Text(
                            user["jobtitle"] ?? '',
                            style: TextStyle(fontSize: 12),
                          ),
                          Text(
                            user["companyname"] ?? '',
                            style: TextStyle(fontSize: 12),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 15),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10),
                      child: ListTile(
                        leading: Icon(Icons.person),
                        title: Text('Update Profile'),
                        onTap: () {
                          Get.to(() => ProfilePage());
                        },
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10),
                      child: ListTile(
                        // minTileHeight: 18,
                        leading: Icon(Icons.privacy_tip),
                        title: Text('Privacy Policy'),
                        onTap: () {
                          Get.to(() => PrivacyPolicyPage());
                        },
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10),
                      child: ListTile(
                        // minTileHeight: 15,
                        leading: Icon(Icons.info),
                        title: Text('Version 1.0.12'),
                        // onTap: _showLogoutDialog,
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10),
                      child: ListTile(
                        // minTileHeight: 15,
                        leading: Icon(Icons.logout),
                        title: Text('Logout'),
                        onTap: _showLogoutDialog,
                      ),
                    ),
                  ],
                ),
                // Close button positioned in the top-right corner
                Positioned(
                  top: 12,
                  right: 12,
                  child: IconButton(
                    icon: Icon(
                      Icons.close,
                      color: Colors.grey,
                      size: 24,
                    ),
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                  ),
                ),
              ],
            ),
          ),
          body: pages.isNotEmpty && _currentIndex < pages.length
              ? pages[_currentIndex]
              : HomePage(onTabChange: _changeTab),
          bottomNavigationBar: BottomNavigationBar(
            unselectedItemColor: Colors.white,
            selectedItemColor: Colors.grey[200],
            backgroundColor: Color(0xffc01058),
            currentIndex: _currentIndex < navItems.length ? _currentIndex : 0,
            onTap: (index) {
              if (index < pages.length) {
                _changeTab(index);
              }
            },
            type: BottomNavigationBarType.fixed,
            selectedFontSize: 13,
            unselectedFontSize: 10,
            items: navItems,
          ),
        ),
      ),
    );
  }

  void _showLogoutDialog() {
    Get.dialog(
      AlertDialog(
        title: Text('Logout'),
        content: Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              await _performLogout();
              Get.offAllNamed('/login');
            },
            child: Text('Logout'),
          ),
        ],
      ),
    );
  }

  Future<void> _performLogout() async {
    try {
      final webSocketService = Get.find<WebSocketService>();
      webSocketService.closeConnection();
      await ChatClient.getInstance.logout(true);
      final storage = GetStorage();
      await storage.erase();
      await storage.write("isLoggedOut", true);
      Get.offAllNamed('/login');
    } catch (e) {
      debugPrint('Error during logout: $e');
    }
  }
}

class _ReactiveCallAppBar extends StatelessWidget
    implements PreferredSizeWidget {
  final ValueNotifier<bool> userBusyStatus;
  final CallService callService;
  final List pageNames;
  final int currentIndex;
  final bool showCustomSnackbar;
  final VoidCallback showLogoutDialog;

  const _ReactiveCallAppBar(
      {required this.userBusyStatus,
      required this.callService,
      required this.pageNames,
      required this.currentIndex,
      required this.showCustomSnackbar,
      required this.showLogoutDialog});

  @override
  Size get preferredSize =>
      Size.fromHeight(kToolbarHeight + (userBusyStatus.value ? 50 : 0));

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<bool>(
      valueListenable: userBusyStatus,
      builder: (_, isBusy, __) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          AnimatedContainer(
            duration: Duration(milliseconds: 300),
            height: userBusyStatus.value ? 50 : 0,
            width: double.infinity,
            color: Colors.green,
            curve: Curves.easeInOut,
            child: userBusyStatus.value
                ? GestureDetector(
                    onTap: () {
                      Get.to(() => CallScreen(
                            calleeAgoraId: callService.calleeAgoraId,
                            calleeName: callService.calleeName,
                            calleeJob: callService.calleeJob,
                            calleeCompany: callService.calleeCompany,
                            fcmToken: callService.fcmToken,
                            jwtToken: callService.jwtToken,
                            channelName: callService.channelName,
                            token: '',
                            callUUID: callService.callUUID,
                            status: 'ongoing',
                            callType: callService.callType,
                            callInitiator: callService.callInitiator,
                            fromColdStart: false,
                          ));
                    },
                    child: Center(
                      child: CallDurationIndicator(callService: callService),
                    ),
                  )
                : null,
          ),
          AppBar(
            foregroundColor: Colors.white,
            backgroundColor: Color(0xffc01058),
            title: pageNames.isNotEmpty && currentIndex < pageNames.length
                ? pageNames[currentIndex]
                : Text('Home', style: TextStyle(fontSize: 18)),
            actions: [
              Container(
                padding: EdgeInsets.all(10.0),
                child: Row(
                  children: [
                    IconButton(
                        icon: Icon(showCustomSnackbar
                            ? Icons.notifications_off_sharp
                            : Icons.notifications_sharp),
                        onPressed: () {
                          // _callService.endCall();
                          // print(
                          //     "${_callService.acceptedCall} && ${!_callService.isCallEnded} && ${!_callService.isCallDeclined}");
                          print(userBusyStatus.value);
                          showCustomSnackbar
                              ? _showSettingsDialog()
                              : Get.to(() => NotificationsScreen());
                        }),
                    PopupMenuButton<String>(
                      offset: Offset(10, 50),
                      splashRadius: 0,
                      onSelected: (value) {
                        if (value == 'Profile') {
                          Get.to(() => ProfilePage());
                        } else if (value == 'Logout') {
                          showLogoutDialog();
                        }
                      },
                      itemBuilder: (context) => [
                        PopupMenuItem(
                          value: 'Profile',
                          child: Row(
                            children: [
                              Icon(Icons.person, color: Colors.black54),
                              SizedBox(width: 10),
                              Text('Profile'),
                            ],
                          ),
                        ),
                        PopupMenuItem(
                          value: 'Logout',
                          child: Row(
                            children: [
                              Icon(Icons.logout, color: Colors.black54),
                              SizedBox(width: 10),
                              Text('Logout'),
                            ],
                          ),
                        ),
                      ],
                      child: Container(
                        height: 32,
                        width: 50,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.account_circle),
                            Icon(Icons.arrow_drop_down)
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _showSettingsDialog() async {
    await showDialog(
      context: Get.context!,
      builder: (context) => AlertDialog(
        title: const Text("Notifications"),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (Platform.isIOS)
              Column(
                children: [
                  Image.asset('assets/notification_animate.gif', height: 350),
                  SizedBox(height: 16),
                ],
              ),
            const Text(
              "You have disabled notifications, to get message and call notifications. Please enable in Settings.",
              textAlign: TextAlign.left,
            ),
          ],
        ),
        actions: [
          TextButton(
            child: const Text("Cancel"),
            onPressed: () => Navigator.pop(context),
          ),
          TextButton(
            child: const Text("Open Settings"),
            onPressed: () {
              Navigator.pop(context);
              openAppSettings();
            },
          ),
        ],
      ),
    );
  }
}
