import 'package:agora_chat_sdk/agora_chat_sdk.dart';
import 'package:cmtmeet/service/connection_service.dart';
import 'package:cmtmeet/service/token_manager.dart';
import 'package:get_storage/get_storage.dart';

class AuthService {
  final _storage = GetStorage();
  final _connectionService = ConnectionService();

  Future<bool> authenticate() async {
    if (!TokenManager.isTokenValid()) {
      return await _refreshTokenAndConnect();
    }
    return await _verifyAndRestoreConnections();
  }

  Future<bool> _refreshTokenAndConnect() async {
    try {
      await TokenManager.refreshToken();
      return await _verifyAndRestoreConnections();
    } catch (e) {
      await _clearAuthState();
      return false;
    }
  }

  Future<bool> _verifyAndRestoreConnections() async {
    final user = _storage.read("user");
    if (user == null) return false;

    try {
      if (!await _isChatConnected()) {
        await _connectToChat(user["agoraid"], _storage.read("chatToken"));
      }

      await _connectionService.restoreConnections();

      return true;
    } catch (e) {
      await _clearAuthState();
      return false;
    }
  }

  Future<bool> _isChatConnected() async {
    return await ChatClient.getInstance.isConnected() &&
        await ChatClient.getInstance.isLoginBefore();
  }

  Future<void> _connectToChat(String agoraId, String token) async {
    if (await ChatClient.getInstance.isLoginBefore()) {
      await ChatClient.getInstance.logout(true);
    }
    await ChatClient.getInstance.loginWithToken(agoraId, token);
  }

  Future<void> _clearAuthState() async {
    await _storage.remove("chatToken");
    await _storage.remove("rtmToken");
    await _storage.remove("tokenExpiration");
    await ChatClient.getInstance.logout(true);
    await _connectionService.resetAllConnections();
  }

  bool isLoggedIn() {
    return _storage.read("user") != null;
  }

  String getRoleBasedRoute() {
    final user = _storage.read("user");
    return user?["role"] == "admin" ? '/admin/dashboard' : '/user/dashboard';
  }
}
