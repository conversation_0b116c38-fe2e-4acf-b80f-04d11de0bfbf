import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:agora_chat_sdk/agora_chat_sdk.dart';
import 'package:agora_rtc_engine/agora_rtc_engine.dart';
import 'package:cmtmeet/service/caller_tone_service.dart';
import 'package:cmtmeet/service/callkit_manager.dart';
import 'package:cmtmeet/service/firebase_service.dart';
import 'package:cmtmeet/utils/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:permission_handler/permission_handler.dart';

class CallService {
  // Singleton instance
  static final CallService _instance = CallService._internal();
  factory CallService() => _instance;
  CallService._internal();

  // Agora engine instance
  RtcEngine? engine;
  bool _isInitialized = false;
  bool joinedCall = false;
  bool acceptedCall = false;
  bool isCallEnded = false;
  bool isCallDeclined = false;
  bool isCallDeclinedByCallee = false;
  bool _isDisposing = false;
  bool _isRinging = false;

  // Call state variables
  int? remoteUid;
  bool isRemoteVideoEnabled = true;
  bool isRemoteAudioEnabled = true;
  int callDurationInSeconds = 0;
  Timer? _callTimer;

  // Add a stream controller for call duration updates
  final StreamController<int> _callDurationController =
      StreamController<int>.broadcast();
  Stream<int> get callDurationStream => _callDurationController.stream;

  // Device settings
  bool isVideoOn = false;
  final ValueNotifier<bool> isSpeakerOn = ValueNotifier(false);
  final ValueNotifier<bool> isMicOn = ValueNotifier(true);
  // bool isSpeakerOn = false;
  // bool isMicOn = true;
  bool isFrontCam = true;

  // Callback functions
  Function()? onUserJoined;
  Function(bool isMuted)? onRemoteAudioMuted;
  Function(bool isMuted)? onRemoteVideoMuted;
  Function(int duration)? onCallDurationUpdate;
  Function()? onUserOffline;
  Function()? onEndCall;
  Function()? onBusyStatusReceived;
  Function()? onDeclineStatusReceived;

  // Call data
  late String callUUID;
  late String calleeAgoraId;
  late String calleeName;
  late String calleeJob;
  late String calleeCompany;
  late String callType;
  late String channelName;
  late String fcmToken;
  late String jwtToken;
  late bool callInitiator;
  bool isBusy = false;

  bool callUIReady = false;

  void assignCallData({
    required String callUUID,
    required String calleeAgoraId,
    required String calleeName,
    required String calleeJob,
    required String calleeCompany,
    required String callType,
    required String channelName,
    required String fcmToken,
    required String jwtToken,
    required bool callInitiator,
  }) {
    this.callUUID = callUUID;
    this.calleeAgoraId = calleeAgoraId;
    this.calleeName = calleeName;
    this.calleeJob = calleeJob;
    this.calleeCompany = calleeCompany;
    this.callType = callType;
    this.channelName = channelName;
    this.fcmToken = fcmToken;
    this.jwtToken = jwtToken;
    this.callInitiator = callInitiator;
  }

  Future<void> initializeAgora() async {
    if (_isInitialized) return;

    final granted = await _requestPermissions(callType == 'video');
    if (!granted) {
      throw Exception("Permissions not granted");
    }

    try {
      engine = createAgoraRtcEngine();
      await engine!.initialize(RtcEngineContext(appId: Constants.appId));

      // Register event handlers
      engine!.registerEventHandler(
        RtcEngineEventHandler(
          onJoinChannelSuccess: (RtcConnection connection, int elapsed) async {
            if (callType == 'audio') engine!.setEnableSpeakerphone(false);
            this.joinedCall = true;
            if (Platform.isAndroid) {
              print('🟢 Starting native service');
              const callPlatform =
                  MethodChannel('com.cmtevents.cmtmeet/call_service');
              await callPlatform.invokeMethod('startService'); // moved here ✅
            }
          },
          onUserJoined: (RtcConnection connection, int remoteUid, int elapsed) {
            this.remoteUid = remoteUid;
            this.acceptedCall = true;
            startCallTimer();
            stopRinging();
            onUserJoined?.call();
          },
          onUserMuteAudio: (connection, remoteUid, muted) {
            this.isRemoteAudioEnabled = !muted;
            onRemoteAudioMuted?.call(muted);
          },
          onUserMuteVideo: (connection, remoteUid, muted) {
            this.isRemoteVideoEnabled = !muted;
            onRemoteVideoMuted?.call(muted);
          },
          onUserOffline: (connection, remoteUid, reason) {
            this.remoteUid = 0;
            if (onUserOffline == null) {
              endCall();
            } else {
              onUserOffline?.call();
            }
          },
          onError: (ErrorCodeType err, String msg) {
            print("Agora Error: $msg and code: $err");
          },
        ),
      );

      // Enable audio for both call types
      await engine!.enableAudio();

      // Enable video only if it's a video call
      if (callType == 'video') {
        isVideoOn = true;
        isSpeakerOn.value = true;
        await engine!.enableVideo();
        await engine!.startPreview();
      }

      _isInitialized = true;
    } catch (e) {
      print("Failed to initialize Agora: $e");
      rethrow;
    }
  }

  Future<void> joinChannel({
    required String token,
    required String channelName,
    required bool isVideoCall,
  }) async {
    if (!_isInitialized) {
      throw Exception("Agora not initialized");
    }

    await engine!.joinChannel(
      token: token,
      channelId: channelName,
      uid: 0,
      options: ChannelMediaOptions(
        channelProfile: ChannelProfileType.channelProfileCommunication,
        clientRoleType: ClientRoleType.clientRoleBroadcaster,
        publishCameraTrack: isVideoCall && isVideoOn,
        publishMicrophoneTrack: isMicOn.value,
        autoSubscribeAudio: true,
        autoSubscribeVideo: true,
      ),
    );
  }

  void markCallUIReady() {
    callUIReady = true;
    print("Call UI Ready");
  }

  Future<void> startRinging() async {
    if (_isRinging) return;
    _isRinging = true;
    print("Start Ringing");

    try {
      await NativeCallerTone.switchOutput(false);
      await NativeCallerTone.start();
    } catch (e) {
      print('Error starting ringtone: $e');
      _isRinging = false;
      rethrow;
    }
  }

  Future<void> stopRinging() async {
    if (!_isRinging) return;
    _isRinging = false;
    print("Stop Ringing");

    try {
      await NativeCallerTone.stop();
    } catch (e) {
      print('Error stopping ringtone: $e');
      rethrow;
    }
  }

  void toggleMute() {
    isMicOn.value = !isMicOn.value;
    engine?.muteLocalAudioStream(!isMicOn.value);
  }

  void toggleVideo() {
    isVideoOn = !isVideoOn;
    engine?.muteLocalVideoStream(!isVideoOn);
  }

  void toggleSpeaker() async {
    isSpeakerOn.value = !isSpeakerOn.value;
    if (joinedCall) {
      await engine?.setEnableSpeakerphone(isSpeakerOn.value);
    } else {
      print("Turn speaker on/off in Ringing");
      await NativeCallerTone.switchOutput(!isSpeakerOn.value);
    }
  }

  void switchCamera() {
    isFrontCam = !isFrontCam;
    engine?.switchCamera();
  }

  Future<void> declineCall() async {
    if (isCallDeclined) return;
    isCallDeclined = true;
    stopRinging();
    try {
      if (Platform.isIOS) {
        await CallManager().endCall(callUUID);
      }
      sendDecline(calleeName, callUUID, channelName, fcmToken, jwtToken);
      if (callInitiator) {
        sendCallStatus(calleeAgoraId, callType, 'call', '0');
      }
      if (joinedCall) {
        await engine!.leaveChannel();
      }
      _cleanUp();
    } catch (e) {
      print('Decline call error: $e');
    }
  }

  Future<void> endCall() async {
    if (isCallEnded) {
      print('[📞 endCall] Call already ended. Skipping.');
      return;
    }

    isCallEnded = true;
    print('[📞 endCall] 🔚 Marking call as ended');

    if (onEndCall != null) {
      onEndCall?.call();
    }

    try {
      if (Platform.isIOS) {
        print('[📞 endCall] 🍎 Ending iOS call with UUID: $callUUID');
        await CallManager().endCall(callUUID);
      }

      if (acceptedCall && callInitiator) {
        print('[📞 endCall] ⏱ Call not accepted. Sending status.');
        final callTimer = callDurationInSeconds;
        print('[📞 endCall] ⏱ Call duration: $callTimer seconds');
        sendCallStatus(calleeAgoraId, callType, 'call', '$callTimer');
      }

      stopCallTimer();
      print('[📞 endCall] 🛑 Call timer stopped');

      if (joinedCall) {
        print('[📞 endCall] 🚪 Leaving Agora channel');
        await engine?.leaveChannel();
      }

      print('[📞 endCall] 🧹 Running cleanup');
      await _cleanUp();

      print('[📞 endCall] ✅ Call ended cleanly');
    } catch (e) {
      print('[📞 endCall] ❌ Error while ending call: $e');
    }
  }

  Future<void> handleBusyStatus() async {
    if (isBusy) return;
    isBusy = true;
    stopRinging();
    if (onBusyStatusReceived != null) {
      onBusyStatusReceived?.call();
    }
    try {
      if (Platform.isIOS) {
        await CallManager().endCall(callUUID);
      }
      if (callInitiator) {
        sendCallStatus(calleeAgoraId, callType, 'missed_call', '0');
      }
      if (joinedCall) {
        await engine!.leaveChannel();
      }
      _cleanUp();
    } catch (e) {
      print('End call error: $e');
    }
  }

  Future<void> handleDeclineStatus() async {
    if (isCallDeclinedByCallee) return;
    isCallDeclinedByCallee = true;
    stopRinging();
    onDeclineStatusReceived?.call();
    try {
      if (Platform.isIOS) {
        await CallManager().endCall(callUUID);
      }
      if (callInitiator) {
        sendCallStatus(calleeAgoraId, callType, 'call', '0');
      }
      if (joinedCall) {
        await engine!.leaveChannel();
      }
      _cleanUp();
    } catch (e) {
      print('Decline call from callee error: $e');
    }
  }

  Future<void> _cleanUp() async {
    if (_isDisposing) return;
    _isDisposing = true;
    print('[🧹 _cleanUp] Starting cleanup');

    try {
      print('[🧹 _cleanUp] setUserBusyStatus(false)');
      userBusyStatus.value = false;
      // setUserBusyStatus(false);

      print('[🧹 _cleanUp] Resetting callbacks to null');
      onUserJoined = null;
      onRemoteAudioMuted = null;
      onRemoteVideoMuted = null;
      onCallDurationUpdate = null;
      onUserOffline = null;
      onBusyStatusReceived = null;
      onDeclineStatusReceived = null;

      print('[🧹 _cleanUp] Resetting internal flags');
      _isInitialized = false;
      _isRinging = false;
      joinedCall = false;
      acceptedCall = false;
      isCallEnded = false;
      isCallDeclined = false;
      isCallDeclinedByCallee = false;
      remoteUid = null;
      isBusy = false;
      isSpeakerOn.value = false;
      isMicOn.value = true;

      print('[🧹 _cleanUp] Disposing Agora engine');
      await engine?.release();

      if (Platform.isAndroid) {
        print('[🧹 _cleanUp] Resetting native busy state');
        const cleanupPlatform = MethodChannel('com.cmtevents.cmtmeet/cleanup');
        await cleanupPlatform.invokeMethod('resetAppBusy', {'value': false});

        print('[🧹 _cleanUp] 🧯 Stopping native service');
        const callPlatform =
            MethodChannel('com.cmtevents.cmtmeet/call_service');
        await callPlatform.invokeMethod('stopService'); // moved here ✅
      }
    } catch (e) {
      print('[🧹 _cleanUp] ❌ Error: $e');
    } finally {
      _isDisposing = false;
      print('[🧹 _cleanUp] Done');
    }
  }

  // Modify startCallTimer to use the stream
  void startCallTimer() {
    _callTimer = Timer.periodic(Duration(seconds: 1), (timer) {
      callDurationInSeconds++;
      _callDurationController.add(callDurationInSeconds);
      onCallDurationUpdate?.call(callDurationInSeconds);
    });
  }

  void stopCallTimer() {
    _callTimer?.cancel();
    _callTimer = null;
    callDurationInSeconds = 0;
  }

  Future<void> sendCallStatus(String chatAgoraId, String callType,
      String status, String seconds) async {
    try {
      var message = ChatMessage.createCustomSendMessage(
        targetId: chatAgoraId,
        event: "calls",
        params: {
          "callType": callType,
          "status": status,
          "seconds": seconds,
        },
      );

      // Send message to server
      await ChatClient.getInstance.chatManager.sendMessage(message);
    } catch (e) {
      print('Error sending missed call: $e');
    }
  }

  Future<bool> _requestPermissions(bool isVideoCall) async {
    final micStatus = await Permission.microphone.request();
    PermissionStatus? cameraStatus;

    if (isVideoCall) {
      cameraStatus = await Permission.camera.request();
    }

    if (micStatus != PermissionStatus.granted ||
        (isVideoCall && cameraStatus != PermissionStatus.granted)) {
      if (micStatus.isPermanentlyDenied ||
          (isVideoCall && cameraStatus!.isPermanentlyDenied)) {
        openAppSettings();
      }
      return false;
    }
    return true;
  }

  Future<void> sendDecline(String callerName, String callUUID,
      String channelName, String fcmToken, String jwtToken) async {
    try {
      final receiverFCMToken = fcmToken;

      // Prepare FCM message payload for call decline
      final messageData = {
        "message": {
          "token": receiverFCMToken,
          "data": {
            "callerName": callerName,
            "callUUID": callUUID,
            "channelName": channelName,
            "status": "DECLINED",
            "type": "CALL_RESPONSE",
            "click_action": "FLUTTER_NOTIFICATION_CLICK",
          },
          "android": {
            "priority": "high",
            // "notification": {"sound": "default", "channel_id": "calls_channel"}
          },
          "apns": {
            "headers": {"apns-priority": "10"},
            "payload": {
              "aps": {"sound": "default", "badge": 1, "content-available": 1},
              "voip": "1"
            }
          }
        }
      };

      // Send HTTP request to FCM API
      final response = await http.post(
        Uri.parse(Constants.FCM_API_URL),
        headers: {
          "Authorization": "Bearer $jwtToken",
          "Content-Type": "application/json"
        },
        body: jsonEncode(messageData),
      );

      if (response.statusCode != 200) {
        final errorData = jsonDecode(response.body);
        throw Exception("FCM Decline Error: ${errorData.toString()}");
      }
    } catch (e) {
      print("Failed to send call decline status: $e");
      // Optionally show error to user
      Get.snackbar(
        "Error",
        "Failed to send decline notification",
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }
}

class CallDurationIndicator extends StatelessWidget {
  final CallService callService;

  const CallDurationIndicator({required this.callService});

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<int>(
      stream: callService.callDurationStream,
      initialData: callService.callDurationInSeconds,
      builder: (context, snapshot) {
        return Text(
          _formatDuration(snapshot.data ?? 0),
          style: TextStyle(color: Colors.white),
        );
      },
    );
  }

  String _formatDuration(int seconds) {
    final minutes = (seconds ~/ 60).toString().padLeft(2, '0');
    final remainingSeconds = (seconds % 60).toString().padLeft(2, '0');
    return '$minutes:$remainingSeconds';
  }
}

// import 'package:flutter/services.dart';

// const platform = MethodChannel('com.cmtevents.cmtmeet/call_service');

// Future<void> startCallForegroundService() async {
//   try {
//     await platform.invokeMethod('startCallService');
//   } catch (e) {
//     print("Failed to start call service: $e");
//   }
// }

// Future<void> stopCallForegroundService() async {
//   try {
//     await platform.invokeMethod('stopCallService');
//   } catch (e) {
//     print("Failed to stop call service: $e");
//   }
// }
