import 'package:flutter/services.dart';

class NativeCallerTone {
  static const _channel = MethodChannel('com.cmtevents.cmtmeet/caller_tone');

  static Future<void> start() async {
    await _channel.invokeMethod('startRinging');
  }

  static Future<void> stop() async {
    await _channel.invokeMethod('stopRinging');
  }

  static Future<void> switchOutput(bool toSpeaker) async {
    await _channel.invokeMethod('switchOutput', {'speaker': toSpeaker});
  }
}
