import 'dart:io';

import 'package:flutter_callkit_incoming/entities/call_kit_params.dart';
import 'package:flutter_callkit_incoming/entities/ios_params.dart';
import 'package:flutter_callkit_incoming/flutter_callkit_incoming.dart';

class CallManager {
  static final CallManager _instance = CallManager._internal();
  factory CallManager() => _instance;
  CallManager._internal();

  late String? _currentCallId;

  // iOS-only CallKit for incoming calls
  Future<void> showIncomingCallIOS({
    required String callerAgoraId,
    required String callerName,
    required String callerJob,
    required String callerCompany,
    required String fcmToken,
    required String channelName,
    required String token,
    required String callType,
    required String callUUID,
    required String jwtToken,
  }) async {
    if (!Platform.isIOS) return; // Exit on Android

    final params = CallKitParams(
      id: callUUID,
      nameCaller: callerName,
      handle: callerAgoraId,
      type: callType == 'video' ? 1 : 0,
      duration: 45000,
      textAccept: 'Accept',
      textDecline: 'Decline',
      extra: {
        'callerAgoraId': callerAgoraId,
        'callerName': callerName,
        'callerJob': callerJob,
        'callerCompany': callerCompany,
        'fcmToken': fcmToken,
        'channelName': channelName,
        'token': token,
        'callType': callType,
        'callUUID': callUUID,
        'jwtToken': jwtToken,
      },
      ios: IOSParams(
        handleType: 'generic',
        supportsVideo: callType == 'video',
        maximumCallGroups: 2,
        audioSessionMode: 'voiceChat',
        ringtonePath: 'Ringtone.caf',
      ),
    );

    _currentCallId = callUUID;
    await FlutterCallkitIncoming.setCallConnected(callUUID);
    await FlutterCallkitIncoming.showCallkitIncoming(params);
    print(_currentCallId);
  }

  // iOS-only CallKit for outgoing calls
  Future<void> startOutgoingCallIOS({
    required String callerAgoraId,
    required String callerName,
    required String channelName,
    required String token,
    required String callType,
    required String callUUID,
  }) async {
    if (!Platform.isIOS) return;

    final params = CallKitParams(
      id: callUUID,
      nameCaller: callerName,
      handle: callerAgoraId,
      type: callType == 'video' ? 1 : 0,
      extra: {
        'channelName': channelName,
        'token': token,
        'callerAgoraId': callerAgoraId,
        'callType': callType,
      },
      ios: IOSParams(
        handleType: 'generic',
        supportsVideo: callType == 'video',
      ),
    );

    _currentCallId = callUUID;
    await FlutterCallkitIncoming.startCall(params);
    await FlutterCallkitIncoming.setCallConnected(callUUID);
  }

  Future<void> endCall(callUUID) async {
    if (callUUID != null) {
      await FlutterCallkitIncoming.endCall(callUUID!);
      _currentCallId = null;
    }
  }

  Future<void> endAllCalls() async {
    await FlutterCallkitIncoming.endAllCalls();
    _currentCallId = null;
  }
}
