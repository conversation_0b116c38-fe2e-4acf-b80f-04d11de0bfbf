import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:cmtmeet/service/websocket_service.dart';
import 'package:cmtmeet/utils/constants.dart';

class ConnectionService {
  final _storage = GetStorage();
  final _webSocketService = Get.isRegistered<WebSocketService>()
      ? Get.find<WebSocketService>()
      : Get.put<WebSocketService>(WebSocketService());

  bool get _hasValidSession {
    final user = _storage.read("user");
    final event = _storage.read("event");
    return user != null && event != null;
  }

  Future<void> restoreConnections() async {
    if (_hasValidSession) {
      await _initializeWebSocket();
    }
  }

  Future<void> _initializeWebSocket() async {
    _webSocketService.initializeConnection("wss://${Constants.url}");
    return;
  }

  Future<void> resetAllConnections() async {
    _webSocketService.closeConnection();
    return;
  }

  Future<void> handleAppResume() async {
    if (_hasValidSession && _webSocketService.isConnectionClosed()) {
      await _initializeWebSocket();
    }
  }
}
