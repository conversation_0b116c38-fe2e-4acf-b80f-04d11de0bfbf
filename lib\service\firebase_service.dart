import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:agora_chat_sdk/agora_chat_sdk.dart';
import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:cmtmeet/pages/call_screen.dart';
import 'package:cmtmeet/service/call_service.dart';
import 'package:cmtmeet/service/callkit_manager.dart';
import 'package:cmtmeet/utils/constants.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_callkit_incoming/entities/call_event.dart';
import 'package:flutter_callkit_incoming/flutter_callkit_incoming.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:get_storage/get_storage.dart';
import 'package:uuid/uuid.dart';

enum NotificationType {
  calling,
  chatMessage,
  meeting,
}

extension NotificationTypeExtension on NotificationType {
  String get value {
    switch (this) {
      case NotificationType.calling:
        return 'CALL_RESPONSE';
      case NotificationType.chatMessage:
        return 'CHAT_MESSAGE';
      case NotificationType.meeting:
        return 'MEETING_RESPONSE';
    }
  }
}

final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
final CallManager _callManager = CallManager();
final CallService _callService = CallService();
final _uuid = Uuid();

int notificationIdCounter = 0;

// bool userBusyStatus = false;
final ValueNotifier<bool> userBusyStatus = ValueNotifier(false);
String? currentChatUserId;

// void setUserBusyStatus(bool busyStatus) => userBusyStatus.value = busyStatus;

void setCurrentChatUserId(String? chatUserId) => currentChatUserId = chatUserId;

int getNextNotificationId() => notificationIdCounter++;

void initializeFirebaseMessaging() async {
  await _setupNotificationChannels();
  await _requestPermissions();

  _setupMessageHandlers();
  ChatHandlerManager.initializeGlobal();

  // Initialize CallKit listener for iOS
  if (Platform.isIOS) {
    FlutterCallkitIncoming.onEvent.listen(_handleCallKitEvent);
  }
}

Future<void> _setupNotificationChannels() async {
  await AwesomeNotifications().initialize(
    null,
    [
      NotificationChannel(
        channelKey: 'chat_messages',
        channelName: 'Chat Messages',
        channelDescription: 'Channel for chat message notifications',
        importance: NotificationImportance.High,
        playSound: true,
        enableVibration: true,
        defaultColor: Colors.blue,
        ledColor: Colors.blue,
        defaultRingtoneType: DefaultRingtoneType.Notification,
      ),
    ],
  );

  if (Platform.isAndroid) {
    AwesomeNotifications().setListeners(
      onActionReceivedMethod: NotificationHandler.onActionReceivedMethod,
    );
  }
}

Future<void> _requestPermissions() async {
  await _firebaseMessaging.requestPermission(
    alert: true,
    badge: true,
    sound: true,
    criticalAlert: true,
  );
}

void _setupMessageHandlers() {
  FirebaseMessaging.onMessage.listen(_firebaseForegroundHandler);
  FirebaseMessaging.onBackgroundMessage(_firebaseBackgroundHandler);
  FirebaseMessaging.onMessageOpenedApp.listen(_firebaseMessageOpenedHandler);
}

Future<void> _firebaseForegroundHandler(RemoteMessage message) async {
  final data = message.data;
  print("Foreground: $data");
  await _handleForegroundNotificationData(data);
}

Future<void> _firebaseBackgroundHandler(RemoteMessage message) async {
  final data = message.data;
  print("Background: $data");
  await _handleBackgroundNotificationData(data);
}

Future<void> _firebaseMessageOpenedHandler(RemoteMessage message) async {
  final data = message.data;
  print("Call Opened: $data");
  await _handleNotificationAction(data);
}

Future<void> _handleForegroundNotificationData(
    Map<String, dynamic> data) async {
  if (data['type'] == NotificationType.calling.value) {
    if (Platform.isIOS) {
      await _handleIncomingCall(data);
    }
  } else if (data['type'] == NotificationType.chatMessage.value) {
    if (Platform.isAndroid) showFCMNotificationForMessage(data);
  }
}

Future<void> _handleBackgroundNotificationData(
    Map<String, dynamic> data) async {
  if (data['type'] == NotificationType.calling.value) {
    if (Platform.isIOS) {
      await _handleIncomingCall(data);
    }
  }
}

Future<void> _handleNotificationAction(Map<String, dynamic> data) async {
  if (data['type'] == NotificationType.chatMessage.value) {
    navigateToCMTConnectPage(data);
  } else if (data['type'] == NotificationType.meeting.value) {
    navigateToMeetingPage();
  }
}

Future<void> _handleIncomingCall(Map<String, dynamic> data) async {
  final callUUID = data['callUUID'] ?? _uuid.v4();

  // If we're already in a call, send busy status immediately
  if ((userBusyStatus.value) && data['status'] == 'CALLING') {
    await _sendBusyStatus(
      callUUID: callUUID,
      channelName: data['channelName'],
      fcmToken: data['fcmToken'],
      jwtToken: data['jwtToken'],
    );
    return;
  }

  // Set user as busy and track current call
  userBusyStatus.value = true;

  if (data['status'] == 'CALLING') {
    await _callManager.showIncomingCallIOS(
      callerAgoraId: data['callerAgoraId'] ?? 'Unknown',
      callerName: data['callerName'] ?? 'Unknown',
      callerJob: data['callerJob'] ?? 'Unknown',
      callerCompany: data['callerCompany'] ?? 'Unknown',
      fcmToken: data['fcmToken'] ?? 'Unknown',
      channelName: data['channelName'],
      token: data['token'],
      callType: data['callType'] ?? 'audio',
      callUUID: callUUID,
      jwtToken: data['jwtToken'],
    );
  } else if (data['status'] == 'DECLINED') {
    final callUUID = data['callUUID'] as String;
    if (_callService.callUUID == callUUID) {
      _callService.handleDeclineStatus();
    }
    userBusyStatus.value = false;
  } else if (data['status'] == 'BUSY') {
    Timer.periodic(Duration(milliseconds: 100), (timer) {
      print("Trying");
      if (_callService.callUIReady) {
        print("Passed");
        _callService.handleBusyStatus();
        timer.cancel();
      }
    });
    userBusyStatus.value = false;
  }
}

Future<void> showMeetingNotification(Map<String, dynamic> data) async {}

Future<void> _handleCallKitEvent(CallEvent? event) async {
  print("Event from iOS: $event");
  if (event == null) return;

  switch (event.event) {
    case Event.actionCallAccept:
      final rawExtra = event.body['extra'];
      final extra = (rawExtra is Map)
          ? Map<String, dynamic>.from(rawExtra)
          : <String, dynamic>{};
      navigateToCallPage(extra, 'attended');
      break;
    case Event.actionCallEnded:
      print("Call Ended by iOS");
      break;
    // print("iOS declined");
    // print("Extra in Call Ended: ${event.body['extra']}");

    // if (onCallDeclinedGlobal != null && onCallBusyGlobal != null) {
    // if (declineStatus == 'Declined') {
    //   print("Declined by opposite user");
    //   onCallDeclinedGlobal!();
    // } else if (declineStatus == 'Busy') {
    //   onCallBusyGlobal!();
    // }
    // } else {
    // print("Declined by current user");
    // final callerName = event.body['extra']?['callerName'];
    // final callUUID = event.body['extra']?['callUUID'];
    // final channelName = event.body['extra']?['channelName'];
    // final fcmToken = event.body['extra']?['fcmToken'];
    // final jwtToken = event.body['extra']?['jwtToken'];
    // if (callUUID != null) {
    //   _sendDecline(callerName, callUUID, channelName, fcmToken, jwtToken);
    // }
    // }
    // userBusyStatus.value = false;
    case Event.actionCallDecline:
      print("Call Declined by iOS");
      break;
    default:
      break;
  }
}

int _findTabIndex(String pageIdentifier) {
  final storage = GetStorage();
  final event = storage.read('event') ?? {};
  final availableTabs = [
    'home',
    'agenda',
    if (event['cmtconnect'] ?? false) 'cmtconnect',
    if (event['networking'] ?? false) 'networking',
    if (event['cmtconnect'] ?? false) 'schedule',
    // if (event['watchLive'] ?? false) 'watchlive',
  ];

  return availableTabs.indexOf(pageIdentifier);
}

void navigateToCallPage(Map<String, dynamic> data, String status) {
  print("Page Navigating");
  if (userBusyStatus.value) return;
  userBusyStatus.value = true;
  print("Page Navigated");

  Get.to(() => CallScreen(
        calleeAgoraId: data['callerAgoraId'],
        calleeName: data['callerName'],
        calleeJob: data['callerJob'],
        calleeCompany: data['callerCompany'],
        fcmToken: data['fcmToken'],
        jwtToken: data['jwtToken'],
        channelName: data['channelName'],
        token: data['token'],
        callUUID: data['callUUID'],
        status: status,
        callType: data['callType'],
        callInitiator: false,
        fromColdStart: true,
      ))?.then((_) {
    // isCallPageOpen = false;
    userBusyStatus.value = false;
  });
}

Future<void> _sendDecline(
    callerName, callUUID, channelName, fcmToken, jwtToken) async {
  try {
    print("Trigger by send decline");
    await CallManager().endCall(callUUID);

    // Prepare FCM message payload for call decline
    final messageData = {
      "message": {
        "token": fcmToken,
        "data": {
          "callerName": callerName,
          "callUUID": callUUID,
          "channelName": channelName,
          "status": "DECLINED",
          "type": "CALL_RESPONSE",
          "click_action": "FLUTTER_NOTIFICATION_CLICK",
        },
        "android": {
          "priority": "high",
          "notification": {"sound": "default", "channel_id": "calls_channel"}
        },
        "apns": {
          "headers": {"apns-priority": "10"},
          "payload": {
            "aps": {"sound": "default", "badge": 1, "content-available": 1}
          }
        }
      }
    };

    // Send HTTP request to FCM API
    final response = await http
        .post(
          Uri.parse(Constants.FCM_API_URL),
          headers: {
            "Authorization": "Bearer $jwtToken",
            "Content-Type": "application/json"
          },
          body: jsonEncode(messageData),
        )
        .timeout(Duration(seconds: 10));

    if (response.statusCode != 200) {
      final errorData = jsonDecode(response.body);
      throw Exception("FCM Decline Error: ${errorData.toString()}");
    }

    print("✅ Call decline notification sent successfully from Service");
  } catch (e) {
    debugPrint("Failed to send call decline status: $e");
    // Optionally show error to user
    Get.snackbar(
      "Error",
      "Failed to send decline notification",
      snackPosition: SnackPosition.BOTTOM,
    );
  }
}

void navigateToCMTConnectPage(Map<String, dynamic> data) {
  // if (isCMTConnectPageOpen) return;
  // isCMTConnectPageOpen = true;

  Get.offAllNamed(
    '/user/dashboard',
    arguments: {
      'initialTab': 2,
      'connectSection': 'Online',
      'openChatWith': data['senderId'],
    },
  );
  // isCMTConnectPageOpen = false;
}

void navigateToMeetingPage() {
  // if (isSchedulePageOpen) return;
  // isSchedulePageOpen = true;

  Get.offAllNamed(
    '/user/dashboard',
    arguments: {
      'initialTab': _findTabIndex('schedule'),
    },
  );
  // isSchedulePageOpen = false;
}

class NotificationHandler {
  static Future<void> onActionReceivedMethod(
      ReceivedAction receivedAction) async {
    try {
      if (receivedAction.payload == null ||
          receivedAction.payload!['data'] == null) {
        print('Invalid notification payload');
        return;
      }
      final data = jsonDecode(receivedAction.payload!['data']!);
      final type = data!['type'];
      await AwesomeNotifications().dismiss(receivedAction.id!);
      if (type == NotificationType.chatMessage.value) {
        navigateToCMTConnectPage(data);
      }
    } catch (e) {
      print('Error handling notification action: $e');
    }
  }
}

Future<void> _sendBusyStatus({
  required String callUUID,
  required String channelName,
  required String fcmToken,
  required String jwtToken,
}) async {
  try {
    // Prepare FCM message payload for busy status
    final messageData = {
      "message": {
        "token": fcmToken,
        "data": {
          "callUUID": callUUID,
          "channelName": channelName,
          "status": "BUSY",
          "type": "CALL_RESPONSE",
          "click_action": "FLUTTER_NOTIFICATION_CLICK",
        },
        "android": {
          "priority": "high",
          // "notification": {"sound": "default", "channel_id": "calls_channel"}
        },
        "apns": {
          "headers": {"apns-priority": "10"},
          "payload": {
            "aps": {"sound": "default", "badge": 1, "content-available": 1}
          }
        }
      }
    };

    // Send HTTP request to FCM API
    final response = await http.post(
      Uri.parse(Constants.FCM_API_URL),
      headers: {
        "Authorization": "Bearer $jwtToken",
        "Content-Type": "application/json"
      },
      body: jsonEncode(messageData),
    );

    if (response.statusCode != 200) {
      final errorData = jsonDecode(response.body);
      throw Exception("FCM Busy Status Error: ${errorData.toString()}");
    }

    print("✅ Busy status notification sent successfully");
  } catch (e) {
    debugPrint("Failed to send busy status: $e");
    Get.snackbar(
      "Error",
      "Failed to send busy status",
      snackPosition: SnackPosition.BOTTOM,
    );
  }
}

void showFCMNotificationForMessage(Map<String, dynamic> data) {
  final isCurrentlyInChatScreen = data['senderId'] == currentChatUserId;
  if (isCurrentlyInChatScreen) return;

  String body = '';

  if (data['messageType'] == 'text') {
    body = data['message'];
    // } else if (data['messageType'] == MessageType.CUSTOM) {
    //   final customBody = message.body as ChatCustomMessageBody;
    //   if (customBody.event == "vcard") {
    //     body = "${attendee?['fullname'] ?? 'Someone'} shared a contact card.";
    //   } else if (customBody.event == "schedule_meeting") {
    //     body = "${attendee?['fullname'] ?? 'Someone'} scheduled a meeting.";
    //   } else {
    //     body = "You received a custom message.";
    //   }
  } else {
    body = "You received a message.";
  }

  AwesomeNotifications().createNotification(
    content: NotificationContent(
      id: getNextNotificationId(),
      channelKey: 'chat_messages',
      title: '${data['sender'] ?? 'Unknown'}',
      body: body,
      payload: {
        'data': jsonEncode({
          "senderId": data['senderId'],
          "type": NotificationType.chatMessage.value
        })
      },
      category: NotificationCategory.Message,
      wakeUpScreen: true,
      fullScreenIntent: true,
      displayOnBackground: true,
      displayOnForeground: !isCurrentlyInChatScreen,
      criticalAlert: true,
      showWhen: true,
      autoDismissible: true,
    ),
  );
}

class ChatHandlerManager {
  // Current screen tracking
  static String? currentScreen; // 'global', 'connect', or 'chat'
  static String? currentChatAgoraId; // Only relevant for chat screen

  // State management callbacks
  static Function()? updateConnectPageState;
  static Map<String, int>? lastMessageTimestamps;

  // Chat screen specific properties
  static List<ChatMessage>? _chatMessages;
  static VoidCallback? _processMessagesCallback;
  static Function(String, int)? _onMessageSentCallback;
  static Future<void> Function(List<ChatMessage>)? _markMessagesAsReadCallback;

  static void initializeGlobal() {
    currentScreen = 'global';
    _initializeHandler();
  }

  static void initializeConnect({
    required Function() updateState,
    required Map<String, int> timestamps,
  }) {
    currentScreen = 'connect';
    updateConnectPageState = updateState;
    lastMessageTimestamps = timestamps;
    _initializeHandler();
  }

  static void initializeChat({
    required List<ChatMessage> messages,
    required VoidCallback processMessages,
    required void Function(String, int) onMessageSent,
    required Future<void> Function(List<ChatMessage>) markMessagesAsRead,
    required String chatAgoraId,
  }) {
    currentScreen = 'chat';
    currentChatAgoraId = chatAgoraId;
    _chatMessages = messages;
    _processMessagesCallback = processMessages;
    _onMessageSentCallback = onMessageSent;
    _markMessagesAsReadCallback = markMessagesAsRead;
    _initializeHandler();
  }

  static void _initializeHandler() {
    // Remove any existing handler first
    ChatClient.getInstance.chatManager.removeEventHandler('UNIFIED_HANDLER');

    // Add new handler
    ChatClient.getInstance.chatManager.addEventHandler(
      'UNIFIED_HANDLER',
      ChatEventHandler(
        onMessagesReceived: _handleMessages,
      ),
    );
  }

  static void _handleMessages(List<ChatMessage> messages) async {
    switch (currentScreen) {
      case 'global':
        break;

      case 'connect':
        for (var message in messages) {
          final senderAgoraId = message.from ?? '';
          lastMessageTimestamps?[senderAgoraId] = message.serverTime;
        }
        updateConnectPageState?.call();
        break;

      case 'chat':
        // Process messages
        final filteredMessages =
            messages.where((m) => m.from == currentChatAgoraId).toList();
        _chatMessages?.insertAll(0, filteredMessages);
        _processMessagesCallback?.call();

        // Notify parent
        final currentTimestamp = DateTime.now().millisecondsSinceEpoch;
        _onMessageSentCallback?.call(
            currentChatAgoraId ?? '', currentTimestamp);

        // Mark as read
        if (_markMessagesAsReadCallback != null) {
          await _markMessagesAsReadCallback!(messages);
        }
        break;
    }
  }

  static void dispose() {
    currentScreen = null;
    currentChatAgoraId = null;
    updateConnectPageState = null;
    lastMessageTimestamps = null;
    _chatMessages = null;
    _processMessagesCallback = null;
    _onMessageSentCallback = null;
    _markMessagesAsReadCallback = null;
    ChatClient.getInstance.chatManager.removeEventHandler('UNIFIED_HANDLER');
  }
}
