// import 'dart:convert';
// import 'dart:io';

// import 'package:agora_chat_sdk/agora_chat_sdk.dart';
// import 'package:awesome_notifications/awesome_notifications.dart';
// import 'package:firebase_messaging/firebase_messaging.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:get_storage/get_storage.dart';

// enum NotificationType {
//   chatMessage,
//   meetingRequest,
//   meetingResponse,
//   meetingAccepted,
//   meetingDeclined,
// }

// extension NotificationTypeExtension on NotificationType {
//   String get value {
//     switch (this) {
//       case NotificationType.chatMessage:
//         return 'CHAT_MESSAGE';
//       case NotificationType.meetingRequest:
//         return 'MEETING_REQUEST';
//       case NotificationType.meetingResponse:
//         return 'MEETING_RESPONSE';
//       case NotificationType.meetingAccepted:
//         return 'MEETING_ACCEPTED';
//       case NotificationType.meetingDeclined:
//         return 'MEETING_DECLINED';
//     }
//   }
// }

// final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
// int notificationIdCounter = 0;
// bool _isInChatScreen = false;
// bool isCallPageOpen = false;
// bool isCMTConnectPageOpen = false;
// bool isSchedulePageOpen = false;
// bool isAppInForeground = true;
// bool userBusyStatus = false;
// String? currentChatUserId;
// String? declineStatus;

// void setChatScreenState(bool isOpen) => _isInChatScreen = isOpen;

// int getNextNotificationId() => notificationIdCounter++;

// void initializeFirebaseMessaging() async {
//   await _setupNotificationChannels();
//   await _requestPermissions();
//   _setupMessageHandlers();
//   ChatHandlerManager.initializeGlobal();
// }

// Future<void> _setupNotificationChannels() async {
//   await AwesomeNotifications().initialize(
//     null,
//     [
//       NotificationChannel(
//         channelKey: 'incoming_call_channel',
//         channelName: 'Call Notifications',
//         channelDescription: 'Notifications for incoming calls',
//         importance: NotificationImportance.Max,
//         defaultColor: Colors.red,
//         ledColor: Colors.red,
//         playSound: true,
//         enableVibration: true,
//         enableLights: true,
//         // soundSource: 'resource://raw/ring',
//         locked: true,
//         defaultRingtoneType: DefaultRingtoneType.Ringtone,
//         channelShowBadge: true,
//         onlyAlertOnce: true,
//         criticalAlerts: true,
//       ),
//       NotificationChannel(
//         channelKey: 'chat_messages',
//         channelName: 'Chat Messages',
//         channelDescription: 'Channel for chat message notifications',
//         importance: NotificationImportance.High,
//         playSound: true,
//         enableVibration: true,
//         defaultColor: Colors.blue,
//         ledColor: Colors.blue,
//         defaultRingtoneType: DefaultRingtoneType.Notification,
//       ),
//       NotificationChannel(
//         channelKey: 'schedule_meetings',
//         channelName: 'Scheduled Meetings',
//         channelDescription: 'Notifications for upcoming and completed meetings',
//         importance: NotificationImportance.High,
//         playSound: true,
//         enableVibration: true,
//         defaultColor: Colors.blue,
//         ledColor: Colors.blue,
//         defaultRingtoneType: DefaultRingtoneType.Notification,
//       ),
//     ],
//   );

//   if (Platform.isAndroid) {
//     AwesomeNotifications().setListeners(
//       onActionReceivedMethod: NotificationHandler.onActionReceivedMethod,
//     );
//   }
// }

// Future<void> _requestPermissions() async {
//   await _firebaseMessaging.requestPermission(
//     alert: true,
//     badge: true,
//     sound: true,
//     criticalAlert: true,
//   );
// }

// void _setupMessageHandlers() {
//   FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
//   FirebaseMessaging.onBackgroundMessage(_firebaseBackgroundHandler);
//   FirebaseMessaging.onMessageOpenedApp.listen(_handleMessageOpened);
// }

// Future<void> _handleForegroundMessage(RemoteMessage message) async {
//   final data = message.data;
//   print("Foreground: $data");
//   _logNotificationEvent('received', data['type'] ?? 'Null', data);
//   await _handleNotificationData(data);
// }

// Future<void> _firebaseBackgroundHandler(RemoteMessage message) async {
//   final data = message.data;
//   print("Background: $data");
//   _logNotificationEvent('background', data['type'] ?? 'Null', data);
//   await _handleNotificationData(data);
// }

// Future<void> _handleMessageOpened(RemoteMessage message) async {
//   final data = message.data;
//   print("Call Opened: $data");
//   // _logNotificationEvent(
//   //     'opened', jsonDecode(data['data'])['type'] ?? 'Null', data);
//   await _handleNotificationAction(data);
// }

// Future<void> _handleNotificationData(Map<String, dynamic> data) async {
//   if (data['type'].toString().startsWith('MEETING_')) {
//     await showMeetingNotification(data);
//   }
// }

// Future<void> _handleNotificationAction(Map<String, dynamic> data) async {
//   if (data['type'] == NotificationType.chatMessage.value) {
//     navigateToCMTConnectPage(data);
//   }
// }

// Future<void> showMeetingNotification(Map<String, dynamic> data) async {
//   final String type = data['type'];
//   final String sender = data['senderName'] ?? 'Someone';

//   String title;
//   String body;

//   switch (type) {
//     case 'MEETING_REQUEST':
//       title = 'Meeting Scheduled';
//       body = '$sender wants to meet with you';
//       break;
//     case 'MEETING_RESPONSE':
//       title = 'Meeting Status';
//       body =
//           '$sender has ${data['status']?.toLowerCase() ?? 'responded'} to your meeting';
//       break;
//     case 'MEETING_START':
//       title = 'Meeting Reminder';
//       body = '$sender is waiting to meet with you';
//       break;
//     default:
//       title = 'Meeting Update';
//       body = 'You have a new meeting notification from $sender';
//   }

//   await AwesomeNotifications().createNotification(
//     content: NotificationContent(
//       id: getNextNotificationId(),
//       channelKey: 'schedule_meetings',
//       title: title,
//       body: body,
//       payload: {'data': jsonEncode(data)},
//       category: NotificationCategory.Event,
//       wakeUpScreen: true,
//       autoDismissible: true,
//     ),
//   );
// }

// Future<void> showCallNotification(Map<String, dynamic> data) async {
//   final id = getNextNotificationId();
//   print("Awesome notification created");

//   await AwesomeNotifications().createNotification(
//     content: NotificationContent(
//       id: id,
//       channelKey: 'incoming_call_channel',
//       title: 'Incoming Call',
//       body: '${data['callerName']} is calling you',
//       payload: {'data': jsonEncode(data)},
//       category: NotificationCategory.Call,
//       wakeUpScreen: true,
//       fullScreenIntent: true,
//       locked: true,
//       autoDismissible: false,
//       displayOnForeground: false,
//       displayOnBackground: true,
//       criticalAlert: true,
//     ),
//     actionButtons: [
//       NotificationActionButton(
//         key: 'accept',
//         label: 'Accept',
//         color: Colors.green,
//         autoDismissible: true,
//       ),
//       NotificationActionButton(
//         key: 'decline',
//         label: 'Decline',
//         color: Colors.red,
//         autoDismissible: true,
//       ),
//     ],
//   );

//   Future.delayed(Duration(minutes: 1), () {
//     AwesomeNotifications().dismiss(id);
//     userBusyStatus = false;
//   });
// }

// int _findTabIndex(String pageIdentifier) {
//   final storage = GetStorage();
//   final event = storage.read('event') ?? {};
//   final availableTabs = [
//     'home',
//     'agenda',
//     if (event['cmtconnect'] ?? false) 'cmtconnect',
//     if (event['networking'] ?? false) 'networking',
//     if (event['cmtconnect'] ?? false) 'schedule',
//     // if (event['watchLive'] ?? false) 'watchlive',
//   ];

//   return availableTabs.indexOf(pageIdentifier);
// }

// void navigateToCMTConnectPage(Map<String, dynamic> data) {
//   if (isCMTConnectPageOpen) return;
//   isCMTConnectPageOpen = true;

//   Get.offAllNamed(
//     '/user/dashboard',
//     arguments: {
//       'initialTab': 2,
//       'connectSection': 'Online',
//       'openChatWith': data['senderId'],
//     },
//   );
//   isCMTConnectPageOpen = false;
// }

// void navigateToMeetingPage() {
//   if (isSchedulePageOpen) return;
//   isSchedulePageOpen = true;

//   Get.offAllNamed(
//     '/user/dashboard',
//     arguments: {
//       'initialTab': _findTabIndex('schedule'),
//     },
//   );
//   isSchedulePageOpen = false;
// }

// class NotificationHandler {
//   static Future<void> onActionReceivedMethod(
//       ReceivedAction receivedAction) async {
//     try {
//       print(jsonEncode(receivedAction.toMap()));

//       if (receivedAction.payload == null ||
//           receivedAction.payload!['data'] == null) {
//         print('Invalid notification payload');
//         return;
//       }

//       final data = jsonDecode(receivedAction.payload!['data']!);
//       final type = data!['type'];
//       final buttonKey = receivedAction.buttonKeyPressed;

//       print("Message data: $data");

//       // _logNotificationEvent('clicked', type ?? 'Null', data);

//       // print("Notification clicked: $receivedAction");
//       await AwesomeNotifications().dismiss(receivedAction.id!);

//       print(buttonKey);
//       if (type == NotificationType.chatMessage.value) {
//         navigateToCMTConnectPage(jsonDecode(data));
//       } else if (type.toString().startsWith('MEETING_')) {
//         print(type);
//         navigateToMeetingPage();
//       }
//     } catch (e) {
//       print('Error handling notification action: $e');
//     }
//   }
// }

// void _logNotificationEvent(
//     String action, String type, Map<String, dynamic> data) {
//   print('Notification $action - Type: $type, Data: $data');
// }

// void showLocalNotificationForMessage(ChatMessage message) {
//   if (_isInChatScreen && message.from == currentChatUserId) return;

//   final storage = GetStorage();
//   final attendees = storage.read<List<dynamic>>('attendees') ?? [];
//   final attendeeMap = {for (var a in attendees) a['agoraid']: a};
//   final attendee = attendeeMap[message.from];

//   String body = '';

//   if (message.body.type == MessageType.TXT) {
//     body = (message.body as ChatTextMessageBody).content;
//   } else if (message.body.type == MessageType.CUSTOM) {
//     final customBody = message.body as ChatCustomMessageBody;
//     if (customBody.event == "vcard") {
//       body = "${attendee?['fullname'] ?? 'Someone'} shared a contact card.";
//     } else if (customBody.event == "schedule_meeting") {
//       body = "${attendee?['fullname'] ?? 'Someone'} scheduled a meeting.";
//     } else {
//       body = "You received a custom message.";
//     }
//   } else {
//     body = "You received a message.";
//   }

//   AwesomeNotifications().createNotification(
//     content: NotificationContent(
//       id: message.serverTime.hashCode,
//       channelKey: 'chat_messages',
//       title: 'New message from ${attendee?['fullname'] ?? 'Unknown'}',
//       body: body,
//       payload: {
//         'data': jsonEncode({
//           "message_id": message.msgId,
//           "type": NotificationType.chatMessage.value
//         })
//       },
//       category: NotificationCategory.Message,
//       wakeUpScreen: true,
//       fullScreenIntent: true,
//       displayOnBackground: true,
//       displayOnForeground: !_isInChatScreen,
//       criticalAlert: true,
//       showWhen: true,
//       autoDismissible: true,
//     ),
//   );
// }

// class ChatHandlerManager {
//   // Current screen tracking
//   static String? currentScreen; // 'global', 'connect', or 'chat'
//   static String? currentChatAgoraId; // Only relevant for chat screen

//   // State management callbacks
//   static Function()? updateConnectPageState;
//   static Function(String, int)? updateChatScreenState;
//   static Map<String, int>? lastMessageTimestamps;

//   // Chat screen specific properties
//   static List<ChatMessage>? _chatMessages;
//   static VoidCallback? _processMessagesCallback;
//   static Function(String, int)? _onMessageSentCallback;
//   static Future<void> Function(List<ChatMessage>)? _markMessagesAsReadCallback;

//   static void initializeGlobal() {
//     currentScreen = 'global';
//     _initializeHandler();
//   }

//   static void initializeConnect({
//     required Function() updateState,
//     required Map<String, int> timestamps,
//   }) {
//     currentScreen = 'connect';
//     updateConnectPageState = updateState;
//     lastMessageTimestamps = timestamps;
//     _initializeHandler();
//   }

//   static void initializeChat({
//     required List<ChatMessage> messages,
//     required VoidCallback processMessages,
//     required void Function(String, int) onMessageSent,
//     required Future<void> Function(List<ChatMessage>) markMessagesAsRead,
//     required String chatAgoraId,
//   }) {
//     currentScreen = 'chat';
//     currentChatAgoraId = chatAgoraId;
//     _chatMessages = messages;
//     _processMessagesCallback = processMessages;
//     _onMessageSentCallback = onMessageSent;
//     _markMessagesAsReadCallback = markMessagesAsRead;
//     _initializeHandler();
//   }

//   static void _initializeHandler() {
//     // Remove any existing handler first
//     ChatClient.getInstance.chatManager.removeEventHandler('UNIFIED_HANDLER');

//     // Add new handler
//     ChatClient.getInstance.chatManager.addEventHandler(
//       'UNIFIED_HANDLER',
//       ChatEventHandler(
//         onMessagesReceived: _handleMessages,
//       ),
//     );
//   }

//   static void _handleMessages(List<ChatMessage> messages) async {
//     switch (currentScreen) {
//       case 'global':
//         for (var message in messages) {
//           print(message);
//           if (Platform.isAndroid) showLocalNotificationForMessage(message);
//         }
//         break;

//       case 'connect':
//         for (var message in messages) {
//           if (Platform.isAndroid) showLocalNotificationForMessage(message);
//           final senderAgoraId = message.from ?? '';
//           lastMessageTimestamps?[senderAgoraId] = message.serverTime;
//         }
//         updateConnectPageState?.call();
//         break;

//       case 'chat':
//         // Notification handling
//         for (var message in messages) {
//           if (isAppInForeground && message.from != currentChatAgoraId) {
//             if (Platform.isAndroid) showLocalNotificationForMessage(message);
//             print(_isInChatScreen);
//           } else {
//             if (Platform.isAndroid) showLocalNotificationForMessage(message);
//           }
//         }

//         // Process messages
//         final filteredMessages =
//             messages.where((m) => m.from == currentChatAgoraId).toList();
//         _chatMessages?.insertAll(0, filteredMessages);
//         _processMessagesCallback?.call();

//         // Notify parent
//         final currentTimestamp = DateTime.now().millisecondsSinceEpoch;
//         _onMessageSentCallback?.call(
//             currentChatAgoraId ?? '', currentTimestamp);

//         // Mark as read
//         if (_markMessagesAsReadCallback != null) {
//           await _markMessagesAsReadCallback!(messages);
//         }
//         break;
//     }
//   }

//   static void dispose() {
//     currentScreen = null;
//     currentChatAgoraId = null;
//     updateConnectPageState = null;
//     updateChatScreenState = null;
//     lastMessageTimestamps = null;
//     _chatMessages = null;
//     _processMessagesCallback = null;
//     _onMessageSentCallback = null;
//     _markMessagesAsReadCallback = null;
//     ChatClient.getInstance.chatManager.removeEventHandler('UNIFIED_HANDLER');
//   }
// }
