import 'dart:async';
import 'dart:io';

import 'package:cmtmeet/utils/constants.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:agora_chat_sdk/agora_chat_sdk.dart';
import 'dart:convert';

class TokenManager {
  static final _storage = GetStorage();
  static bool _isRefreshing = false;
  static Timer? _refreshTimer;

  static Future<void> scheduleTokenRefresh() async {
    _cancelExistingTimer();

    final expirationTimeString = _storage.read("tokenExpiration");
    if (expirationTimeString == null) return;

    final expirationTime = DateTime.parse(expirationTimeString).toLocal();
    final currentTime = DateTime.now();
    final timeUntilExpiration = expirationTime.difference(currentTime);

    if (timeUntilExpiration < const Duration(minutes: 5)) {
      await refreshToken();
      return;
    }

    _scheduleNewRefresh(timeUntilExpiration);
  }

  static void _cancelExistingTimer() {
    _refreshTimer?.cancel();
    _refreshTimer = null;
  }

  static void _scheduleNewRefresh(Duration timeUntilExpiration) {
    final refreshTime = timeUntilExpiration - const Duration(minutes: 5);
    _refreshTimer = Timer(refreshTime, _executeScheduledRefresh);
  }

  static Future<void> _executeScheduledRefresh() async {
    try {
      await refreshToken();
    } catch (e) {
      print('❌ Scheduled refresh failed: $e');
      await _handleRefreshFailure(e);
    }
  }

  static Future<void> refreshToken() async {
    if (_isRefreshing) return;
    _isRefreshing = true;

    try {
      final user = _storage.read("user");
      if (user == null || user["agoraid"] == null) {
        throw Exception("User data or Agora ID not found");
      }

      final tokens = await _fetchNewTokens(user["agoraid"]);
      await _storeTokens(tokens);
      await _renewSdkConnections(tokens, user["agoraid"]);
      await scheduleTokenRefresh();
    } finally {
      _isRefreshing = false;
    }
  }

  static Future<Map<String, dynamic>> _fetchNewTokens(String agoraId) async {
    final response = await http
        .post(
          Uri.parse("https://${Constants.url}/auth/refresh-token"),
          headers: {"Content-Type": "application/json"},
          body: jsonEncode({"agoraid": agoraId}),
        )
        .timeout(const Duration(seconds: 10));

    if (response.statusCode != 200) {
      throw HttpException('Failed to refresh token: ${response.statusCode}');
    }

    final responseData = jsonDecode(response.body);
    if (responseData["message"] != "Token refreshed") {
      throw Exception("Failed to refresh token: ${responseData["message"]}");
    }

    return responseData["tokens"] as Map<String, dynamic>;
  }

  static Future<void> _storeTokens(Map<String, dynamic> tokens) async {
    await _storage.write("chatToken", tokens["chatToken"] ?? '');
    await _storage.write("rtmToken", tokens["rtmToken"] ?? '');
    await _storage.write("tokenExpiration", tokens["tokenExpiration"] ?? '');
  }

  static Future<void> _renewSdkConnections(
      Map<String, dynamic> tokens, String agoraid) async {
    try {
      if (await ChatClient.getInstance.isLoginBefore()) {
        await ChatClient.getInstance.renewAgoraToken(tokens["chatToken"]);
      } else {
        await ChatClient.getInstance
            .loginWithToken(agoraid, tokens["chatToken"]);
      }
    } catch (e) {
      print('Failed to renew SDK connections: $e');
      rethrow;
    }
  }

  static Future<void> _handleRefreshFailure(dynamic error) async {
    if (error is SocketException ||
        error is TimeoutException ||
        error is HttpException) {
      await _retryRefresh();
    } else {
      await _clearAuthState();
    }
  }

  static Future<void> _retryRefresh() async {
    for (int i = 0; i < 3; i++) {
      await Future.delayed(Duration(seconds: (i + 1) * 2));
      try {
        await refreshToken();
        return;
      } catch (e) {
        print('Retry attempt ${i + 1} failed: $e');
      }
    }
    await _clearAuthState();
  }

  static Future<void> _clearAuthState() async {
    await _storage.erase();
    await _storage.write("isLoggedOut", true);
    Get.offAllNamed('/login');
  }

  static bool isTokenValid() {
    final expirationTimeString = _storage.read("tokenExpiration");
    if (expirationTimeString == null) return false;

    final expirationTime = DateTime.parse(expirationTimeString);
    final currentTime = DateTime.now();
    return currentTime.add(const Duration(minutes: 5)).isBefore(expirationTime);
  }

  static Future<void> handleAppResume() async {
    if (_isRefreshing) return;

    final user = _storage.read("user");
    if (user == null) return;

    try {
      if (!isTokenValid()) {
        await refreshToken();
        return;
      }

      if (!(await ChatClient.getInstance.isLoginBefore())) {
        await ChatClient.getInstance
            .loginWithToken(user["agoraid"], _storage.read("chatToken"));
      }
    } catch (e) {
      print('⚠️ Resume recovery failed: $e');
    }
  }
}
