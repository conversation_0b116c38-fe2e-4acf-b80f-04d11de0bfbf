import 'dart:async';
import 'dart:convert';
import 'package:cmtmeet/pages/room_page.dart';
// import 'package:cmtmeet/pages/room_page_china.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:web_socket_channel/web_socket_channel.dart';

class WebSocketService {
  WebSocketChannel? _channel;
  final Map<String, String> userStatuses = {};
  final Map<String, dynamic> roomDetails = {};
  final List<void Function(String, String, String)> _statusListeners = [];
  final List<void Function(Map<String, dynamic>)> _roomListeners = [];

  String? _currentUrl;
  bool _isManuallyDisconnected = false;

  final StreamController<dynamic> _socketResponseController =
      StreamController<dynamic>.broadcast();

  Stream<dynamic> get socketResponseStream => _socketResponseController.stream;

  /// Initializes the WebSocket connection with the provided URL.
  void initializeConnection(String url) {
    _currentUrl = url;
    _isManuallyDisconnected = false;

    _channel = WebSocketChannel.connect(Uri.parse(url));
    _channel?.stream.listen(
      (message) {
        _handleMessage(message);
      },
      onDone: () {
        print("WebSocket connection closed.");
        if (!_isManuallyDisconnected) {
          print("Attempting to reconnect...");
          _reconnect();
        } else {
          print("WebSocket closed manually.");
        }
      },
      onError: (error) {
        print("WebSocket error: $error");
      },
    );

    print("WebSocket connection initialized.");
    final userId = GetStorage().read("user")?["id"];
    final eventCode = GetStorage().read("event")?["eventCode"];
    final currentRoom = GetStorage().read("currentRoom");
    if (userId != null && eventCode != null) {
      sendMessage({"type": "userStatus", "userId": userId, "eventCode": eventCode});
    }

    if (eventCode != null) {
      sendMessage({"type": "networkRoom", "action": "roomData", "eventCode": eventCode});
    }
    // If we were in a room before, try to reconnect
    if (currentRoom != null) {
      sendMessage({
        "type": "networkRoom",
        "action": "reconnect",
        "roomName": currentRoom,
        "userId": userId,
        "eventCode": eventCode
      });
    }
  }

  /// Reconnects the WebSocket if it is closed unexpectedly.
  void _reconnect() {
    if (_currentUrl != null) {
      initializeConnection(_currentUrl!);
    }
  }

  /// Handles incoming WebSocket messages.
  void _handleMessage(String message) {
    try {
      final data = jsonDecode(message);

      switch (data['type']) {
        case 'welcome':
          print(data['message']);
          break;

        case 'userStateChange':
          final userId = data['userId'].toString();
          final eventCode = data['eventCode'].toString();
          final status = data['status'];
          final key = '$userId-$eventCode';

          userStatuses[key] = status;
          _notifyStatusListeners(userId, eventCode, status);
          break;

        case 'existingUserStatuses':
          final users = data['users'] as List<dynamic>;
          for (final user in users) {
            final userId = user['userId'].toString();
            final eventCode = data['eventCode'].toString();
            final status = user['status'];
            final key = '$userId-$eventCode';

            userStatuses[key] = status;
            _notifyStatusListeners(userId, eventCode, status);
          }
          break;

        // Room management messages
        case 'roomJoined':
          final token = data['token'];
          final isHost = data['isHost'];
          final channel = data['channel'];
          final room = data['room'];
          final roomName = room['name'];
          final roomType = room['type'];
          GetStorage().write("currentRoom", roomName);
          Get.to(() => RoomPage(
              room: channel,
              roomName: roomName,
              token: token,
              isHost: isHost,
              roomType: roomType));
          break;

        case 'userJoined':
          _socketResponseController.add(data);
          break;

        case 'userLeft':
          _socketResponseController.add(data);
          break;

        case 'newHost':
          _socketResponseController.add(data);
          break;

        case 'networkRoomData':
          print('[WebSocket] Received full room data refresh');
          roomDetails.clear();
          final result = Map<String, dynamic>.from(data['result'] ?? {});
          print('[WebSocket] Loaded ${result.length} rooms');
          roomDetails.addAll(result);
          _notifyRoomListeners(roomDetails);
          print('[WebSocket] Notified listeners of full room data update');
          break;

        case 'roomUpdate':
          print('[WebSocket] Received room update');
          if (data['data'] is Map<String, dynamic>) {
            final updateData = data['data'] as Map<String, dynamic>;
            print('[WebSocket] Updating ${updateData.length} room(s)');

            updateData.forEach((roomKey, roomInfo) {
              if (roomInfo is Map<String, dynamic>) {
                print('[WebSocket] Updating room $roomKey');
                final oldMemberCount =
                    (roomDetails[roomKey]?['members'] as List?)?.length ?? 0;
                final newMemberCount = (roomInfo['members'] as List?)?.length ?? 0;

                roomDetails[roomKey] = Map<String, dynamic>.from(roomInfo);

                print('[WebSocket] Room $roomKey member count changed from '
                    '$oldMemberCount to $newMemberCount');
              }
            });

            _notifyRoomListeners(updateData);
            print('[WebSocket] Notified listeners of room updates');
          } else {
            print('[WebSocket] Invalid room update format');
          }
          break;

        case 'admin_clean_success':
          _notifyRoomListeners(data);
          break;

        case 'roomError':
          _notifyRoomListeners(data);
          break;

        default:
          print('Unknown WebSocket message type: ${data['type']}');
      }
    } catch (e) {
      print("Failed to decode WebSocket message: $message, Error: $e");
    }
  }

  /// Sends a message through the WebSocket.
  void sendMessage(Map<String, dynamic> message) {
    if (_channel == null) return;
    _channel?.sink.add(jsonEncode(message));
  }

  /// Room Management Functions
  void joinRoom(String room) {
    final userId = GetStorage().read("user")?["id"];
    final fullname = GetStorage().read("user")?["fullname"];
    final eventCode = GetStorage().read("event")?["eventCode"];
    if (userId == null) return;

    sendMessage({
      "type": "networkRoom",
      "action": "join",
      "roomName": room,
      "userId": userId,
      "fullname": fullname,
      "eventCode": eventCode
    });
  }

  void leaveRoom() {
    final userId = GetStorage().read("user")?["id"];
    final fullname = GetStorage().read("user")?["fullname"];
    final eventCode = GetStorage().read("event")?["eventCode"];
    final currentRoom = GetStorage().read("currentRoom");
    if (userId == null) return;

    sendMessage({
      "type": "networkRoom",
      "action": "leave",
      "roomName": currentRoom,
      "userId": userId,
      "fullname": fullname,
      "eventCode": eventCode
    });

    // Clear current room from storage after leaving
    GetStorage().remove("currentRoom");
  }

  /// Status Listeners Management
  void _notifyStatusListeners(String userId, String eventCode, String status) {
    for (final listener in _statusListeners) {
      listener(userId, eventCode, status);
    }
  }

  void addStatusListener(void Function(String, String, String) listener) {
    _statusListeners.add(listener);
  }

  void removeStatusListener(void Function(String, String, String) listener) {
    _statusListeners.remove(listener);
  }

  /// Room Listeners Management
  void _notifyRoomListeners(Map<String, dynamic> message) {
    for (final listener in _roomListeners) {
      listener(message);
    }
  }

  void addRoomListener(void Function(Map<String, dynamic>) listener) {
    _roomListeners.add(listener);
  }

  void removeRoomListener(void Function(Map<String, dynamic>) listener) {
    _roomListeners.remove(listener);
  }

  /// Connection Management
  bool isConnectionClosed() {
    return _channel == null;
  }

  void closeConnection() {
    _isManuallyDisconnected = true;
    _channel?.sink.close();
    _channel = null;
    userStatuses.clear();
    GetStorage().remove("currentRoom");
  }
}

final WebSocketService webSocketService = WebSocketService();
