import 'package:cmtmeet/pages/getstart_page.dart';
import 'package:cmtmeet/pages/signing_page.dart';
import 'package:cmtmeet/pages/splash_screen.dart';
import 'package:cmtmeet/pages/user_dashboard_page.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:cmtmeet/pages/onboarding_page.dart';
// import 'package:cmt/pages/signin_page.dart';
// import 'package:cmt/pages/home_page.dart';

class AppPages {
  static final routes = [
    GetPage(name: '/splash', page: () => LaunchRouter()),
    GetPage(name: '/get-start', page: () => GetStartPage()),
    GetPage(
      name: '/onboarding',
      page: () => OnboardingPage(),
      transition: Transition.rightToLeft,
      transitionDuration: Duration(milliseconds: 300),
    ),
    GetPage(
      name: '/login',
      page: () => SigningPage(),
      transition: Transition.rightToLeft,
      transitionDuration: Duration(milliseconds: 300),
    ),
    GetPage(
      name: '/admin/dashboard',
      page: () => Container(
        color: Colors.deepPurpleAccent,
      ),
    ),
    GetPage(
      name: '/user/dashboard',
      page: () => UserDashboardPage(),
      transition: Transition.rightToLeft,
      transitionDuration: Duration(milliseconds: 300),
    ),
  ];
}
