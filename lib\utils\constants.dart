import 'package:flutter/material.dart';

class Constants {
  static const String url =
      'meetagora-hfdjcgbcdrepbuc0.southeastasia-01.azurewebsites.net';
  // static const String url = 'fond-generally-stag.ngrok-free.app';
  static const String clientId = '86f9uu0x29oldd';
  static const String clientSecret = 'WPL_AP1.JhleCa7sy2tJaC6w.t9Pq/Q==';
  static const String redirectUrl =
      'https://www.cmtevents.com/cmtmeet/linkedin-callback.html';
  static const String appKey = '611215916#1530627';
  static const String appId = '4970bcf832df4acfa9a191eb5cbfcd7d';
  static const String appName = 'CMT';
  static const String fcmServiceToken = '953784194836';
  static const String FCM_API_URL =
      'https://fcm.googleapis.com/v1/projects/cmt-meet-fd921/messages:send';
  static const colorApp = Color.fromRGBO(192, 16, 88, 1);
  static const fillColor = Color.fromRGBO(224, 227, 231, 1);
  static const borderColor = Colors.red;
  static const spacingHeight30 = SizedBox(height: 30);
  static const spacingHeight20 = SizedBox(height: 20);
  static const spacingHeight10 = SizedBox(height: 10);
  static const spacingHeight5 = SizedBox(height: 5);
  static const spacingWidth5 = SizedBox(width: 5);
  static const colorWhite = Colors.white;
  static final textStyle = TextStyle(
    fontWeight: FontWeight.w600,
    fontSize: 16,
    // fontFamily: 'Verdana',
    // fontFamily: 'Roboto',
    fontFamily: 'Poppins',
    letterSpacing: 0.0,
  );
  static final textStyleSubmit = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w500,
    fontFamily: 'Roboto',
    letterSpacing: 0.5,
  );
  static final textStyleSignout = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w600,
    fontFamily: 'sans-serif',
  );
  static final textStyleAgenda = TextStyle(
    fontWeight: FontWeight.w400,
    fontSize: 14,
    fontFamily: 'Poppins',
  );
}
