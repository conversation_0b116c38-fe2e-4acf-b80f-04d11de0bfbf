// Import Firebase scripts for service worker
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js');

// Firebase configuration
// Note: These should match the configuration in src/config/firebase.js
const firebaseConfig = {
  apiKey: "AIzaSyAIkEyJ_S8vToYMO2b4UBFTTdb6fOpSSdQ",
  authDomain: "cmt-meet-fd921.firebaseapp.com",
  projectId: "cmt-meet-fd921",
  storageBucket: "cmt-meet-fd921.firebasestorage.app",
  messagingSenderId: "953784194836",
  appId: "1:953784194836:web:9fbc8a15ba903d49c51401"
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);

// Retrieve Firebase Messaging object
const messaging = firebase.messaging();

// Handle background messages
messaging.onBackgroundMessage(function(payload) {
  console.log('[firebase-messaging-sw.js] Received background message ', payload);
  
  // Customize notification here
  const notificationTitle = payload.notification?.title || 'Incoming Call';
  const notificationOptions = {
    body: payload.notification?.body || 'You have an incoming call',
    icon: '/logo192.png',
    badge: '/logo192.png',
    tag: 'incoming-call',
    requireInteraction: true,
    actions: [
      {
        action: 'answer',
        title: 'Answer',
        icon: '/logo192.png'
      },
      {
        action: 'decline',
        title: 'Decline',
        icon: '/logo192.png'
      }
    ],
    data: payload.data
  };

  self.registration.showNotification(notificationTitle, notificationOptions);
});

// Handle notification click events
self.addEventListener('notificationclick', function(event) {
  console.log('[firebase-messaging-sw.js] Notification click received.');

  event.notification.close();

  if (event.action === 'answer') {
    // Handle answer action
    console.log('User clicked Answer');
    // Open the app and handle the call
    event.waitUntil(
      clients.openWindow('/')
    );
  } else if (event.action === 'decline') {
    // Handle decline action
    console.log('User clicked Decline');
    // Send decline message to the app
  } else {
    // Handle default click (open app)
    event.waitUntil(
      clients.openWindow('/')
    );
  }
});
