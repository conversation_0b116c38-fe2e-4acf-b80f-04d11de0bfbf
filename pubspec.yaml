name: cmtmeet
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.12+12

environment:
  sdk: ^3.6.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  firebase_core: ^3.13.0
  firebase_messaging: ^15.2.5
  get_storage: ^2.1.1
  agora_chat_sdk: ^1.3.2
  http: ^1.3.0
  get: ^4.7.2
  awesome_notifications: ^0.10.1
  agora_rtc_engine: ^6.5.1
  permission_handler: ^12.0.0
  wakelock_plus: ^1.2.11
  web_socket_channel: ^3.0.2
  url_launcher: ^6.3.1
  flutter_profile_picture: ^2.0.0
  carousel_slider: ^5.0.0
  fluttertoast: ^8.2.12
  flutter_animate: ^4.5.2
  android_intent_plus: ^5.3.0
  cached_network_image: ^3.4.1
  linkedin_login: ^3.1.3
  add_2_calendar: ^3.0.1
  flutter_native_splash: ^2.4.4
  flutter_slidable: ^4.0.0
  flutter_callkit_incoming: ^2.5.2
  uuid: ^4.5.1
  flutter_ringtone_player: ^4.0.0+4
  firebase_remote_config: ^5.4.5
  in_app_update: ^4.2.3
  package_info_plus: ^8.3.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.14.3

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.

flutter_icons:
  android: true
  ios: true
  image_path: "assets/logo.png"
  adaptive_icon_background: "#ffffff"
  adaptive_icon_foreground: "assets/logo_foreground.png"

flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
flutter_native_splash:
  color: "#ffffff"
  image: assets/cmtmeet_800px.png

  android_12:
    image: assets/cmtmeet_800px.png
    color: "#ffffff"

  web: false
