"use client"

import React, { useState, useEffect } from "react"
import {
  Dialog,
  DialogType,
  IconButton,
  PrimaryButton,
  Spinner,
  SpinnerSize,
  Text,
  mergeStyleSets,
  FontWeights,
  getTheme,
  Stack,
  StackItem,
} from "@fluentui/react"
import { Persona, PersonaSize } from "@fluentui/react/lib/Persona"

// Styles for the call component
const theme = getTheme()
const styles = mergeStyleSets({
  callDialog: {
    width: "350px",
    maxWidth: "90vw",
    borderRadius: "8px",
    overflow: "hidden",
  },
  callHeader: {
    backgroundColor: theme.palette.themePrimary,
    padding: "20px",
    textAlign: "center",
    color: "white",
    borderTopLeftRadius: "8px",
    borderTopRightRadius: "8px",
  },
  personaContainer: {
    padding: "20px 0",
  },
  callStatusText: {
    fontSize: "14px",
    fontWeight: FontWeights.semibold,
    marginTop: "8px",
    color: theme.palette.white,
  },
  callDurationText: {
    fontSize: "14px",
    marginTop: "6px",
    color: theme.palette.white,
  },
  callBody: {
    padding: "20px",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    minHeight: "150px",
  },
  callControls: {
    display: "flex",
    justifyContent: "center",
    gap: "16px",
    marginTop: "20px",
  },
  callProperties: {
    display: "flex",
    flexDirection: "column",
    gap: "8px",
    marginTop: "15px",
    width: "100%",
    maxWidth: "250px",
    border: `1px solid ${theme.palette.neutralLight}`,
    padding: "10px",
    borderRadius: "4px",
  },
  propertyRow: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    fontSize: "12px",
  },
  propertyLabel: {
    color: theme.palette.neutralSecondary,
  },
  propertyValue: {
    color: theme.palette.neutralPrimary,
    fontWeight: FontWeights.semibold,
  },
  controlButton: {
    width: "48px",
    height: "48px",
    borderRadius: "50%",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    cursor: "pointer",
    border: "none",
  },
  endCallButton: {
    backgroundColor: "#ff4d4f",
    color: "white",
    "&:hover": {
      backgroundColor: "#ff7875",
    },
  },
  muteButton: {
    backgroundColor: theme.palette.neutralLight,
    color: theme.palette.themePrimary,
    "&:hover": {
      backgroundColor: theme.palette.neutralLighter,
    },
  },
  mutedButton: {
    backgroundColor: theme.palette.themePrimary,
    color: "white",
    "&:hover": {
      backgroundColor: theme.palette.themeDark,
    },
  },
  disabledButton: {
    backgroundColor: theme.palette.neutralLight,
    color: theme.palette.neutralTertiary,
    "&:hover": {
      backgroundColor: theme.palette.neutralLighter,
    },
  },
  iconStyles: {
    fontSize: "16px",
  },
  connectingSpinner: {
    marginBottom: "20px",
  },
  helpText: {
    fontSize: "12px",
    color: theme.palette.neutralSecondary,
    marginTop: "20px",
    maxWidth: "250px",
    textAlign: "center",
  },
  audioLevelIndicator: {
    width: "20px",
    height: "100%",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "flex-end",
    margin: "0 auto",
    marginTop: "10px",
  },
  audioLevelBar: {
    width: "4px",
    backgroundColor: theme.palette.themePrimary,
    borderRadius: "2px",
    transition: "height 0.1s ease",
  },
  audioContainer: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    height: "20px",
    marginTop: "8px",
  },
  connectionStateText: {
    fontSize: "12px",
    color: theme.palette.neutralSecondary,
    marginTop: "4px",
  },
  networkQualityIndicator: {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    marginTop: "8px",
    gap: "2px",
  },
  networkBar: {
    width: "4px",
    height: "8px",
    backgroundColor: theme.palette.neutralLight,
    borderRadius: "1px",
    transition: "all 0.3s ease",
  },
  networkBarActive: {
    backgroundColor: theme.palette.themePrimary,
  },
  networkQualityText: {
    fontSize: "10px",
    color: theme.palette.neutralSecondary,
    marginLeft: "4px",
  },
  remoteAudioLevelContainer: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    height: "20px",
    marginTop: "8px",
  },
})

// Enum for call status descriptions
const CallStatusText = {
  idle: "Call Ended",
  connecting: "Connecting...",
  connected: "Connected",
  disconnected: "Call Disconnected",
}

// Network quality labels
const NetworkQualityText = {
  0: "Unknown",
  1: "Excellent",
  2: "Good",
  3: "Fair",
  4: "Poor",
  5: "Bad",
  6: "Very Bad",
}

const CallComponent = ({
  activeCall,
  callStatus,
  callDuration,
  isMuted,
  localTrackState,
  remoteUsers,
  onEndCall,
  onToggleMute,
  onSetAudioEnabled,
  formatCallDuration,
  connectionState,
  networkQuality,
}) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [longConnecting, setLongConnecting] = useState(false)
  const [audioLevel, setAudioLevel] = useState(0)
  const audioDetectionIntervalRef = React.useRef(null)

  // Open dialog when there's an active call
  useEffect(() => {
    if (activeCall) {
      console.log("Opening call dialog for:", activeCall.peerName)
      setIsDialogOpen(true)
      setLongConnecting(false)
    } else {
      setIsDialogOpen(false)
      setLongConnecting(false)
    }
  }, [activeCall])

  // Set "long connecting" state if connecting takes more than 10 seconds
  useEffect(() => {
    let timer
    if (callStatus === "connecting" && activeCall) {
      timer = setTimeout(() => {
        setLongConnecting(true)
      }, 10000)
    } else {
      setLongConnecting(false)
    }
    return () => {
      if (timer) clearTimeout(timer)
    }
  }, [callStatus, activeCall])

  // Audio level detection for visualization
  useEffect(() => {
    if (callStatus === "connected" && !isMuted && localTrackState.audioTrackEnabled) {
      // Simple audio level simulation based on speaking probability
      audioDetectionIntervalRef.current = setInterval(() => {
        // In a real implementation, you would get the actual audio level from the track
        // For example: const level = localMicrophoneTrack.getVolumeLevel();

        // For now, we'll use a more realistic simulation
        const randomBase = Math.random() * 0.3 // Base level (background noise)
        const speakingProbability = 0.4 // 40% chance of "speaking" in any given interval

        if (Math.random() < speakingProbability) {
          // When "speaking", generate a higher level
          const speakingLevel = randomBase + Math.random() * 0.7
          setAudioLevel(speakingLevel)
        } else {
          // Otherwise, just show the background noise level
          setAudioLevel(randomBase)
        }
      }, 100)
    } else {
      setAudioLevel(0)
      if (audioDetectionIntervalRef.current) {
        clearInterval(audioDetectionIntervalRef.current)
        audioDetectionIntervalRef.current = null
      }
    }

    return () => {
      if (audioDetectionIntervalRef.current) {
        clearInterval(audioDetectionIntervalRef.current)
      }
    }
  }, [callStatus, isMuted, localTrackState.audioTrackEnabled])

  // Add a useEffect to handle call connection timeout
  useEffect(() => {
    let connectionTimeoutTimer

    if (callStatus === "connecting") {
      // If call is still connecting after 30 seconds, show an error
      connectionTimeoutTimer = setTimeout(() => {
        if (callStatus === "connecting") {
          console.log("Call connection timed out")
          onEndCall()
        }
      }, 30000)
    }

    return () => {
      if (connectionTimeoutTimer) {
        clearTimeout(connectionTimeoutTimer)
      }
    }
  }, [callStatus, onEndCall])

  // Add a useEffect to handle remote user audio
  useEffect(() => {
    // Ensure remote users' audio tracks are playing
    if (callStatus === "connected" && remoteUsers && remoteUsers.length > 0) {
      remoteUsers.forEach(user => {
        if (user.audioTrack) {
          // Play the remote audio track if it's available
          try {
            console.log(`Ensuring remote user ${user.uid}'s audio is playing`);
            user.audioTrack.play();
          } catch (err) {
            console.warn(`Error playing remote user ${user.uid}'s audio:`, err);
          }
        }
      });
    }
  }, [callStatus, remoteUsers]);

  // Close dialog and end call
  const handleDismiss = () => {
    console.log("User ended call from dialog")
    onEndCall()
    setIsDialogOpen(false)
  }

  // Helper function to render network quality bars
  const renderNetworkQuality = (quality) => {
    if (!quality) return null;
    
    // Use uplink quality for outgoing, downlink for incoming
    const qualityLevel = activeCall?.isOutgoing 
      ? quality.uplinkNetworkQuality
      : quality.downlinkNetworkQuality;
    
    // Default to 0 (unknown) if undefined
    const level = typeof qualityLevel === 'undefined' ? 0 : qualityLevel;
    
    // Render 5 bars with appropriate active state based on quality level
    // Lower number = better quality (1 is best, 6 is worst)
    return (
      <div className={styles.networkQualityIndicator}>
        {[5, 4, 3, 2, 1].map((barLevel) => (
          <div 
            key={`network-bar-${barLevel}`}
            className={`${styles.networkBar} ${level <= barLevel ? styles.networkBarActive : ''}`}
            style={{ height: `${6 + barLevel * 2}px` }}
          />
        ))}
        <span className={styles.networkQualityText}>
          {NetworkQualityText[level]}
        </span>
      </div>
    );
  };

  // Add audio level indicators for remote users
  const renderRemoteAudioLevel = () => {
    if (!remoteUsers || remoteUsers.length === 0 || callStatus !== "connected") {
      return null;
    }

    // Generate a simulated audio level visualization for now
    // In a full implementation, you would use the actual audio level from Agora SDK
    const audioLevels = [];
    for (let i = 0; i < 5; i++) {
      const height = Math.random() > 0.5 ? Math.random() * 15 : 3;
      audioLevels.push(height);
    }

    return (
      <div className={styles.audioContainer}>
        <div className={styles.remoteAudioLevelContainer}>
          {audioLevels.map((height, index) => (
            <div 
              key={`audio-bar-${index}`}
              className={styles.audioLevelBar} 
              style={{ 
                height: `${height}px`,
                backgroundColor: theme.palette.themeDark,
                marginLeft: '2px',
                width: '3px'
              }} 
            />
          ))}
        </div>
      </div>
    );
  };

  // Only render if there's an active call
  if (!activeCall) return null

  return (
    <Dialog
      hidden={!isDialogOpen}
      onDismiss={handleDismiss}
      dialogContentProps={{
        type: DialogType.normal,
        showCloseButton: false,
        title: null,
        subText: null,
      }}
      modalProps={{
        isBlocking: true,
        styles: { main: styles.callDialog },
      }}
    >
      {/* Call Header with user info */}
      <div className={styles.callHeader}>
        <div className={styles.personaContainer}>
          <Persona
            imageInitials={activeCall.peerName
              ?.split(" ")
              .map((n) => n[0])
              .join("")}
            text={activeCall.peerName}
            size={PersonaSize.size72}
            hidePersonaDetails
            styles={{
              root: { justifyContent: "center" },
            }}
          />
        </div>
        <Text className={styles.callStatusText}>
          {CallStatusText[callStatus]}
          {activeCall?.isCrossPlatform && (
            <span style={{ marginLeft: '8px', fontSize: '12px', color: '#0078d4' }}>
              (Cross-platform)
            </span>
          )}
        </Text>

        {callStatus === "connected" && (
          <Text className={styles.callDurationText}>
            {formatCallDuration(callDuration)}
          </Text>
        )}

        {callStatus === "connecting" && connectionState && (
          <Text className={styles.connectionStateText}>
            Connection: {connectionState}
          </Text>
        )}

        {activeCall?.isCrossPlatform && callStatus === "connecting" && (
          <Text className={styles.connectionStateText}>
            Notifying mobile device...
          </Text>
        )}
        
        {networkQuality && renderNetworkQuality(networkQuality)}

        {/* Audio level visualization for local audio */}
        {callStatus === "connected" && !isMuted && localTrackState.audioTrackEnabled && (
          <div className={styles.audioContainer}>
            <div className={styles.audioLevelIndicator}>
              <div className={styles.audioLevelBar} style={{ height: `${audioLevel * 15}px` }} />
            </div>
          </div>
        )}
        
        {/* Remote user audio level indicator */}
        {callStatus === "connected" && remoteUsers && remoteUsers.length > 0 && renderRemoteAudioLevel()}
      </div>

      {/* Call Body */}
      <div className={styles.callBody}>
        {/* Show spinner when connecting */}
        {callStatus === "connecting" && (
          <>
            <Spinner size={SpinnerSize.large} label="Establishing connection..." className={styles.connectingSpinner} />
            {longConnecting && (
              <Text className={styles.helpText}>
                Taking longer than expected. The recipient may be unavailable or there might be connection issues.
              </Text>
            )}
          </>
        )}

        {/* Call controls */}
        <div className={styles.callControls}>
          {/* Mute Button */}
          <IconButton
            className={`${styles.controlButton} ${isMuted ? styles.mutedButton : styles.muteButton}`}
            iconProps={{ iconName: isMuted ? "MicOff" : "Microphone", styles: styles.iconStyles }}
            onClick={onToggleMute}
            title={isMuted ? "Unmute" : "Mute"}
            disabled={callStatus !== "connected" || !localTrackState.audioTrackEnabled}
          />

          {/* Enable/Disable Audio */}
          <IconButton
            className={`${styles.controlButton} ${localTrackState.audioTrackEnabled ? styles.muteButton : styles.disabledButton}`}
            iconProps={{
              iconName: localTrackState.audioTrackEnabled ? "PlugConnected" : "PlugDisconnected",
              styles: styles.iconStyles,
            }}
            onClick={() => onSetAudioEnabled(!localTrackState.audioTrackEnabled)}
            title={localTrackState.audioTrackEnabled ? "Disable Audio" : "Enable Audio"}
            disabled={callStatus !== "connected"}
          />

          {/* End Call Button */}
          <IconButton
            className={`${styles.controlButton} ${styles.endCallButton}`}
            iconProps={{ iconName: "DeclineCall", styles: styles.iconStyles }}
            onClick={onEndCall}
            title="End Call"
          />
        </div>

        {/* Call properties (connection details) */}
        {callStatus === "connected" && (
          <div className={styles.callProperties}>
            <div className={styles.propertyRow}>
              <span className={styles.propertyLabel}>Call Type</span>
              <span className={styles.propertyValue}>Voice Call</span>
            </div>
            <div className={styles.propertyRow}>
              <span className={styles.propertyLabel}>Local Microphone</span>
              <span
                className={styles.propertyValue}
                style={{
                  color: isMuted
                    ? theme.palette.redDark
                    : localTrackState.audioTrackEnabled
                      ? theme.palette.green
                      : theme.palette.neutralTertiary,
                }}
              >
                {isMuted ? "Muted" : localTrackState.audioTrackEnabled ? "Active" : "Disabled"}
              </span>
            </div>
            <div className={styles.propertyRow}>
              <span className={styles.propertyLabel}>Remote Audio</span>
              <span 
                className={styles.propertyValue}
                style={{
                  color: remoteUsers.length > 0 
                    ? theme.palette.green 
                    : theme.palette.neutralTertiary,
                }}
              >
                {remoteUsers.length > 0 
                  ? remoteUsers[0].hasAudio 
                    ? "Connected & Active" 
                    : "Connected (Muted)" 
                  : "Waiting..."}
              </span>
            </div>
            
            {/* Display remote audio track info if available */}
            {remoteUsers.length > 0 && remoteUsers[0].audioTrack && (
              <div className={styles.propertyRow}>
                <span className={styles.propertyLabel}>Remote Track</span>
                <span 
                  className={styles.propertyValue}
                  style={{
                    color: remoteUsers[0].audioTrack.isPlaying 
                      ? theme.palette.green 
                      : theme.palette.neutralTertiary,
                  }}
                >
                  {remoteUsers[0].audioTrack.isPlaying 
                    ? "Playing" 
                    : "Not Playing"}
                </span>
              </div>
            )}

            <div className={styles.propertyRow}>
              <span className={styles.propertyLabel}>Channel</span>
              <span className={styles.propertyValue} style={{ fontSize: "10px" }}>
                {activeCall.channelName.substring(0, 12)}
              </span>
            </div>
            <div className={styles.propertyRow}>
              <span className={styles.propertyLabel}>Network</span>
              <span 
                className={styles.propertyValue}
                style={{
                  color: networkQuality?.uplinkNetworkQuality <= 3 
                    ? theme.palette.green 
                    : networkQuality?.uplinkNetworkQuality <= 4
                      ? theme.palette.themeDarkAlt
                      : theme.palette.redDark,
                }}
              >
                {networkQuality 
                  ? NetworkQualityText[networkQuality.uplinkNetworkQuality] || "Unknown"
                  : "Unknown"}
              </span>
            </div>
          </div>
        )}
      </div>

      {/* Dialog Footer */}
      {/* <Stack horizontal horizontalAlign="center" styles={{ root: { padding: "0 20px 20px" } }}>
        <StackItem>
          <PrimaryButton onClick={onEndCall} text="End Call" />
        </StackItem>
      </Stack> */}
    </Dialog>
  )
}

export default CallComponent
