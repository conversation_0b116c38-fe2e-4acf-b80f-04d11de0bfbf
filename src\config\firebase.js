import { initializeApp } from 'firebase/app';
import { getMessaging, getToken, onMessage } from 'firebase/messaging';

// Firebase configuration
// Note: These should be moved to environment variables in production
const firebaseConfig = {
  apiKey: "AIzaSyAIkEyJ_S8vToYMO2b4UBFTTdb6fOpSSdQ",
  authDomain: "cmt-meet-fd921.firebaseapp.com",
  projectId: "cmt-meet-fd921",
  storageBucket: "cmt-meet-fd921.firebasestorage.app",
  messagingSenderId: "953784194836",
  appId: "1:953784194836:web:9fbc8a15ba903d49c51401"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Cloud Messaging and get a reference to the service
const messaging = getMessaging(app);

export { messaging, getToken, onMessage };
export default app;
