import AgoraRTM from "agora-rtm-sdk"

// Static registry of active CallService instances to prevent duplicates
const activeCallServices = {}

/**
 * Call Service for managing real-time signaling for voice calls
 * This service handles the signaling part of voice calls using Agora RTM
 */
export class CallService {
  // Static method to get an existing instance or create a new one
  static getInstance(userId, rtmToken) {
    const instanceKey = `${userId}_${rtmToken?.substring(0, 10)}`

    // Return existing instance if available
    if (activeCallServices[instanceKey]) {
      console.log(`Reusing existing CallService instance for ${userId}`)
      return activeCallServices[instanceKey]
    }

    // Create new instance
    console.log(`Creating new CallService instance for ${userId}`)
    const instance = new CallService(userId, rtmToken)
    activeCallServices[instanceKey] = instance

    return instance
  }

  /**
   * Get the RTM client instance
   * This allows other services to use the same RTM client
   * @returns {Object} The RTM client instance
   */
  getRTMClient() {
    if (!this.rtmClient) {
      console.warn("RTM client not initialized yet")
    }
    return this.rtmClient
  }

  /**
   * Check if the RTM client is initialized and logged in
   * @returns {boolean} Whether the RTM client is ready
   */
  isRTMClientReady() {
    return this.rtmClient !== null && this.rtmClient.connectionState === "CONNECTED"
  }

  /**
   * Wait for the RTM client to be initialized and logged in
   * @param {number} timeoutMs - Timeout in milliseconds
   * @returns {Promise<boolean>} Whether the RTM client is ready
   */
  async waitForRTMClientReady(timeoutMs = 5000) {
    if (this.isRTMClientReady()) {
      return true
    }

    return new Promise((resolve) => {
      const startTime = Date.now()
      const checkInterval = setInterval(() => {
        if (this.isRTMClientReady()) {
          clearInterval(checkInterval)
          resolve(true)
        } else if (Date.now() - startTime > timeoutMs) {
          clearInterval(checkInterval)
          console.warn("Timeout waiting for RTM client to be ready")
          resolve(false)
        }
      }, 100)
    })
  }

  constructor(userId, rtmToken) {
    this.userId = userId
    this.rtmToken = rtmToken
    this.rtmClient = null
    this.callListeners = {
      incomingCall: [],
      callAccepted: [],
      callRejected: [],
      callEnded: [],
      callMissed: [],
    }

    // Add an instance ID for debugging
    this.instanceId = `call_service_${userId}_${Date.now()}`
    console.log(`Creating new CallService instance: ${this.instanceId}`)

    this.initialize()
  }

  // Update the CallService to properly handle RTM token and connection

  // Fix the initialize method to properly use the token
  async initialize() {
    try {
      console.log(`Initializing CallService instance: ${this.instanceId}`)

      // Create RTM client 
      // Dev
      // this.rtmClient = AgoraRTM.createInstance("********************************")

      // Pro
      this.rtmClient = AgoraRTM.createInstance("********************************")

      // Set up event listeners
      this.setupEventListeners()

      // Login to RTM
      console.log(`RTM login attempt for instance ${this.instanceId}:`, {
        userId: this.userId.toString(),
        tokenLength: this.rtmToken ? this.rtmToken.length : 0,
        tokenPreview: this.rtmToken ? `${this.rtmToken.substring(0, 10)}...` : "null",
      })

      // Login to RTM - ensure userId is a string and rtmToken is valid
      await this.rtmClient.login({
        uid: this.userId.toString(),
        token: this.rtmToken, // Use the provided token instead of hardcoded one
      })

      console.log(`RTM client initialized for CallService instance: ${this.instanceId}, user: ${this.userId}`)
    } catch (error) {
      console.error(`Failed to initialize RTM client for CallService instance ${this.instanceId}:`, error)
      throw error
    }
  }

  // Improve the connection state handling
  setupEventListeners() {
    if (!this.rtmClient) return

    // Listen for messages
    this.rtmClient.on("MessageFromPeer", async ({ text }, peerId) => {
      try {
        console.log(`Received RTM message from peer ${peerId}:`, text)

        // Add more detailed logging for debugging
        console.log(`[RTM DEBUG] Message received at ${new Date().toISOString()}`)
        console.log(`[RTM DEBUG] Message length: ${text.length} bytes`)
        console.log(`[RTM DEBUG] Sender ID: ${peerId}`)
        console.log(`[RTM DEBUG] Receiver ID: ${this.userId}`)
        console.log(`[RTM DEBUG] RTM Client state: ${this.rtmClient.connectionState}`)

        const message = JSON.parse(text)

        // Log the parsed message for debugging
        console.log(`[RTM DEBUG] Parsed message:`, JSON.stringify(message, null, 2))

        // Process message based on type
        switch (message.type) {
          case "call_request":
            console.log(`[RTM DEBUG] Processing call_request from ${peerId}`)
            console.log(`[RTM DEBUG] Is video call: ${!!message.isVideoCall}`)
            this.handleIncomingCall(message, peerId)
            break
          case "call_accept":
            console.log(`[CRITICAL] Received call_accept for call ${message.callId} from ${peerId}`)
            // Acknowledge the acceptance immediately to ensure both sides know
            this.sendAckMessage(message.callId, peerId, "accept_ack")
            this.handleCallAccepted(message)
            break
          case "call_reject":
            this.handleCallRejected(message)
            break
          case "call_end":
            this.handleCallEnded(message)
            break
          case "call_busy":
            this.handleCallBusy(message)
            break
          case "accept_ack":
            console.log(`[CRITICAL] Received accept_ack for call ${message.callId} - sender knows we accepted`)
            break
          default:
            // Ignore other message types
            console.log("Ignoring unknown message type:", message.type)
            break
        }
      } catch (error) {
        console.error("Error processing RTM message:", error)
      }
    })

    // Connection state change events
    this.rtmClient.on("ConnectionStateChanged", (newState, reason) => {
      console.log("RTM connection state changed to:", newState, "reason:", reason)

      // Handle reconnection if needed
      if (newState === "DISCONNECTED" || newState === "ABORTED") {
        // Try to reconnect after a delay
        setTimeout(() => {
          console.log("Attempting to reconnect RTM client...")
          this.rtmClient
            .login({ uid: this.userId.toString(), token: this.rtmToken })
            .then(() => console.log("RTM client reconnected successfully"))
            .catch((err) => console.error("Failed to reconnect RTM client:", err))
        }, 2000)
      }
    })
  }

  /**
   * Send an acknowledgment message
   * This helps with debugging and ensuring messages are received
   */
  async sendAckMessage(callId, peerId, type) {
    try {
      if (!this.rtmClient) {
        console.error("Cannot send ack - RTM client not initialized")
        return
      }

      const ackMessage = {
        type,
        callId,
        timestamp: Date.now()
      }

      console.log(`[ACK] Sending ${type} acknowledgment for call ${callId} to ${peerId}`)
      console.log(`[ACK] RTM Client state: ${this.rtmClient.connectionState}`)

      // Send with max priority
      await this.rtmClient.sendMessageToPeer(
        { text: JSON.stringify(ackMessage) },
        peerId.toString(),
        { enableOfflineMessaging: true, enableHistoricalMessaging: true }
      )

      console.log(`[ACK] Successfully sent ${type} acknowledgment`)
    } catch (error) {
      console.error(`Error sending ${type}:`, error)
    }
  }

  /**
   * Handle incoming call request
   */
  handleIncomingCall(message, callerId) {
    const callData = {
      callId: message.callId,
      callerId: callerId,
      callerName: message.callerName,
      channelName: message.channelName,
      timestamp: message.timestamp,
      isVideoCall: !!message.isVideoCall, // Add flag to indicate if this is a video call
    }

    console.log("Handling incoming call:", callData)
    console.log(`[INCOMING CALL] Call type: ${callData.isVideoCall ? 'Video' : 'Audio'}`)
    console.log(`[INCOMING CALL] Number of registered listeners: ${this.callListeners.incomingCall.length}`)

    // Send an immediate acknowledgment to the caller
    this.sendAckMessage(message.callId, callerId, "call_request_received")

    // Notify listeners with a small delay to ensure UI is ready
    setTimeout(() => {
      console.log(`[INCOMING CALL] Notifying ${this.callListeners.incomingCall.length} listeners about incoming call`)
      this.callListeners.incomingCall.forEach((listener, index) => {
        try {
          console.log(`[INCOMING CALL] Notifying listener ${index + 1}`)
          listener(callData)
          console.log(`[INCOMING CALL] Listener ${index + 1} notified successfully`)
        } catch (err) {
          console.error(`[INCOMING CALL] Error notifying listener ${index + 1}:`, err)
        }
      })
    }, 100)
  }

  /**
   * Handle accepted call
   */
  handleCallAccepted(message) {
    console.log(`[CRITICAL] Call ${message.callId} was accepted - notifying listeners`)

    // Add a short delay to ensure both sides have time to initialize
    setTimeout(() => {
      console.log(`[CRITICAL] Executing delayed listener notification for call ${message.callId}`)
      // Pass the entire message, not just the ID, for better tracing
      this.callListeners.callAccepted.forEach((listener) => listener(message))
    }, 300)
  }

  /**
   * Handle rejected call
   */
  handleCallRejected(message) {
    console.log("Call was rejected:", message.callId)
    // Notify listeners
    this.callListeners.callRejected.forEach((listener) => listener(message.callId))
  }

  /**
   * Handle ended call
   */
  handleCallEnded(message) {
    console.log(`[END CALL HANDLER] Call ${message.callId} was ended ${message.isBackup ? '(backup signal)' : ''}`);

    // Log timing information if available
    if (message.timestamp) {
      const delay = Date.now() - message.timestamp;
      console.log(`[END CALL HANDLER] Signal delay: ${delay}ms`);
    }

    // Notify listeners
    this.callListeners.callEnded.forEach((listener) => {
      try {
        listener(message.callId);
      } catch (err) {
        console.error(`[END CALL HANDLER] Error in listener:`, err);
      }
    });

    console.log(`[END CALL HANDLER] All end call listeners notified for call ${message.callId}`);
  }

  /**
   * Handle busy response
   */
  handleCallBusy(message) {
    console.log("Recipient is busy:", message.callId)
    // Notify listeners that the person is busy
    this.callListeners.callRejected.forEach((listener) => listener(message.callId, "busy"))
  }

  /**
   * Initiate a call to a recipient
   */
  async initiateCall({ callId, channelName, recipientId, callerId, callerName, timestamp, isVideoCall }) {
    try {
      if (!this.rtmClient) {
        throw new Error("RTM client not initialized")
      }

      // Create call request message
      const callRequest = {
        type: "call_request",
        callId,
        channelName,
        callerId,
        callerName,
        timestamp,
        isVideoCall: !!isVideoCall, // Add flag to indicate if this is a video call
      }

      console.log("Sending call request:", callRequest, "to recipient:", recipientId)

      // Send message to recipient with retry logic
      const maxRetries = 3;
      let retryCount = 0;
      let success = false;

      while (!success && retryCount < maxRetries) {
        try {
      // Send message to recipient
          await this.rtmClient.sendMessageToPeer(
            { text: JSON.stringify(callRequest) },
            recipientId.toString(),
            { enableOfflineMessaging: true, enableHistoricalMessaging: true }
          );
          success = true;
        } catch (err) {
          retryCount++;
          console.warn(`RTM send attempt ${retryCount} failed:`, err);

          if (retryCount < maxRetries) {
            // Wait before retry
            await new Promise(resolve => setTimeout(resolve, 1000));
          } else {
            throw err;
          }
        }
      }

      return true
    } catch (error) {
      console.error("Error initiating call:", error)
      throw error
    }
  }

  /**
   * Accept an incoming call
   */
  async acceptCall(callId, callerId) {
    try {
      if (!this.rtmClient) {
        throw new Error("RTM client not initialized")
      }

      // Create accept message
      const acceptMessage = {
        type: "call_accept",
        callId,
        timestamp: Date.now()
      }

      console.log(`[CRITICAL] Sending call accept for callId ${callId} to caller ${callerId}`)

      // Explicitly trigger self-notification first to start local connection
      // This ensures the receiver begins connecting even if signaling fails
      console.log(`[CRITICAL] Pre-emptively self-notifying about acceptance for ${callId}`)
      this.handleCallAccepted(acceptMessage)

      // Send message with retry logic - more retries and shorter initial delay
      const maxRetries = 8;
      let retryCount = 0;
      let success = false;

      while (!success && retryCount < maxRetries) {
        try {
          // Send message to caller with high priority
          await this.rtmClient.sendMessageToPeer(
            { text: JSON.stringify(acceptMessage) },
            callerId.toString(),
            { enableOfflineMessaging: true, enableHistoricalMessaging: true }
          );
          success = true;

          console.log(`[CRITICAL] Successfully sent call accept message for ${callId}`);
        } catch (err) {
          retryCount++;
          console.warn(`[CRITICAL] RTM accept send attempt ${retryCount} failed:`, err);

          if (retryCount < maxRetries) {
            // Exponential backoff for retries, but start with shorter delay
            const delay = 200 * Math.pow(1.5, retryCount);
            console.log(`Retrying in ${delay}ms...`);
            await new Promise(resolve => setTimeout(resolve, delay));
          } else {
            throw err;
          }
        }
      }

      return true
    } catch (error) {
      console.error("[CRITICAL] Error accepting call:", error)
      throw error
    }
  }

  /**
   * Reject an incoming call
   */
  async rejectCall(callId, callerId) {
    try {
      if (!this.rtmClient) {
        throw new Error("RTM client not initialized")
      }

      // Create reject message
      const rejectMessage = {
        type: "call_reject",
        callId,
      }

      console.log("Sending call reject:", rejectMessage, "to caller:", callerId)

      // Send message with retry logic
      const maxRetries = 3;
      let retryCount = 0;
      let success = false;

      while (!success && retryCount < maxRetries) {
        try {
      // Send message to caller
          await this.rtmClient.sendMessageToPeer(
            { text: JSON.stringify(rejectMessage) },
            callerId.toString(),
            { enableOfflineMessaging: true }
          );
          success = true;
        } catch (err) {
          retryCount++;
          console.warn(`RTM reject send attempt ${retryCount} failed:`, err);

          if (retryCount < maxRetries) {
            await new Promise(resolve => setTimeout(resolve, 1000));
          } else {
            throw err;
          }
        }
      }

      return true
    } catch (error) {
      console.error("Error rejecting call:", error)
      return false
    }
  }

  /**
   * End an active call with improved reliability
   */
  async endCall(callId, peerId) {
    try {
      if (!this.rtmClient) {
        throw new Error("RTM client not initialized")
      }

      // Validate parameters
      if (!callId) {
        console.warn("Missing callId when ending call");
        return false;
      }

      if (!peerId) {
        console.warn("Missing peerId when ending call");
        return false;
      }

      // Create end call message
      const endCallMessage = {
        type: "call_end",
        callId,
        timestamp: Date.now(), // Add timestamp for tracing
        forceEnd: true // Flag to indicate this should force end the call
      }

      console.log(`[END CALL SIGNAL] Sending call end for call ${callId} to peer ${peerId}`);

      // Send message with enhanced retry logic
      const maxRetries = 5; // Increase retries for this critical message
      let retryCount = 0;
      let success = false;

      // Make sure we have a valid string format for the peer ID
      const peerIdString = String(peerId).trim();

      if (!peerIdString) {
        console.warn("[END CALL SIGNAL] Invalid peer ID format when ending call");
        return false;
      }

      while (!success && retryCount < maxRetries) {
        try {
          console.log(`[END CALL SIGNAL] Attempt ${retryCount + 1} to send end call signal to ${peerIdString}`);

          // Send message to peer with highest priority options
          await this.rtmClient.sendMessageToPeer(
            { text: JSON.stringify(endCallMessage) },
            peerIdString,
            {
              enableOfflineMessaging: true,
              enableHistoricalMessaging: true
            }
          );

          success = true;
          console.log(`[END CALL SIGNAL] Successfully sent end call signal on attempt ${retryCount + 1}`);

          // Send multiple backup messages to increase reliability
          // This helps with network issues and potential message loss
          for (let backupAttempt = 0; backupAttempt < 3; backupAttempt++) {
            try {
              console.log(`[END CALL SIGNAL] Sending backup end call signal #${backupAttempt + 1}`);
              setTimeout(async () => {
                try {
                  await this.rtmClient.sendMessageToPeer(
                    { text: JSON.stringify({
                      ...endCallMessage,
                      isBackup: true,
                      backupAttempt: backupAttempt + 1
                    }) },
                    peerIdString,
                    { enableOfflineMessaging: true }
                  );
                  console.log(`[END CALL SIGNAL] Backup end call signal #${backupAttempt + 1} sent`);
                } catch (err) {
                  console.warn(`[END CALL SIGNAL] Error sending backup signal #${backupAttempt + 1}:`, err);
                }
              }, 500 * (backupAttempt + 1)); // Stagger backup messages
            } catch (err) {
              console.warn(`[END CALL SIGNAL] Error scheduling backup signal #${backupAttempt + 1}:`, err);
            }
          }
        } catch (err) {
          retryCount++;
          console.warn(`[END CALL SIGNAL] Send attempt ${retryCount} failed:`, err);

          if (retryCount < maxRetries) {
            // Exponential backoff with jitter for retries
            const delay = Math.min(1000 * Math.pow(1.5, retryCount) + Math.random() * 300, 8000);
            console.log(`[END CALL SIGNAL] Retrying in ${Math.round(delay)}ms...`);
            await new Promise(resolve => setTimeout(resolve, delay));
          } else {
            throw err;
          }
        }
      }

      // Self-process end signal to ensure local state is properly updated
      // This ensures that even if the other side can't receive our signal, we still update our state
      this.handleCallEnded(endCallMessage);

      return true
    } catch (error) {
      console.error("[END CALL SIGNAL] Error ending call:", error)
      return false
    }
  }

  /**
   * Send busy status to a caller
   */
  async sendBusyStatus(callId, callerId) {
    try {
      if (!this.rtmClient) {
        throw new Error("RTM client not initialized")
      }

      // Create busy message
      const busyMessage = {
        type: "call_busy",
        callId,
      }

      console.log("Sending busy status:", busyMessage, "to caller:", callerId)

      // Send message to caller
      await this.rtmClient.sendMessageToPeer({ text: JSON.stringify(busyMessage) }, callerId.toString())

      return true
    } catch (error) {
      console.error("Error sending busy status:", error)
      return false
    }
  }

  /**
   * Register a callback for incoming calls
   */
  onIncomingCall(callback) {
    this.callListeners.incomingCall.push(callback)
  }

  /**
   * Unregister a callback for incoming calls
   */
  offIncomingCall(callback) {
    this.callListeners.incomingCall = this.callListeners.incomingCall.filter(cb => cb !== callback)
  }

  /**
   * Register a callback for when calls are accepted
   */
  onCallAccepted(callback) {
    this.callListeners.callAccepted.push(callback)
  }

  /**
   * Unregister a callback for when calls are accepted
   */
  offCallAccepted(callback) {
    this.callListeners.callAccepted = this.callListeners.callAccepted.filter(cb => cb !== callback)
  }

  /**
   * Register a callback for when calls are rejected
   */
  onCallRejected(callback) {
    this.callListeners.callRejected.push(callback)
  }

  /**
   * Unregister a callback for when calls are rejected
   */
  offCallRejected(callback) {
    this.callListeners.callRejected = this.callListeners.callRejected.filter(cb => cb !== callback)
  }

  /**
   * Register a callback for when calls are ended by the peer
   */
  onCallEnded(callback) {
    this.callListeners.callEnded.push(callback)
  }

  /**
   * Unregister a callback for when calls are ended by the peer
   */
  offCallEnded(callback) {
    this.callListeners.callEnded = this.callListeners.callEnded.filter(cb => cb !== callback)
  }

  /**
   * Register a callback for missed calls
   */
  onCallMissed(callback) {
    this.callListeners.callMissed.push(callback)
  }

  /**
   * Unregister a callback for missed calls
   */
  offCallMissed(callback) {
    this.callListeners.callMissed = this.callListeners.callMissed.filter(cb => cb !== callback)
  }

  /**
   * Clean up resources
   */
  async cleanup() {
    try {
      console.log(`Cleaning up CallService instance: ${this.instanceId}`)

      // Remove from static registry
      const instanceKey = `${this.userId}_${this.rtmToken?.substring(0, 10)}`
      if (activeCallServices[instanceKey] === this) {
        delete activeCallServices[instanceKey]
        console.log(`Removed CallService instance from registry: ${this.instanceId}`)
      }

      // Clear all listeners to prevent memory leaks
      this.callListeners = {
        incomingCall: [],
        callAccepted: [],
        callRejected: [],
        callEnded: [],
        callMissed: [],
      }

      if (this.rtmClient) {
        // Logout from RTM
        await this.rtmClient.logout()
        console.log(`RTM client logged out for CallService instance: ${this.instanceId}`)

        // Remove all event listeners
        this.rtmClient.removeAllListeners()

        // Set to null to aid garbage collection
        this.rtmClient = null
      }
    } catch (error) {
      console.error(`Error cleaning up CallService instance ${this.instanceId}:`, error)
    }
  }
}
