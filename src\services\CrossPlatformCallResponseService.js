/**
 * CrossPlatformCallResponseService
 * 
 * Handles bidirectional call responses between web and mobile platforms.
 * This service manages call acceptance, rejection, busy, and cancellation responses
 * for cross-platform calls using FCM notifications.
 */

class CrossPlatformCallResponseService {
  constructor() {
    this.responseHandlers = new Map();
    this.pendingCalls = new Map();
    this.fcmProjectId = 'cmt-meet-fd921';
    this.fcmEndpoint = `https://fcm.googleapis.com/v1/projects/${this.fcmProjectId}/messages:send`;
  }

  /**
   * Register a response handler for a specific call
   */
  registerResponseHandler(callId, handler) {
    console.log(`Registering response handler for call: ${callId}`);
    this.responseHandlers.set(callId, handler);
  }

  /**
   * Unregister a response handler
   */
  unregisterResponseHandler(callId) {
    console.log(`Unregistering response handler for call: ${callId}`);
    this.responseHandlers.delete(callId);
    this.pendingCalls.delete(callId);
  }

  /**
   * Store pending call information
   */
  storePendingCall(callId, callData) {
    console.log(`Storing pending call data for: ${callId}`);
    this.pendingCalls.set(callId, {
      ...callData,
      timestamp: Date.now()
    });
  }

  /**
   * Get pending call information
   */
  getPendingCall(callId) {
    return this.pendingCalls.get(callId);
  }

  /**
   * Send call response via FCM
   */
  async sendCallResponse(callData, responseType) {
    try {
      console.log(`Sending ${responseType} response for call: ${callData.callId}`);

      if (!callData.callerFCMToken || !callData.jwtToken) {
        throw new Error('Missing FCM token or JWT token for response');
      }

      const fcmPayload = {
        message: {
          token: callData.callerFCMToken,
          data: {
            callerAgoraId: callData.callerId,
            callerName: callData.callerName,
            channelName: callData.channelName,
            callUUID: callData.callId,
            status: responseType.toUpperCase(),
            type: 'CALL_RESPONSE',
            callType: callData.isVideoCall ? 'video' : 'audio',
            click_action: 'FLUTTER_NOTIFICATION_CLICK'
          },
          android: {
            priority: 'high'
          },
          apns: {
            headers: { 'apns-priority': '10' },
            payload: {
              aps: { 
                'content-available': 1, 
                sound: responseType === 'ACCEPTED' ? 'default' : 'default', 
                badge: responseType === 'ACCEPTED' ? 1 : 0 
              },
              voip: '1'
            }
          }
        }
      };

      const response = await fetch(this.fcmEndpoint, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${callData.jwtToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(fcmPayload)
      });

      if (response.ok) {
        console.log(`${responseType} response sent successfully`);
        return { success: true };
      } else {
        const errorText = await response.text();
        console.error(`Failed to send ${responseType} response:`, errorText);
        return { success: false, error: errorText };
      }
    } catch (error) {
      console.error(`Error sending ${responseType} response:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Handle incoming call response from mobile
   */
  handleIncomingResponse(payload) {
    try {
      const callId = payload.data?.callUUID;
      const status = payload.data?.status;

      if (!callId || !status) {
        console.warn('Invalid response payload:', payload);
        return;
      }

      console.log(`Received ${status} response for call: ${callId}`);

      // Get the response handler for this call
      const handler = this.responseHandlers.get(callId);
      if (handler) {
        handler({
          callId,
          status: status.toLowerCase(),
          payload: payload.data
        });
      } else {
        console.warn(`No response handler found for call: ${callId}`);
      }

      // Clean up if call is ended
      if (['DECLINED', 'CANCELLED', 'ENDED'].includes(status)) {
        this.unregisterResponseHandler(callId);
      }
    } catch (error) {
      console.error('Error handling incoming response:', error);
    }
  }

  /**
   * Accept a cross-platform call
   */
  async acceptCall(callData) {
    return await this.sendCallResponse(callData, 'ACCEPTED');
  }

  /**
   * Reject a cross-platform call
   */
  async rejectCall(callData) {
    return await this.sendCallResponse(callData, 'DECLINED');
  }

  /**
   * Cancel a cross-platform call
   */
  async cancelCall(callData) {
    return await this.sendCallResponse(callData, 'CANCELLED');
  }

  /**
   * Mark call as busy
   */
  async busyCall(callData) {
    return await this.sendCallResponse(callData, 'BUSY');
  }

  /**
   * End a cross-platform call
   */
  async endCall(callData) {
    return await this.sendCallResponse(callData, 'ENDED');
  }

  /**
   * Clean up expired pending calls
   */
  cleanupExpiredCalls(maxAge = 60000) { // 1 minute default
    const now = Date.now();
    for (const [callId, callData] of this.pendingCalls.entries()) {
      if (now - callData.timestamp > maxAge) {
        console.log(`Cleaning up expired call: ${callId}`);
        this.unregisterResponseHandler(callId);
      }
    }
  }

  /**
   * Get all pending calls
   */
  getAllPendingCalls() {
    return Array.from(this.pendingCalls.values());
  }

  /**
   * Clear all pending calls and handlers
   */
  clearAll() {
    console.log('Clearing all pending calls and response handlers');
    this.responseHandlers.clear();
    this.pendingCalls.clear();
  }
}

// Create and export singleton instance
const crossPlatformCallResponseService = new CrossPlatformCallResponseService();

export default crossPlatformCallResponseService;
