import AgoraRTC from "agora-rtc-sdk-ng";

// Static registry of active VideoCallService instances to prevent duplicates
const activeVideoCallServices = {};

/**
 * Video Call Service for managing real-time video calls
 * This service extends the existing call functionality to support video
 */
export class VideoCallService {
  // Static method to get an existing instance or create a new one
  static getInstance(userId, rtcToken, callService) {
    const instanceKey = `${userId}_${rtcToken?.substring(0, 10)}`;

    // Return existing instance if available
    if (activeVideoCallServices[instanceKey]) {
      console.log(`Reusing existing VideoCallService instance for ${userId}`);
      const instance = activeVideoCallServices[instanceKey];

      // Update the call service reference if provided
      if (callService && instance.callService !== callService) {
        console.log(`Updating CallService reference for VideoCallService instance: ${instance.instanceId}`);
        instance.callService = callService;
      }

      return instance;
    }

    // Create new instance
    console.log(`Creating new VideoCallService instance for ${userId}`);
    const instance = new VideoCallService(userId, rtcToken, callService);
    activeVideoCallServices[instanceKey] = instance;

    return instance;
  }

  constructor(userId, rtcToken, callService) {
    this.userId = userId;
    this.rtcToken = rtcToken;
    this.callService = callService; // Store reference to CallService for RTM signaling
    this.client = null;
    this.localVideoTrack = null;
    this.localAudioTrack = null;
    this.remoteUsers = [];
    this.isInitialized = false;
    this.currentChannelName = null; // Track current channel for token renewal

    // Add a map to track video availability separately from the Agora user objects
    this.videoAvailabilityMap = new Map();

    // Add an instance ID for debugging
    this.instanceId = `video_call_service_${userId}_${Date.now()}`;
    console.log(`Creating new VideoCallService instance: ${this.instanceId}`);
  }

  /**
   * Initialize the video call service
   */
  async initialize() {
    if (this.isInitialized) {
      console.log(`VideoCallService already initialized: ${this.instanceId}`);
      return;
    }

    try {
      console.log(`Initializing VideoCallService instance: ${this.instanceId}`);

      // Verify that we have a valid CallService instance for RTM signaling
      if (!this.callService) {
        console.warn(`No CallService provided to VideoCallService instance: ${this.instanceId}`);
        console.warn("Video calls will work but signaling (call initiation, etc.) may fail");
      } else {
        // Wait for RTM client to be ready
        const rtmReady = await this.callService.waitForRTMClientReady();
        if (!rtmReady) {
          console.warn("RTM client not ready, video call signaling may not work properly");
        }
      }

      // Create RTC client with optimal video configurations for smoother streaming
      this.client = AgoraRTC.createClient({
        mode: "rtc", // Using RTC mode for real-time communication (video calls)
        codec: "h264", // Use H.264 for better compatibility and quality
        enableAudioVolumeIndicator: true,
        // Additional options for better quality
        streamFallbackOption: 1, // Automatically downgrade stream quality if network is poor
        enableDualStream: true, // Enable dual stream mode for better adaptability
      });

      // Set up event listeners
      this.setupEventListeners();

      this.isInitialized = true;
      console.log(`VideoCallService initialized: ${this.instanceId}`);
    } catch (error) {
      console.error(`Failed to initialize VideoCallService: ${error}`);
      throw error;
    }
  }

  /**
   * Set up event listeners for the RTC client
   */
  setupEventListeners() {
    if (!this.client) return;

    // Handle remote user publishing tracks
    this.client.on("user-published", async (user, mediaType) => {
      console.log(`Remote user ${user.uid} published ${mediaType} track`);

      try {
        // Subscribe to the remote user
        await this.client.subscribe(user, mediaType);
        console.log(`Successfully subscribed to ${mediaType} track of user ${user.uid}`);

        if (mediaType === "video") {
          // Add user to remote users array if not already present
          const userIndex = this.remoteUsers.findIndex(u => u.uid === user.uid);

          if (userIndex === -1) {
            // Add new user
            this.remoteUsers.push(user);
            console.log(`Added user ${user.uid} to remote users array. Total: ${this.remoteUsers.length}`);
          } else {
            // Update existing user reference to ensure it has the latest tracks
            this.remoteUsers[userIndex] = user;
            console.log(`Updated existing user ${user.uid} in remote users array`);
          }

          // Track video availability in our separate map instead of modifying the user object
          if (user.videoTrack) {
            // Store video availability in our map
            this.videoAvailabilityMap.set(user.uid, true);
            console.log(`Remote video track ready for user ${user.uid}. Will be played by UI component.`);
          }
        }

        if (mediaType === "audio") {
          // Add user to remote users array if not already present (in case audio comes first)
          const userIndex = this.remoteUsers.findIndex(u => u.uid === user.uid);

          if (userIndex === -1) {
            this.remoteUsers.push(user);
            console.log(`Added user ${user.uid} to remote users array from audio event. Total: ${this.remoteUsers.length}`);
          } else {
            // Update existing user reference
            this.remoteUsers[userIndex] = user;
          }

          // Play the remote audio track immediately
          if (user.audioTrack) {
            user.audioTrack.play();
            console.log(`Playing remote audio for user ${user.uid}`);
          }
        }
      } catch (err) {
        console.error(`Error handling remote user ${user.uid} publishing ${mediaType}:`, err);
      }
    });

    // Handle remote user unpublishing tracks
    this.client.on("user-unpublished", (user, mediaType) => {
      console.log(`Remote user ${user.uid} unpublished ${mediaType} track`);

      if (mediaType === "video") {
        // Update our video availability map
        this.videoAvailabilityMap.set(user.uid, false);
        console.log(`Updated video availability for user ${user.uid} to false`);

        // Note: We don't remove the user from remoteUsers array here anymore
        // because they might still have audio track published
        // Instead, we'll just update our tracking map
      }
    });

    // Handle remote user leaving
    this.client.on("user-left", (user) => {
      console.log(`Remote user ${user.uid} left the channel`);

      // Remove user from remote users array
      const initialLength = this.remoteUsers.length;
      this.remoteUsers = this.remoteUsers.filter(u => u.uid !== user.uid);
      console.log(`User ${user.uid} left. Removed from remote users. Before: ${initialLength}, After: ${this.remoteUsers.length}`);

      // Clean up our video availability map
      this.videoAvailabilityMap.delete(user.uid);
      console.log(`Removed user ${user.uid} from video availability map`);
    });

    // Handle connection state changes
    this.client.on("connection-state-change", (curState) => {
      console.log(`RTC connection state changed to ${curState}`);
    });
  }

  /**
   * Join a video call channel
   */
  async joinChannel(channelName, token) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      // Check if already connected to a channel
      if (this.client && this.client.connectionState === "CONNECTED") {
        console.log(`Already connected to a channel. Returning existing tracks.`);
        return {
          localAudioTrack: this.localAudioTrack,
          localVideoTrack: this.localVideoTrack,
        };
      }

      // Check if in connecting state
      if (this.client && this.client.connectionState === "CONNECTING") {
        console.log(`Client is currently connecting to a channel. Waiting for connection...`);
        // Wait for connection to complete or timeout after 5 seconds
        await new Promise((resolve, reject) => {
          const timeout = setTimeout(() => {
            reject(new Error("Connection timeout"));
          }, 5000);

          const connectionStateCallback = (curState) => {
            if (curState === "CONNECTED") {
              clearTimeout(timeout);
              this.client.off("connection-state-change", connectionStateCallback);
              resolve();
            } else if (curState === "DISCONNECTED") {
              clearTimeout(timeout);
              this.client.off("connection-state-change", connectionStateCallback);
              reject(new Error("Connection failed"));
            }
          };

          this.client.on("connection-state-change", connectionStateCallback);
        });

        return {
          localAudioTrack: this.localAudioTrack,
          localVideoTrack: this.localVideoTrack,
        };
      }

      // Join the channel
      await this.client.join("4970bcf832df4acfa9a191eb5cbfcd7d", channelName, token || null, this.userId);
      this.currentChannelName = channelName; // Track current channel for token renewal
      console.log(`Joined channel ${channelName} successfully`);

      // Create and publish local tracks with improved quality and stability
      if (!this.localAudioTrack || !this.localVideoTrack) {
        try {
          // Create audio track first with optimized settings
          if (!this.localAudioTrack) {
            this.localAudioTrack = await AgoraRTC.createMicrophoneAudioTrack({
              AEC: true, // Echo cancellation
              AGC: true, // Auto gain control
              ANS: true, // Automatic noise suppression
              encoderConfig: "music_standard" // Higher quality audio
            });
            console.log("Created optimized audio track");
          }

          // Create video track separately with optimized settings
          if (!this.localVideoTrack) {
            this.localVideoTrack = await AgoraRTC.createCameraVideoTrack({
              // Detailed encoder configuration for better control over video quality
              encoderConfig: {
                width: { ideal: 640, min: 320, max: 1280 },
                height: { ideal: 480, min: 240, max: 720 },
                frameRate: { ideal: 24, min: 15, max: 30 },
                bitrateMax: 1000, // kbps
                bitrateMin: 400,  // kbps
              },
              facingMode: "user",
              optimizationMode: "balanced", // Balance between quality and performance
              cameraId: undefined // Let the browser choose the best camera
            });
            console.log("Created optimized video track");
          }
        } catch (trackErr) {
          console.error("Error creating local tracks:", trackErr);
          // Fallback to combined creation if separate creation fails
          [this.localAudioTrack, this.localVideoTrack] = await AgoraRTC.createMicrophoneAndCameraTracks(
            {
              AEC: true,
              AGC: true,
              ANS: true,
            },
            {
              // Detailed encoder configuration for better control over video quality
              encoderConfig: {
                width: { ideal: 640, min: 320, max: 1280 },
                height: { ideal: 480, min: 240, max: 720 },
                frameRate: { ideal: 24, min: 15, max: 30 },
                bitrateMax: 1000, // kbps
                bitrateMin: 400,  // kbps
              },
              facingMode: "user",
              optimizationMode: "balanced"
            }
          );
          console.log("Created fallback tracks");
        }
      }

      // Set additional properties for better video quality
      if (this.localVideoTrack) {
        // Set video enhancement options if available
        if (this.localVideoTrack.setBeautyEffect) {
          try {
            await this.localVideoTrack.setBeautyEffect(true, {
              smoothnessLevel: 0.5,
              lighteningLevel: 0.3,
            });
          } catch (err) {
            console.warn("Beauty effect not supported:", err);
          }
        }
      }

      // Publish local tracks if not already published
      if (this.localAudioTrack && this.localVideoTrack) {
        try {
          await this.client.publish([this.localAudioTrack, this.localVideoTrack]);
          console.log("Published local tracks successfully");
        } catch (publishError) {
          console.error(`Error publishing tracks: ${publishError}`);
          // If tracks are already published, this is not a critical error
          if (!publishError.toString().includes("already published")) {
            throw publishError;
          }
        }
      }

      return {
        localAudioTrack: this.localAudioTrack,
        localVideoTrack: this.localVideoTrack,
      };
    } catch (error) {
      console.error(`Error joining channel: ${error}`);
      throw error;
    }
  }

  /**
   * Renew RTC token during active call
   */
  async renewToken(newToken) {
    try {
      if (!this.client || !this.currentChannelName) {
        console.warn("Cannot renew token: client not initialized or no active channel");
        return false;
      }

      console.log(`Renewing RTC token for channel: ${this.currentChannelName}`);

      // Use Agora's renewToken method to update the token without disconnecting
      await this.client.renewToken(newToken);
      this.rtcToken = newToken;

      console.log("RTC token renewed successfully during active video call");
      return true;
    } catch (error) {
      console.error("Error renewing RTC token during active video call:", error);
      return false;
    }
  }

  /**
   * Leave the video call channel and clean up resources
   */
  async leaveChannel() {
    try {
      console.log("Leaving video call channel and cleaning up resources");

      // Stop and clean up remote tracks first
      if (this.remoteUsers.length > 0) {
        console.log(`Cleaning up ${this.remoteUsers.length} remote users`);

        // Stop all remote tracks that might be playing
        this.remoteUsers.forEach(user => {
          try {
            if (user.videoTrack) {
              if (user.videoTrack.isPlaying) {
                user.videoTrack.stop();
              }
              console.log(`Stopped remote video track for user ${user.uid}`);
            }

            if (user.audioTrack) {
              if (user.audioTrack.isPlaying) {
                user.audioTrack.stop();
              }
              console.log(`Stopped remote audio track for user ${user.uid}`);
            }
          } catch (err) {
            console.warn(`Error cleaning up remote tracks for user ${user.uid}:`, err);
          }
        });
      }

      // Unpublish and close local tracks
      if (this.localAudioTrack) {
        try {
          this.localAudioTrack.close();
          console.log("Closed local audio track");
        } catch (err) {
          console.warn("Error closing local audio track:", err);
        }
        this.localAudioTrack = null;
      }

      if (this.localVideoTrack) {
        try {
          this.localVideoTrack.close();
          console.log("Closed local video track");
        } catch (err) {
          console.warn("Error closing local video track:", err);
        }
        this.localVideoTrack = null;
      }

      // Leave the channel if connected
      if (this.client && (this.client.connectionState === "CONNECTED" || this.client.connectionState === "CONNECTING")) {
        await this.client.leave();
        console.log("Left channel successfully");
      } else {
        console.log("Client not connected, no need to leave channel");
      }

      // Clear remote users array and video availability map
      this.remoteUsers = [];
      this.videoAvailabilityMap.clear();
      this.currentChannelName = null; // Clear channel tracking
      console.log("Cleared remote users array and video availability map");

    } catch (error) {
      console.error(`Error leaving channel: ${error}`);

      // Force cleanup even if there was an error
      this.localAudioTrack = null;
      this.localVideoTrack = null;
      this.remoteUsers = [];
      this.videoAvailabilityMap.clear();

      throw error;
    }
  }

  /**
   * Toggle local video on/off
   */
  async toggleVideo(enabled) {
    if (!this.localVideoTrack) return;

    try {
      await this.localVideoTrack.setEnabled(enabled);
      console.log(`Local video ${enabled ? 'enabled' : 'disabled'}`);
      return enabled;
    } catch (error) {
      console.error(`Error toggling video: ${error}`);
      throw error;
    }
  }

  /**
   * Toggle local audio on/off
   */
  async toggleAudio(enabled) {
    if (!this.localAudioTrack) return;

    try {
      await this.localAudioTrack.setEnabled(enabled);
      console.log(`Local audio ${enabled ? 'enabled' : 'disabled'}`);
      return enabled;
    } catch (error) {
      console.error(`Error toggling audio: ${error}`);
      throw error;
    }
  }

  /**
   * Check if a remote user has video available
   * @param {string} uid - The user ID to check
   * @returns {boolean} - Whether the user has video available
   */
  hasUserVideo(uid) {
    // First check our custom tracking map for best performance
    if (this.videoAvailabilityMap.has(uid)) {
      return this.videoAvailabilityMap.get(uid);
    }

    // If not in our map, try to find the user with the matching uid
    const user = this.remoteUsers.find(user => user.uid === uid);

    // If we found the user and they have a video track, update our map and return true
    if (user?.videoTrack) {
      this.videoAvailabilityMap.set(uid, true);
      return true;
    }

    // No video available
    return false;
  }

  /**
   * Clean up resources
   */
  async cleanup() {
    try {
      console.log(`Cleaning up VideoCallService instance: ${this.instanceId}`);

      // Leave channel if still connected
      if (this.client && this.client.connectionState !== "DISCONNECTED") {
        await this.leaveChannel();
      }

      // Remove from static registry
      const instanceKey = `${this.userId}_${this.rtcToken?.substring(0, 10)}`;
      if (activeVideoCallServices[instanceKey] === this) {
        delete activeVideoCallServices[instanceKey];
        console.log(`Removed VideoCallService instance from registry: ${this.instanceId}`);
      }

      this.isInitialized = false;
    } catch (error) {
      console.error(`Error cleaning up VideoCallService: ${error}`);
    }
  }
}
