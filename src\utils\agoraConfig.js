/**
 * Agora configuration utilities
 * This file contains helper functions for Agora RTC and RTM integration
 */

// Agora App ID - should be stored in environment variables in production
export const AGORA_APP_ID = "4970bcf832df4acfa9a191eb5cbfcd7d"

/**
 * Generate a unique, Agora-compliant channel name for one-to-one calls
 *
 * Requirements:
 * - Must be under 64 bytes in length
 * - Can only contain: a-z, A-Z, 0-9, space, !, #, $, %, &, (, ), +, -, :, ;, <, =, ., >, ?, @, [, ], ^, _, {, }, |, ~, ,
 * - Must be unique for each call session
 * - Must be consistent between caller and recipient
 *
 * @param {string} userId1 - First user ID
 * @param {string} userId2 - Second user ID
 * @param {string} callType - Type of call ('voice' or 'video')
 * @returns {string} - Agora-compliant channel name under 64 bytes
 */
export const generateChannelName = (userId1, userId2, callType = 'voice') => {
  // Create a consistent ordering of user IDs to ensure same channel name for both participants
  const sortedIds = [userId1, userId2].sort((a, b) => a.localeCompare(b));
  const user1 = sortedIds[0];
  const user2 = sortedIds[1];

  // Create a hash-like short identifier from the user IDs
  // Using a simple hash function to create shorter, consistent identifiers
  const createShortHash = (str) => {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    // Convert to base36 and take first 6 characters for compactness
    return Math.abs(hash).toString(36).substring(0, 6);
  };

  const hash1 = createShortHash(user1);
  const hash2 = createShortHash(user2);

  // Create timestamp-based unique suffix (shorter than full timestamp)
  // Use base36 to make it more compact
  const timestamp = Date.now();
  const timeHash = timestamp.toString(36);

  // Construct channel name with format: [type]_[hash1]_[hash2]_[timeHash]
  const prefix = callType === 'video' ? 'v' : 'a'; // Single character prefix
  const channelName = `${prefix}_${hash1}_${hash2}_${timeHash}`;

  // Ensure the channel name is under 64 bytes
  if (channelName.length >= 64) {
    // If still too long, truncate the timestamp hash
    const maxTimeHashLength = 63 - prefix.length - hash1.length - hash2.length - 3; // 3 for underscores
    const truncatedTimeHash = timeHash.substring(0, Math.max(1, maxTimeHashLength));
    return `${prefix}_${hash1}_${hash2}_${truncatedTimeHash}`;
  }

  return channelName;
};

/**
 * Validate if a channel name meets Agora requirements
 * @param {string} channelName - Channel name to validate
 * @returns {object} - Validation result with isValid boolean and error message if invalid
 */
export const validateChannelName = (channelName) => {
  // Check length
  if (!channelName || channelName.length === 0) {
    return { isValid: false, error: 'Channel name cannot be empty' };
  }

  if (channelName.length >= 64) {
    return { isValid: false, error: `Channel name too long: ${channelName.length} bytes (max 64)` };
  }

  // Check for valid characters only
  // Agora allows: a-z, A-Z, 0-9, space, !, #, $, %, &, (, ), +, -, :, ;, <, =, ., >, ?, @, [, ], ^, _, {, }, |, ~, ,
  const validCharRegex = /^[a-zA-Z0-9 !#$%&()+\-:;<=>?@[\]^_{|}~,]*$/;

  if (!validCharRegex.test(channelName)) {
    return { isValid: false, error: 'Channel name contains invalid characters' };
  }

  return { isValid: true };
};

/**
 * Generate a unique call ID
 * @returns {string} - Unique call ID
 */
export const generateCallId = () => {
  return `call_${Date.now()}_${Math.floor(Math.random() * 1000)}`
}

/**
 * Check if browser supports required audio features
 * @returns {boolean} - Whether browser supports required features
 */
export const checkBrowserSupport = () => {
  return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia && window.RTCPeerConnection)
}

/**
 * Request microphone permissions
 * @returns {Promise<boolean>} - Whether permissions were granted
 */
export const requestMicrophonePermission = async () => {
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true })

    // Clean up the stream after checking permissions
    stream.getTracks().forEach((track) => track.stop())

    return true
  } catch (error) {
    console.error("Error requesting microphone permission:", error)
    return false
  }
}

/**
 * Format call duration in MM:SS format
 * @param {number} seconds - Duration in seconds
 * @returns {string} - Formatted duration
 */
export const formatCallDuration = (seconds) => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`
}
