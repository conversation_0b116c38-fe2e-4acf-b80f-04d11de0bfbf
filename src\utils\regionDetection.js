/**
 * Region Detection Utility
 * This utility provides functions to detect user's country/region code
 */

/**
 * Get user's region/country code using browser APIs and fallback methods
 */
export const detectUserRegion = async () => {
  try {
    // Method 1: Try to get from browser locale
    const browserRegion = getBrowserRegion();
    if (browserRegion) {
      console.log('Region detected from browser locale:', browserRegion);
      return browserRegion;
    }

    // Method 2: Try to get from timezone
    const timezoneRegion = getRegionFromTimezone();
    if (timezoneRegion) {
      console.log('Region detected from timezone:', timezoneRegion);
      return timezoneRegion;
    }

    // Method 3: Try to get from IP geolocation (fallback)
    const ipRegion = await getRegionFromIP();
    if (ipRegion) {
      console.log('Region detected from IP geolocation:', ipRegion);
      return ipRegion;
    }

    // Default fallback
    console.log('Could not detect region, using default: US');
    return 'US';
  } catch (error) {
    console.error('Error detecting user region:', error);
    return 'US'; // Default fallback
  }
};

/**
 * Get region from browser locale
 */
const getBrowserRegion = () => {
  try {
    // Try navigator.language first
    if (navigator.language) {
      const locale = navigator.language;
      const parts = locale.split('-');
      if (parts.length > 1) {
        return parts[1].toUpperCase();
      }
    }

    // Try navigator.languages array
    if (navigator.languages && navigator.languages.length > 0) {
      for (const lang of navigator.languages) {
        const parts = lang.split('-');
        if (parts.length > 1) {
          return parts[1].toUpperCase();
        }
      }
    }

    // Try Intl.Locale if available
    if (typeof Intl !== 'undefined' && Intl.Locale) {
      const locale = new Intl.Locale(navigator.language);
      if (locale.region) {
        return locale.region.toUpperCase();
      }
    }

    return null;
  } catch (error) {
    console.error('Error getting region from browser locale:', error);
    return null;
  }
};

/**
 * Get region from timezone
 */
const getRegionFromTimezone = () => {
  try {
    if (typeof Intl !== 'undefined' && Intl.DateTimeFormat) {
      const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      
      // Map common timezones to regions
      const timezoneToRegion = {
        // North America
        'America/New_York': 'US',
        'America/Chicago': 'US',
        'America/Denver': 'US',
        'America/Los_Angeles': 'US',
        'America/Toronto': 'CA',
        'America/Vancouver': 'CA',
        'America/Mexico_City': 'MX',
        
        // Europe
        'Europe/London': 'GB',
        'Europe/Paris': 'FR',
        'Europe/Berlin': 'DE',
        'Europe/Rome': 'IT',
        'Europe/Madrid': 'ES',
        'Europe/Amsterdam': 'NL',
        'Europe/Stockholm': 'SE',
        'Europe/Oslo': 'NO',
        'Europe/Copenhagen': 'DK',
        'Europe/Helsinki': 'FI',
        'Europe/Warsaw': 'PL',
        'Europe/Prague': 'CZ',
        'Europe/Vienna': 'AT',
        'Europe/Zurich': 'CH',
        'Europe/Brussels': 'BE',
        'Europe/Dublin': 'IE',
        'Europe/Lisbon': 'PT',
        'Europe/Athens': 'GR',
        'Europe/Moscow': 'RU',
        
        // Asia Pacific
        'Asia/Tokyo': 'JP',
        'Asia/Seoul': 'KR',
        'Asia/Shanghai': 'CN',
        'Asia/Hong_Kong': 'HK',
        'Asia/Singapore': 'SG',
        'Asia/Bangkok': 'TH',
        'Asia/Jakarta': 'ID',
        'Asia/Manila': 'PH',
        'Asia/Kuala_Lumpur': 'MY',
        'Asia/Ho_Chi_Minh': 'VN',
        'Asia/Kolkata': 'IN',
        'Asia/Dubai': 'AE',
        'Asia/Riyadh': 'SA',
        'Asia/Tel_Aviv': 'IL',
        'Australia/Sydney': 'AU',
        'Australia/Melbourne': 'AU',
        'Australia/Perth': 'AU',
        'Pacific/Auckland': 'NZ',
        
        // South America
        'America/Sao_Paulo': 'BR',
        'America/Buenos_Aires': 'AR',
        'America/Santiago': 'CL',
        'America/Lima': 'PE',
        'America/Bogota': 'CO',
        'America/Caracas': 'VE',
        
        // Africa
        'Africa/Cairo': 'EG',
        'Africa/Lagos': 'NG',
        'Africa/Johannesburg': 'ZA',
        'Africa/Casablanca': 'MA',
        'Africa/Nairobi': 'KE'
      };
      
      if (timezoneToRegion[timezone]) {
        return timezoneToRegion[timezone];
      }
      
      // Try to extract region from timezone string
      const parts = timezone.split('/');
      if (parts.length > 1) {
        const region = parts[0];
        const regionMap = {
          'America': 'US',
          'Europe': 'GB',
          'Asia': 'SG',
          'Australia': 'AU',
          'Pacific': 'US',
          'Africa': 'ZA'
        };
        return regionMap[region] || null;
      }
    }
    
    return null;
  } catch (error) {
    console.error('Error getting region from timezone:', error);
    return null;
  }
};

/**
 * Get region from IP geolocation (fallback method)
 */
const getRegionFromIP = async () => {
  try {
    // Use a free IP geolocation service
    const response = await fetch('https://ipapi.co/json/', {
      method: 'GET',
      headers: {
        'Accept': 'application/json'
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      if (data.country_code) {
        return data.country_code.toUpperCase();
      }
    }
    
    return null;
  } catch (error) {
    console.error('Error getting region from IP:', error);
    return null;
  }
};

/**
 * Validate region code format
 */
export const validateRegionCode = (regionCode) => {
  if (!regionCode || typeof regionCode !== 'string') {
    return false;
  }
  
  // Check if it's a valid 2-letter country code
  return /^[A-Z]{2}$/.test(regionCode);
};

/**
 * Get region display name
 */
export const getRegionDisplayName = (regionCode) => {
  const regionNames = {
    'US': 'United States',
    'CA': 'Canada',
    'GB': 'United Kingdom',
    'FR': 'France',
    'DE': 'Germany',
    'IT': 'Italy',
    'ES': 'Spain',
    'NL': 'Netherlands',
    'SE': 'Sweden',
    'NO': 'Norway',
    'DK': 'Denmark',
    'FI': 'Finland',
    'PL': 'Poland',
    'CZ': 'Czech Republic',
    'AT': 'Austria',
    'CH': 'Switzerland',
    'BE': 'Belgium',
    'IE': 'Ireland',
    'PT': 'Portugal',
    'GR': 'Greece',
    'RU': 'Russia',
    'JP': 'Japan',
    'KR': 'South Korea',
    'CN': 'China',
    'HK': 'Hong Kong',
    'SG': 'Singapore',
    'TH': 'Thailand',
    'ID': 'Indonesia',
    'PH': 'Philippines',
    'MY': 'Malaysia',
    'VN': 'Vietnam',
    'IN': 'India',
    'AE': 'United Arab Emirates',
    'SA': 'Saudi Arabia',
    'IL': 'Israel',
    'AU': 'Australia',
    'NZ': 'New Zealand',
    'BR': 'Brazil',
    'AR': 'Argentina',
    'CL': 'Chile',
    'PE': 'Peru',
    'CO': 'Colombia',
    'VE': 'Venezuela',
    'EG': 'Egypt',
    'NG': 'Nigeria',
    'ZA': 'South Africa',
    'MA': 'Morocco',
    'KE': 'Kenya',
    'MX': 'Mexico'
  };
  
  return regionNames[regionCode] || regionCode;
};
